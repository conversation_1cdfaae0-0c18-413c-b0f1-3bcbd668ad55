<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.indexpage.dao.CountByAllMapper">

 <select id="selectLineNumberByOne" resultType="int">
  USE Android_App
  SELECT COUNT(DISTINCT line) AS lineNumber
  FROM [Android_App].[dbo].[Six_TD_DJXX]
  WHERE DATE_SCRQ >= #{date}
  AND DATE_SCRQ &lt; #{date1}
  AND SQR_WorkId = #{workId}
  GROUP BY SQR_WorkId
 </select>

 <select id="selectLineNumberBySection" resultType="int">
  USE Android_App
  SELECT COUNT(DISTINCT line) AS lineNumber
  FROM [Android_App].[dbo].[Six_TD_DJXX]
  WHERE DATE_SCRQ >= #{date}
  AND DATE_SCRQ &lt; #{date1}
  AND section = #{section1}
  GROUP BY section
 </select>

 <select id="selectDayNumberByOne" resultType="int">
  USE IDS_BK_PaperLessSystem
  SELECT COUNT(ProductionLine) as lineNumber
  FROM [T_Configure_ProductionDay_lock]
  WHERE DOM =#{date} AND cardId =#{workId}
 </select>

 <select id="selectDayNumberBySection" resultType="int">
  USE IDS_BK_PaperLessSystem
  SELECT COUNT(ProductionLine) as lineNumber
  FROM [T_Configure_ProductionDay_lock]
  WHERE DOM =#{date} AND Industry =#{pl} AND Discern=#{section}
 </select>

 <select id="selectLineSubByOne" resultType="int">
  USE Schedule
  SELECT  COUNT(ProductionLine) AS lineNumber  FROM [Schedule].[dbo].[configure_connect_lock]
  WHERE DateTime >=#{date} AND DateTime &lt;#{date1} AND PartNumber=#{workId}
 </select>

 <select id="selectLineSubBySection" resultType="int">
  USE Schedule
  SELECT  COUNT(ProductionLine) AS lineNumber  FROM [Schedule].[dbo].[configure_connect_lock]
  WHERE DateTime >=#{date} AND DateTime &lt;#{date1} AND Industry=#{pl} AND Discern=#{section}
 </select>

 <select id="select136ByOne" resultType="int">
  USE Schedule
  SELECT COUNT(productionLine) AS lineNumber
  FROM [Schedule].[dbo].[t_data_comparison]
  WHERE recordDate=#{date} AND spotChecker=#{name}
 </select>

 <select id="select136BySection" resultType="int">
  USE Schedule
  SELECT COUNT(productionLine) AS lineNumber
  FROM [Schedule].[dbo].[t_data_comparison]
  WHERE recordDate=#{date} AND industry=#{pl} AND enginery=#{section}
 </select>

 <select id="selectPerms" resultType="java.lang.String">
  IF EXISTS (
  SELECT 1
  FROM [IDS_BK_PaperLessSystem].[dbo].[t_config_dataPerms]
  WHERE workId=#{cardId}
  )
  SELECT perms
  FROM [IDS_BK_PaperLessSystem].[dbo].[t_config_dataPerms]
  WHERE workId=#{cardId}
  ELSE
  SELECT '0' AS perms
 </select>
</mapper>