package cc.mrbird.febs.newo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Six_TD_DJXX")
public class TDDJXXDto {
    @ApiModelProperty(value = "計劃產量")
    @TableField(value = "SCSL_QOP")
    private BigDecimal SCSL_QOP;
//    @ApiModelProperty(value = "实际产量")
//    private BigDecimal SCSL_QOP_REAL;
    @ApiModelProperty(value = "成品料號")
    @TableField(value = "CPLH_PFN")
    private String CPLH_PFN;
    @ApiModelProperty(value = "客戶名稱")
    @TableField(value = "KHMC_CN")
    private String KHMC_CN;
    @ApiModelProperty(value = "客戶料號")
    @TableField(value = "KHMC_CN")
    private String KHLH_CMN;
    @ApiModelProperty(value = "工單")
    @TableField(value = "GLBH_WON")
    private String GLBH_WON;
    @ApiModelProperty(value = "dc")
    @TableField(value = "DC_SCDC")
    private String DC_SCDC;
    @TableField(value = "RUKUDANHAO")
    @ApiModelProperty(value = "入庫單號")
    private String RUKUDANHAO;
//    @ApiModelProperty(value = "備注")
//    private String REMARKS;


}
