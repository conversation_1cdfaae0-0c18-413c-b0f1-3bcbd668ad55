package cc.mrbird.febs.line.entity;

import cc.mrbird.febs.jjkpi.entity.DjxxDto;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigureConnectDto implements Serializable {
    @ApiModelProperty("綫長交接表")
    private ConfigureConnect configureConnect;
    @ApiModelProperty("kpi表")
    private List<TConfigureClasskpi> classkpiList;
}
