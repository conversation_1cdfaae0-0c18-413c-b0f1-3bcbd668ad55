package cc.mrbird.febs.sparepart.service.Impl;

import cc.mrbird.febs.backwork.service.Email;
import cc.mrbird.febs.sparepart.dao.SparePartByDataMapper;
import cc.mrbird.febs.sparepart.dao.YieldMapper;
import cc.mrbird.febs.sparepart.entity.dto.EquipmentDataBy123;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureYieldOperation;
import cc.mrbird.febs.sparepart.entity.qo.SparePartAndEquipmentQo;
import cc.mrbird.febs.sparepart.entity.qo.UpdateSparePartQo;
import cc.mrbird.febs.sparepart.service.IEquipmentDataService;
import cc.mrbird.febs.sparepart.util.HtmlCodeBySparePart;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class IEquipmentDataImpl implements IEquipmentDataService {
    private final YieldMapper yieldMapper;
    private final SparePartByDataMapper sparePartByDataMapper;

    private final Email email;
    HtmlCodeBySparePart htmlCodeBySparePart = new HtmlCodeBySparePart();

    public IEquipmentDataImpl(YieldMapper yieldMapper, SparePartByDataMapper sparePartByDataMapper, Email email) {
        this.yieldMapper = yieldMapper;
        this.sparePartByDataMapper = sparePartByDataMapper;
        this.email = email;
    }

    @Override
    //@Scheduled(cron = "0 */1 * * * ?")
    public void scheduleUpdate() {
        yieldMapper.UpdateMethodBy123();
    }

    //获取>=90%的备品名称  並且未郵件推送
   //@Scheduled(cron = "0 */1 * * * ?")
    @DSTransactional
    public void getSparePart() throws UnsupportedEncodingException {
        List<EquipmentDataBy123> list = yieldMapper.getSharePartIn123();
        //判断备件同一时间内预警负责人是同一个人
        String toEmails = "";
        String ccEmails = "<EMAIL>";
        String subject = "設備備件壽命到期預警提示";
        subject = URLEncoder.encode(subject, "utf-8");
        String finalSubject = subject;
        list.forEach(dd -> {
            if ("0".equals(dd.getIsSendEmail())) {
                // 假设每个dd条目只发送一封邮件
                try {
                    String content = htmlCodeBySparePart.getCode(
                            dd.getProduction(),
                            dd.getProductionLine(),
                            dd.getEquipmentName(),
                            dd.getSparePartName(),
                            dd.getLifeLimit(),
                            dd.getActualOutput(),
                            dd.getPersonInChargeNameBy02(),
                            dd.getEquipmentUpdateTime()
                    );
                    email.sendEmailBySparePart(dd.getPersonInChargeEmailBy02(), ccEmails, finalSubject, content);
                    yieldMapper.UpdateByEmailSendState(dd.getId(), "1");
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } else if ("1".equals(dd.getIsSendEmail()) && compareWithCurrentTime(dd.getEquipmentUpdateTime()) == true) {
                try {
                    String content = htmlCodeBySparePart.getCode(
                            dd.getProduction(),
                            dd.getProductionLine(),
                            dd.getEquipmentName(),
                            dd.getSparePartName(),
                            dd.getLifeLimit(),
                            dd.getActualOutput(),
                            dd.getPersonInChargeNameBy02(),
                            dd.getEquipmentUpdateTime()
                    );
                    email.sendEmailBySparePart(dd.getPersonInChargeEmailBy02(), ccEmails, finalSubject, content);
                    yieldMapper.UpdateByEmailSendState(dd.getId(), "2");
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } else if ("2".equals(dd.getIsSendEmail()) && compareWithCurrentTime(dd.getEquipmentUpdateTime()) == true) {
                try {
                    String content = htmlCodeBySparePart.getCode(
                            dd.getProduction(),
                            dd.getProductionLine(),
                            dd.getEquipmentName(),
                            dd.getSparePartName(),
                            dd.getLifeLimit(),
                            dd.getActualOutput(),
                            dd.getPersonInChargeNameByMax(),
                            dd.getEquipmentUpdateTime()
                    );
                    email.sendEmailBySparePart(dd.getPersonInChargeEmailByMax(), ccEmails, finalSubject, content);
                    yieldMapper.UpdateByEmailSendState(dd.getId(), "3");
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }
    //第一次预警发送邮件
    //備品數據展示
    public List<TConfigureEquipmentAndYield> querySparePartDataByView(SparePartAndEquipmentQo sparePartAndEquipmentQo) {
        List<TConfigureEquipmentAndYield> list = yieldMapper.querySparePartAndView(sparePartAndEquipmentQo);
        return list;
    }

    @Override
    public int updateSparePart(UpdateSparePartQo updateSparePartQo) {
        int a = yieldMapper.updateSparePart(updateSparePartQo);
        int b = yieldMapper.insertSparePart(updateSparePartQo);
        return a + b;
    }

    @Override
    public List<TConfigureYieldOperation> queryYieldOperation(Integer id) {
        List<TConfigureYieldOperation> list = yieldMapper.queryYieldOperation(id);
        return list;
    }

    @Override
    public TConfigureEquipmentAndYield selectTConfigureEquipmentAndYield(Integer id) {
        return yieldMapper.selectTConfigureEquipmentAndYieldById(id);
    }

    public static boolean compareWithCurrentTime(String inputTime) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            Date inputDate = sdf.parse(inputTime);
            Date currentDate = new Date();

            long diffInMillies = Math.abs(currentDate.getTime() - inputDate.getTime());
            long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);

            return diffInDays >= 1;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

  //@Scheduled(cron = "0 */1 * * * ?")
    public void autoSumYieldByMin(){
        List<TConfigureEquipmentAndYield> list =yieldMapper.autoSumYieldByMin();
        list.forEach(dd->{
            Integer id =dd.getId();
            String classData=dd.getEquipmentUpdateTime();
            String eqId=dd.getEqid();
            int yield=       yieldMapper.sumYieldByAuto(classData,eqId);
            yieldMapper.updateById(id,yield);
        });
    }






}






