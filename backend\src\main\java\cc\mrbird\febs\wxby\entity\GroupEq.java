package cc.mrbird.febs.wxby.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("T_Data_Group_Eq")
public class GroupEq {
    @TableField(value = "GroupName")
    private String GroupName ;
    @TableField(value = "ProductRange")
    private String ProductRange;
    @TableField(value = "ProductRangeCode")
    private String ProductRangeCode;
    @TableField(value = "ProductionLineCode")
    private String ProductionLineCode;
    @TableField(value = "ProductionLine")
    private String ProductionLine ;
    @TableField(value = "L_Sequence")
    private String LSequence;
    @TableField(value = "Equipment_id")
    private String EquipmentId;
    @TableField(value = "Equipment_Name")
    private String EquipmentName;
    @TableField(value = "Eq_sequence")
    private String EqSequence;
}
