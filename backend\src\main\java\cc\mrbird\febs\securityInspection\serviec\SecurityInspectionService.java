package cc.mrbird.febs.securityInspection.serviec;

import cc.mrbird.febs.securityInspection.entity.*;

import java.util.List;

public interface SecurityInspectionService {

    /**
     * 查询巡检时间
     * @return
     */
    List<String> queryDate();

    /**
     * 查询厂区
     * @return
     */
    List<String> queryFactory();

    /**
     * 查询楼栋
     * @return
     */
    List<String> queryBuilding();

    /**
     * 查询楼层
     * @return
     */
    List<String> queryFloor();

    /**
     * 查询主要信息
     * @return
     */
    List<SecurityInspectionExportExcelAllPoint> queryInfo(QueryCondition queryCondition);


    /**
     * 查询樓層信息
     * @return
     */
    List<SecurityInspectionExportExcelFloor> queryInfoByFloor(QueryCondition queryCondition);

    List<InspectionStationReport> groupByLocation(List<InspectionStationRaw> list);
}
