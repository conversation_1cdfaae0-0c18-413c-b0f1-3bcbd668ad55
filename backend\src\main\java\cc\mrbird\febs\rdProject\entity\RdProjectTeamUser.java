package cc.mrbird.febs.rdProject.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的團隊與個人的映射實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectTeamUser {

    @ApiModelProperty(value = "工号（关联user_extra）", position = 0)
    private String workerId;

    @ApiModelProperty(value = "团队ID（含team_id=0）", position = 1)
    private Long teamId;
}
