package cc.mrbird.febs.comparation136.VO;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class TypePerson implements Serializable {

    @ApiModelProperty(value ="駁回人工號")
    private String WorkId;

    @ApiModelProperty(value = "駁回人名字")
    private String name;

    @ApiModelProperty(value = "駁回人類型")
    private String Type;

    @ApiModelProperty(value = "退单原因")
    private String description;
}
