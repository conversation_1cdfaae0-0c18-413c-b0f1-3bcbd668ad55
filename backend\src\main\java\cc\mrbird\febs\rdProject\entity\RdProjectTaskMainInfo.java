package cc.mrbird.febs.rdProject.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的任務信息（樹形）實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectTaskMainInfo {

    @ApiModelProperty(value = "任务ID", position = 0)
    private Long taskId;

    @ApiModelProperty(value = "父任务ID（0表示根）", position = 1)
    private Long parentId;

    @ApiModelProperty(value = "任务树路径（如1,3,5）", position = 2)
    private String treePath;

    @ApiModelProperty(value = "厂区", position = 3)
    private String siteArea;

    @ApiModelProperty(value = "产品处", position = 4)
    private String sbu;

    @ApiModelProperty(value = "机能", position = 5)
    private String functions;

    @ApiModelProperty(value = "课别", position = 6)
    private String section;

    @ApiModelProperty(value = "任务名称", position = 7)
    private String taskName;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间",example = "2024-03-13", position = 8)
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "原定完成时间",example = "2024-03-13", position = 9)
    private Date endTime;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "调整后的完成时间",example = "2024-03-13", position = 10)
    private Date nextEndTime;

    @ApiModelProperty(value = "状态（0待执行,1进行中,2已完成,3已延期）", position = 11)
    private Integer status;

    @ApiModelProperty(value = "创建人工号（非user_id）", position = 12)
    private String creatorWorkerId;
}
