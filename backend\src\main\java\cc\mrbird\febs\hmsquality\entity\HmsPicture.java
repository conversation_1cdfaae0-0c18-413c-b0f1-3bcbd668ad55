package cc.mrbird.febs.hmsquality.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:43
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class HmsPicture {

    @ApiModelProperty(value = "紅墨水圖片ID",position = 0)
    private Integer id;
    @ApiModelProperty(value = "紅墨水品質記錄ID",position = 1)
    private Integer pzId;
    @ApiModelProperty(value = "紅墨水圖片url",position = 2)
    private String picUrl;
    @ApiModelProperty(value = "紅墨水圖片名",position = 3)
    private String picName;
    @ApiModelProperty(value = "紅墨水圖片排序",position = 4)
    private String picOrder;
}
