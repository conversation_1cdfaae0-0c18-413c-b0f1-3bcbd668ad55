package cc.mrbird.febs.agv.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Author: 李囯斌
 * @Date: 2025/2/25
 * @Time: 11:32
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class AgvQueryParam {
    @ApiModelProperty(value = "AGVId",position = 0)
    private Integer id;
    @ApiModelProperty(value = "厂区",example = "BK",position = 1)
    private String factory;
    @ApiModelProperty(value = "楼栋",example = "A3",position = 2)
    private String building;
    @ApiModelProperty(value = "机能",example = "成型",position = 3)
    private String functions;
    @ApiModelProperty(value = "机器人ID",example = "2",position = 4)
    private String robotId;
    @ApiModelProperty(value = "机器人名称",example = "成型工序2#AGV",position = 5)
    private String robotName;
}
