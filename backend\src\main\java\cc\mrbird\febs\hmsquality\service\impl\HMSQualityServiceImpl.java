package cc.mrbird.febs.hmsquality.service.impl;

import cc.mrbird.febs.hmsquality.dao.HMSQualityMapper;
import cc.mrbird.febs.hmsquality.entity.HmsPicture;
import cc.mrbird.febs.hmsquality.entity.dto.HMSQualityDto;
import cc.mrbird.febs.hmsquality.param.HMSQualityQueryParam;
import cc.mrbird.febs.hmsquality.service.HMSQualityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:15
 */
@Service
public class HMSQualityServiceImpl implements HMSQualityService {

    private final HMSQualityMapper emapper;

    @Autowired
    public HMSQualityServiceImpl(HMSQualityMapper emapper) {
        this.emapper = emapper;
    }


    //查詢紅墨水品質記錄
    @Override
    public List<HMSQualityDto> queryHMSQualityDataWithPic(HMSQualityQueryParam hmsQualityQueryParam) {
        //1、查詢出紅墨水品質記錄
        List<HMSQualityDto> list =emapper.selectHMSQualityData(hmsQualityQueryParam);
        //2、提取紅墨水品質記錄所有的ID
        List<Integer> ids = list.stream()
                .map(HMSQualityDto::getId)
                .collect(Collectors.toList());
        //3、批量查詢圖片信息
        Map<Integer, List<String>> picturesMap = new HashMap<>();
        if (!ids.isEmpty()) {
            List<HmsPicture> allPictures = this.selectHmsPics(ids);
            for (HmsPicture picture : allPictures) {
                picturesMap.computeIfAbsent(picture.getPzId(), k -> new ArrayList<>()).add(picture.getPicUrl()+picture.getPicName());
            }
        }
        // 4、將圖片信息設置到對應的HMSQualityDto中
        List<HMSQualityDto> resultList = list.stream()
                .peek(item -> item.setHmsPictureList(picturesMap.getOrDefault(item.getId(), Collections.emptyList())))
                .collect(Collectors.toList());
        resultList = resultList.stream().peek(item -> item.setSum(item.getHmsPictureList().size())).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<HmsPicture> selectHmsPics(List<Integer> ids) {
        if(ids==null||ids.size()==0){
            return Collections.emptyList();
        }
        return emapper.selectHmsPicsByIds(ids);
    }

    @Override
    public List<String> queryProduction() {
        return emapper.selectProduction();
    }

    @Override
    public List<String> queryProductionLine(String production) {
        return emapper.selectProductionLine(production);
    }

    @Override
    public List<String> queryDateCode(String production, String productionLine) {
        return emapper.selectDC(production,productionLine);
    }

    @Override
    public List<String> queryPFN(String production, String productionLine, String dateCode) {
        return emapper.selectPFN(production,productionLine,dateCode);
    }

    @Override
    public List<String> queryUser(String production, String productionLine, String dateCode, String pfn) {
        return emapper.selectUser(production,productionLine,dateCode,pfn);
    }
}
