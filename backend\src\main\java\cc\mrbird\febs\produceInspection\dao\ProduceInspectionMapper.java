package cc.mrbird.febs.produceInspection.dao;

import cc.mrbird.febs.produceInspection.entity.*;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionInspectProjectsQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionMainInfoQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionPartNumberQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionUserInfoQueryParam;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/19
 * @Time: 10:05
 */
@DS("one")
public interface ProduceInspectionMapper {
    List<ProduceInspectionUserInfo> queryByUserId(String userID);

    List<String> querySBU();

    List<String> queryFunctions(String sbu);

    List<String> queryDiscern(String sbu, String functions);

    List<String> queryProduction(String sbu, String functions, String discern);

    List<String> queryProductionLine(String sbu, String functions, String discern, String production);

    List<String> queryWorkStation(String sbu, String functions, String discern, String production, String line);

    ProduceInspectionNotInspect queryIsNotDJ(ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam);

    List<ProduceInspectionMainInfo> queryProductionInspectionMainInfo(ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam);

    List<ProduceInspectionInspectProjects> queryProductionInspectionProjectData(ProduceInspectionInspectProjectsQueryParam produceInspectionInspectProjectsQueryParam);

    List<ProduceInspectionPartNumber> queryProduceInspectionPartNumberData(ProduceInspectionPartNumberQueryParam produceInspectionPartNumberQueryParam);

    List<ProduceInspectionUserInfo> queryProduceInspectionUserInfoData(ProduceInspectionUserInfoQueryParam produceInspectionUserInfoQueryParam);

    int insertPersonnelData(ProduceInspectionUserInfo personnel);

    int insertInspectionItems(ProduceInspectionInspectProjects item);

    int insertPartNumber(ProduceInspectionPartNumber material);

    void saveProduceInspectionPicUrl(ProduceInspectionPicUrl picUrl);

    int getPicCount(Integer valueOf);

    int updatePartNumber(ProduceInspectionPartNumber partNumber);

    int deletePartNumber(Integer id);

    int updateUserInfo(ProduceInspectionUserInfo userInfo);
    int deleteUserInfo(Integer id);

    int updateInspectProject(ProduceInspectionInspectProjects project);
    int deleteInspectProject(Integer id);

    List<ProduceInspectionPicUrl> queryPicsByMainInfoId(String mainInfoId);


}
