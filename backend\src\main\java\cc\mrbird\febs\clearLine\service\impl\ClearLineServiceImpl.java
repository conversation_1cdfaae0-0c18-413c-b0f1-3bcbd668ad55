package cc.mrbird.febs.clearLine.service.impl;

import cc.mrbird.febs.clearLine.dao.ClearLineDataMapper;
import cc.mrbird.febs.clearLine.entity.AddSheet;
import cc.mrbird.febs.clearLine.entity.TDataClearLineCheckByData;
import cc.mrbird.febs.clearLine.entity.TDataClearLineUpdate;
import cc.mrbird.febs.clearLine.entity.pojo.CheckDtoByClearLine;
import cc.mrbird.febs.clearLine.service.ClearLineService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class ClearLineServiceImpl implements ClearLineService {
    @Autowired
    private ClearLineDataMapper clearLineDataMapper;
    @Override
    @DSTransactional()
    public int addSheet(AddSheet addSheet) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String uuid = "R" + sdf.format(new Date());
        addSheet.getTDataClearLine().setLinkId(uuid);
        addSheet.getTDataClearLineCheckItemByLine().setId(uuid);
        boolean c= clearLineDataMapper.addSheetByPointData(addSheet.getTDataClearLine());
        boolean b= clearLineDataMapper.addSheet(addSheet.getTDataClearLineCheckItemByLine());
        return 1;
    }

    @Override
    public String checkFunctionByPL2(CheckDtoByClearLine checkDto) {
       List<TDataClearLineCheckByData> list=   queryCheckData(checkDto);
       TDataClearLineUpdate tDataClearLineUpdate = new TDataClearLineUpdate();
       String id;
        id= checkDto.getId();
       String stNo;
       String qhStatus;
       String qh3;
       for (TDataClearLineCheckByData tDataClearLineCheckByData : list){
          stNo=tDataClearLineCheckByData.getStNo();
          qhStatus=tDataClearLineCheckByData.getQhStatus();
          qh3=tDataClearLineCheckByData.getQh3();
           //根据QH2、QH3设置showId
           String showIdByQh3 =queryShowIdByName(qh3);
           //签核流程   stNo =1,qhStatus=0    (1:签核 0:未签核)
           if (stNo.equals("1") && qhStatus.equals("0")){   //品保签核
               tDataClearLineUpdate.setQhStatus("0");
               tDataClearLineUpdate.setStNo("2");
               tDataClearLineUpdate.setShowId(showIdByQh3);
               tDataClearLineUpdate.setId(id);
               System.out.println("成功設定id::::+"+id);
               clearLineDataMapper.updateQhStatus(tDataClearLineUpdate);
           }else if (stNo.equals("2") && qhStatus.equals("0")){   //  课长/组长签核
               tDataClearLineUpdate.setQhStatus("0");
               tDataClearLineUpdate.setStNo("3");
               tDataClearLineUpdate.setId(id);
               tDataClearLineUpdate.setShowId("");
               clearLineDataMapper.updateQhStatus(tDataClearLineUpdate);
           }
       }
        return "签核成功";
    }
    //IDS1清、换线  直接线长签核
    @Override
    public String checkFunctionByPL1(CheckDtoByClearLine checkDto) {
        List<TDataClearLineCheckByData> list=   queryCheckData(checkDto);
        TDataClearLineUpdate tDataClearLineUpdate = new TDataClearLineUpdate();
        String id;
        id= checkDto.getId();
        String stNo;
        String qhStatus;
        String qh3;
        for (TDataClearLineCheckByData tDataClearLineCheckByData : list){
            stNo=tDataClearLineCheckByData.getStNo();
            qhStatus=tDataClearLineCheckByData.getQhStatus();
            qh3=tDataClearLineCheckByData.getQh3();
            //根据QH2、QH3设置showId
            String showIdByQh3 =queryShowIdByName(qh3);
            //签核流程   stNo =1,qhStatus=0    (1:签核 0:未签核)
            if (stNo.equals("1") && qhStatus.equals("0")){   //  课长/组长签核
                tDataClearLineUpdate.setQhStatus("0");
                tDataClearLineUpdate.setStNo("3");
                tDataClearLineUpdate.setId(id);
                tDataClearLineUpdate.setShowId("");
                clearLineDataMapper.updateQhStatus(tDataClearLineUpdate);
            }
        }
        return "签核成功";
    }

    //退签接口
    @Override
    @DSTransactional()
    public String rebackCheckFunction(AddSheet addSheet) {
        CheckDtoByClearLine checkDto =new CheckDtoByClearLine();
        String id =addSheet.getTDataClearLine().getId();
        checkDto.setId(id);
        String stNo=null;
        String qhStatus=null;
        String showIdByQH1=null;
        String showIdByQh2=null;
        List<TDataClearLineCheckByData> list =queryCheckData(checkDto);
        //先判断当前签核状态到那一步
        for (TDataClearLineCheckByData td :list){
            stNo=td.getStNo();
            qhStatus=td.getQhStatus();

            showIdByQh2=td.getQh2();
            if(stNo.equals("1") && qhStatus.equals("1")){
                //品保签核
                stNo="1";
                qhStatus="0";
                addSheet.getTDataClearLine().setShowId(showIdByQH1=queryShowIdByName(td.getQh2()));
                addSheet.getTDataClearLine().setStNo(stNo);
                addSheet.getTDataClearLine().setQhStatus(qhStatus);
                clearLineDataMapper.updateAllDateByMastData(addSheet.getTDataClearLine());
                clearLineDataMapper.updateAllDateBySalveData(addSheet.getTDataClearLineCheckItemByLine());
                return "成功签核";
            } /*else if (stNo.equals("2")&&qhStatus.equals("0")) {
                //课长退签
                stNo="3";
                qhStatus="0";
                addSheet.getTDataClearLine().setShowId(showIdByQh2);
                addSheet.getTDataClearLine().setStNo(stNo);
                addSheet.getTDataClearLine().setQhStatus(qhStatus);
                clearLineDataMapper.updateAllDateByMastData(addSheet.getTDataClearLine());
                clearLineDataMapper.updateAllDateBySalveData(addSheet.getTDataClearLineCheckItemByLine());
                return "成功签核";
            }*/
        }

        return "签核失败";
    }

    @Override
    @DSTransactional()
    public String rebackCheckFunctionByIDS1(AddSheet addSheet) {
        CheckDtoByClearLine checkDto =new CheckDtoByClearLine();
        String id =addSheet.getTDataClearLine().getId();
        checkDto.setId(id);
        String stNo=null;
        String qhStatus=null;
        List<TDataClearLineCheckByData> list =queryCheckData(checkDto);
        //先判断当前签核状态到那一步
        for (TDataClearLineCheckByData td :list){
            stNo=td.getStNo();
            qhStatus=td.getQhStatus();
            if(stNo.equals("1") && qhStatus.equals("1")){
                //品保签核
                stNo="1";
                qhStatus="0";
                addSheet.getTDataClearLine().setShowId(queryShowIdByName(td.getQh3()));
                addSheet.getTDataClearLine().setStNo(stNo);
                addSheet.getTDataClearLine().setQhStatus(qhStatus);
                clearLineDataMapper.updateAllDateByMastData(addSheet.getTDataClearLine());
                clearLineDataMapper.updateAllDateBySalveData(addSheet.getTDataClearLineCheckItemByLine());
                return "成功签核";
            }
        }
        return "签核失败";
    }

    private List<TDataClearLineCheckByData> queryCheckData(CheckDtoByClearLine checkDto){

        return clearLineDataMapper.selectCheckData(checkDto);
    }

    private String queryShowIdByName(String name){
      String showId=  clearLineDataMapper.selectShowByName(name);

        if (showId==null ||showId.equals("")){
            showId=clearLineDataMapper.selectLineLeaderNumber(name);
        } else if (showId==null ||showId.equals("")){
            showId=clearLineDataMapper.selectGroupLeaderNumber(name);
        }
        return showId;
    }

}