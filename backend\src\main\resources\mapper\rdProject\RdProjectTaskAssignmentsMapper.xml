<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectTaskAssignmentsMapper">

    <!-- 結果映射 -->
    <resultMap id="RdProjectTaskAssignmentsResultMap" type="cc.mrbird.febs.rdProject.entity.RdProjectTaskAssignments">
        <result column="task_id" property="taskId"/>
        <result column="assignee_type" property="assigneeType"/>
        <result column="assignee_id" property="assigneeId"/>
    </resultMap>

    <!-- 插入任務分配記錄 -->
    <insert id="insertRdProjectTaskAssignments" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTaskAssignments">
        INSERT INTO rd_project_task_assignments (task_id, assignee_type, assignee_id)
        VALUES (#{taskId}, #{assigneeType}, #{assigneeId})
    </insert>

    <!-- 根據任務ID和分配者ID刪除任務分配記錄 -->
    <delete id="deleteRdProjectTaskAssignments">
        DELETE FROM rd_project_task_assignments 
        WHERE task_id = #{taskId} AND assignee_id = #{assigneeId}
    </delete>

    <!-- 根據任務ID刪除所有任務分配記錄 -->
    <delete id="deleteRdProjectTaskAssignmentsByTaskId">
        DELETE FROM rd_project_task_assignments WHERE task_id = #{taskId}
    </delete>

    <!-- 更新任務分配記錄 -->
    <update id="updateRdProjectTaskAssignments" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTaskAssignments">
        UPDATE rd_project_task_assignments
        SET assignee_type = #{assigneeType}
        WHERE task_id = #{taskId} AND assignee_id = #{assigneeId}
    </update>

    <!-- 根據任務ID查詢任務分配記錄列表 -->
    <select id="selectRdProjectTaskAssignmentsByTaskId" resultMap="RdProjectTaskAssignmentsResultMap">
        SELECT task_id, assignee_type, assignee_id
        FROM rd_project_task_assignments
        WHERE task_id = #{taskId}
    </select>

    <!-- 根據分配者ID查詢任務分配記錄列表 -->
    <select id="selectRdProjectTaskAssignmentsByAssigneeId" resultMap="RdProjectTaskAssignmentsResultMap">
        SELECT task_id, assignee_type, assignee_id
        FROM rd_project_task_assignments
        WHERE assignee_id = #{assigneeId}
    </select>

    <!-- 根據分配者類型查詢任務分配記錄列表 -->
    <select id="selectRdProjectTaskAssignmentsByAssigneeType" resultMap="RdProjectTaskAssignmentsResultMap">
        SELECT task_id, assignee_type, assignee_id
        FROM rd_project_task_assignments
        WHERE assignee_type = #{assigneeType}
    </select>

    <!-- 查詢所有任務分配記錄 -->
    <select id="selectAllRdProjectTaskAssignments" resultMap="RdProjectTaskAssignmentsResultMap">
        SELECT task_id, assignee_type, assignee_id
        FROM rd_project_task_assignments
    </select>

</mapper>
