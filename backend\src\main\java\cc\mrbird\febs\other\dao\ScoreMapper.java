package cc.mrbird.febs.other.dao;

import cc.mrbird.febs.other.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
@DS("one")
public interface ScoreMapper {
    List<ScoreDto> selectScoreData();
    List<String> selectAllCompetition();
    Integer selectCompetitionId(String competitionName);

    List<SelectScoreVo> selectAllDataById(String  name);

    List<CompetitionConfVo> getCompetitionData(String competitionName);

    List<ScoreBySortVo> getScoreBYSort(String prizeName,String competitionName);

    List<CompetitionConfigVo> getCompetitionConfig(String competitionName);
}
