package cc.mrbird.febs.rdProject.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * @Author: 李囯斌
 * @Date: 2025/7/21
 * @Time: 13:07
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectHistory {
    @ApiModelProperty(value = "歷史動態ID", position = 0)
    private Long historyId;

    @ApiModelProperty(value = "操作細則", position = 1)
    private String content;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(value = "操作时间",example = "2025-07-18 12:11:34", position = 2)
    private Date time;

    @ApiModelProperty(value = "操作者名字", position = 3)
    private String userName;

    @ApiModelProperty(value = "操作者工號", position = 4)
    private String workerId;

    @ApiModelProperty(value = "厂区", position = 5)
    private String siteArea;

    @ApiModelProperty(value = "产品处", position = 6)
    private String sbu;

    @ApiModelProperty(value = "机能", position = 7)
    private String functions;

    @ApiModelProperty(value = "课别", position = 8)
    private String section;
}
