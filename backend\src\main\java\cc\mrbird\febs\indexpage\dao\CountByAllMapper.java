package cc.mrbird.febs.indexpage.dao;

import cc.mrbird.febs.indexpage.entity.qo.SelectLineNumberQo;
import com.baomidou.dynamic.datasource.annotation.DS;

@DS("one")
public interface CountByAllMapper {
    //获取个人 开线数量数据
    Integer selectLineNumberByOne(SelectLineNumberQo selectLineNumberQo);
    //获取对应组织  开线数量数据
    Integer selectLineNumberBySection(SelectLineNumberQo selectLineNumberQo);
    //生产日报表获取 填写记录
    Integer selectDayNumberByOne(SelectLineNumberQo selectLineNumberQo);
    //生产日报表对应组织 获取填写记录
    Integer selectDayNumberBySection(SelectLineNumberQo selectLineNumberQo);
    //线长交接表填写记录数（根据线长）
    Integer selectLineSubByOne(SelectLineNumberQo selectLineNumberQo);
    //线长交接表填写记录数（根据IDS1 课别）
    Integer selectLineSubBySection(SelectLineNumberQo selectLineNumberQo);
    //136比对表填写记录数 (根据线长)
    Integer select136ByOne(SelectLineNumberQo selectLineNumberQo);
    //136比对表填写记录(根据IDS1 课别)
    Integer select136BySection(SelectLineNumberQo selectLineNumberQo);
    String selectPerms(String cardId);
}
