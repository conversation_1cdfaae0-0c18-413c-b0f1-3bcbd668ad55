package cc.mrbird.febs.backwork.service;

import cc.mrbird.febs.backwork.entity.TDataBackwork;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_data_backwork】的数据库操作Service
* @createDate 2023-02-13 15:59:23
*/
public interface TDataBackworkService extends IService<TDataBackwork> {
    //文件上傳接口
    Map<String,String>  backWorkFile(MultipartFile backWorkFile);

    /**
     * 文件下载
     * */
    void   downloadFile(String fileName);





}
