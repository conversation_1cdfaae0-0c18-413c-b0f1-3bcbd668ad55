package cc.mrbird.febs.comparation136.VO;

import cc.mrbird.febs.comparation136.entity.ImageInformation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryIdComparation136VO implements Serializable {

    //課別
    private String section;

    //線體
    private String line;

    //料號
    private String CPLHPFN;

    //對比日期
    private String BDDate;

    //班別
    private String ProductClass;

    //對比項目1-10
    private String BDXM01;

    private String BDXM02;

    private String BDXM03;

    private String BDXM04;

    private String BDXM05;

    private String BDXM06;

    private String BDXM07;

    private String BDXM08;

    private String BDXM09;

    private String BDXM10;

    private String BDXM11;

    private String BDXM12;

    //是否ng
    private String SFNG;

    //NG原因
    private String NGCause;

    //創建人名
    private String JLRName;

    //IPQC名字
    private String IPQCQHRName;

    //簽核組長名字
    private String ZZQHRName;

    //圖片關聯信息
    private List<ImageInformation> imageInformation = new ArrayList<>();

}
