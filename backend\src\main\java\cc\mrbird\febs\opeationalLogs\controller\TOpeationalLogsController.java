package cc.mrbird.febs.opeationalLogs.controller;


import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.opeationalLogs.dao.TLogsByProduction;
import cc.mrbird.febs.opeationalLogs.entity.TOpeationalLogsLine;
import cc.mrbird.febs.opeationalLogs.entity.TOpeationalLogsProduction;
import cc.mrbird.febs.opeationalLogs.service.ITOpeationalLogsLineService;
import cc.mrbird.febs.opeationalLogs.service.ITOpeationalLogsService;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/t_opeational_logs/t-opeational-logs")
public class TOpeationalLogsController {

    @Autowired
    private ITOpeationalLogsService itOpeationalLogsService;

    @Autowired
    private ITOpeationalLogsLineService itOpeationalLogsLineService;

    @Autowired
    private TLogsByProduction tLogsByProduction;

    @ApiOperation(value = "产量表编辑表单",notes = "查询产量表日志")
    @GetMapping("/editProduction")
    public FebsResponse editProduction(@RequestParam("name")String name){
        TOpeationalLogsProduction tOpeationalLogsProduction =new TOpeationalLogsProduction(null,name,"編輯表單",new Date());
        itOpeationalLogsService.save(tOpeationalLogsProduction);
        return new FebsResponse().message("ok");
    }
    @ApiOperation(value = "线长交接表编辑表单",notes = "查询产量表日志")
    @GetMapping("/editLine")
    public FebsResponse editLine(@RequestParam("name")String name){
        TOpeationalLogsLine tOpeationalLogsLine=new TOpeationalLogsLine(null,name,"编辑表单",new Date());
        itOpeationalLogsLineService.save(tOpeationalLogsLine);
        return new FebsResponse().message("ok");
    }
    @ApiOperation(value = "查询产量表日志",notes = "查询产量表日志")
    @GetMapping("/queryProduction")
    public FebsResponse queryProduction(){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.orderByDesc("id");
        queryWrapper.last("limit 10");
        List<TOpeationalLogsProduction> list=itOpeationalLogsService.list(queryWrapper);
        return new FebsResponse().data(list);
    }


    @ApiOperation(value = "查询线长交接表日志",notes = "查询线长交接表日志")
    @GetMapping("/queryLine")
    public FebsResponse queryLine(){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.orderByDesc("id");
        queryWrapper.last("limit 10");
        List<TOpeationalLogsProduction> list=itOpeationalLogsLineService.list(queryWrapper);
        return new FebsResponse().data(list);
    }
    //根据名字和日期查询生产日报表操作日志

    @ApiOperation(value = "生产日报表根据名字和日期查询",notes = "生产日报表根据名字和日期查询日志")
    @GetMapping("/queryLogByName")
    public  FebsResponse queryLogByName(@RequestParam("name") String name,
    @RequestParam("Date") String Date) throws ParseException {
        //String 转换为  Date
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String str = Date;
        //
        java.util.Date date = simpleDateFormat.parse(str);
        String date1=simpleDateFormat.format(date);
        DateTime dt = DateUtil.parse(date1);
        System.out.println("没加一天的日期为:"+dt);
        /**
         * 先把前端传来的字符串做处理
         * 先把字符串转化为Date类型然后格式化
         * 最后再转换为字符串类型
         * */
        String str3 = date1;
        java.util.Date tt = simpleDateFormat.parse(str3);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(tt);
        calendar.add(calendar.DATE,1);
        tt = calendar.getTime();
        String tt1 = simpleDateFormat.format(tt);
        DateTime ttd=DateUtil.parse(tt1);
      //  System.out.println("加一天后的日期为:"+ttd);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.orderByDesc("id");
        queryWrapper.eq("name",name);
        queryWrapper.ge("recordTime",dt);
        queryWrapper.lt("recordTime",ttd);
        List<TOpeationalLogsProduction> list = itOpeationalLogsService.list(queryWrapper);
        return  new FebsResponse().data(list);
    }

    @ApiOperation(value = "查询线长交接表操作日志To名字和时间",notes = "根据名字和时间查询线长交接表日志")
    @GetMapping("/queryByLineLogs")
    public FebsResponse queryByLineLogs(@RequestParam("name") String name,@RequestParam("date")String date) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String str = date;
        java.util.Date date1 = sdf.parse(str);
        String date2 = sdf.format(date1);
        QueryWrapper queryWrapper = new QueryWrapper();

        return new FebsResponse().data(null);
    }
    @ApiOperation(value = "生產趨勢新接口",notes = "生產趨勢新接口")
    @GetMapping("queryByProductionLogs")
    public FebsResponse queryByProductionLogs(@RequestParam("name")String name,String Date){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("name",name);
        //截取數據庫指定字段的日期值,保留年月.
        queryWrapper.eq("DATE_FORMAT(recordTime,'%Y-%m')",Date);
        queryWrapper.orderByDesc("id");
        List<TOpeationalLogsProduction> list = itOpeationalLogsService.list(queryWrapper);
        return new FebsResponse().data(list);
    }

}
