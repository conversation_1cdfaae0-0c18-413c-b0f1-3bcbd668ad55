package cc.mrbird.febs.jjkpi.service.impl;

import cc.mrbird.febs.jjkpi.entity.DjxxDto;
import cc.mrbird.febs.jjkpi.entity.LineInfo;
import cc.mrbird.febs.jjkpi.entity.SixGdDjxx;
import cc.mrbird.febs.jjkpi.dao.SixGdDjxxMapper;
import cc.mrbird.febs.jjkpi.service.ISixGdDjxxService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service

public class SixGdDjxxServiceImpl extends ServiceImpl<SixGdDjxxMapper, SixGdDjxx> implements ISixGdDjxxService {
    @Autowired
    private SixGdDjxxMapper sixGdDjxxMapper;
    @Override
    @DS("one")
    public List<DjxxDto> queryLineForm(String line,String classInfo,String production) {
        List<DjxxDto> list=baseMapper.queryForm(line,classInfo,production);
        for(DjxxDto djxxDto:list){
            djxxDto.setSCSL_QOP_REAL(djxxDto.getSCSL_QOP());
           // djxxDto.getSCSL_QOP().setScale(3);
           // djxxDto.getSCSL_QOP_REAL().setScale(3);
        }
        return list;
    }

    @Override
    @DS("one")
    public List<LineInfo> queryLineInfo(String cardId,List<String> lines) {
        List<LineInfo> lineInfos=new ArrayList<>();
        for(String pl:lines){
            lineInfos.add(sixGdDjxxMapper.queryProductionLineInfo(pl));
        }
        return lineInfos;
    }

    @DS("mssql")
    @Override
    public List<String> queryLineByCardId(String cardId) {
        //根据工卡查询所负责的线体
        return sixGdDjxxMapper.queryProducitonLine(cardId);
    }
}
