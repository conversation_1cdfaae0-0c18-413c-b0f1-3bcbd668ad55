package cc.mrbird.febs.equipmentSelfInspection.dao;

import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentPersonDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.ProjectMaintenance;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentQueryParam;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;

/**
 * Author: 覃金華
 * Date: 2024-08-12
 * Time: 上午 11:27
 */
@DS("one")
public interface EquipmentSelfInspectionMapper {
   List<EquipmentInspectionDto> selectSelfInspectionData(EquipmentQueryParam equipmentQueryParam);

   List<ProjectMaintenance> selectProjectMaintenanceData(EquipmentQueryParam equipmentQueryParam);

   List<EquipmentPersonDto> selectEquipmentPersonData(EquipmentQueryParam equipmentQueryParam);
   List<String>  selectDiscern();
   List<String> selectProduction(String discern);
   List<String> selectProductionLine(String discern,String production);
   List<String> selectEquipmentName(String discern,String production,String productionLine);

   List<String> selectSelfInspectionType();

   int updateByEquipmentData(EquipmentInspectionDto equipmentInspectionDto);
   int updateByProjectMaintenance(ProjectMaintenance projectMaintenance);
   int updateByEquipmentPerson(EquipmentPersonDto equipmentPerson);

   //人員數據刪除
   int delByEquipmentPerson(Integer id);
   //設備數據刪除
   int delByEquipmentData(Integer id);

   //人員數據新增
   int insertByEquipmentPerson(EquipmentPersonDto equipmentPersonDto);
   //設備信息插入
   int insertByEquipmentInspection(EquipmentInspectionDto equipmentInspectionDto);
   //點檢項目數據插入
   int insertByProjectMaintenance(ProjectMaintenance projectMaintenance);
   int countByEquipmentInspection(String production,String productionLine,String equipmentName);


   int delByProjectMaintenance(Integer id);
   String  selectEquipmentId(String production,String productionLine,String equipmentName);
   List<EquipmentInspectionDto> exampleDataByEquipment();

   List<ProjectMaintenance> selectExampleDataByProject();
}
