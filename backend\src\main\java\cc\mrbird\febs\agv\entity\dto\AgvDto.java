package cc.mrbird.febs.agv.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Author: 李囯斌
 * @Date: 2025/2/25
 * @Time: 11:17
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class AgvDto {
    @ApiModelProperty(value = "AGVId",position = 0)
    private Integer id;
    @ApiModelProperty(value = "厂区",example = "BK",position = 1)
    private String factory;
    @ApiModelProperty(value = "楼栋",example = "A3",position = 2)
    private String building;
    @ApiModelProperty(value = "机能",example = "成型",position = 3)
    private String functions;
    @ApiModelProperty(value = "机器人ID",example = "2",position = 4)
    private String robotId;
    @ApiModelProperty(value = "机器人IP",example = "*************",position = 5)
    private String robotIP;
    @ApiModelProperty(value = "机器人名称",example = "成型工序2#AGV",position = 6)
    private String robotName;
    @ApiModelProperty(value = "机器人图片url",example = "http://************:3000/ImageView/wzz/AGV01.png",position = 7)
    private String robotPicUrl;
    @ApiModelProperty(value = "工作状态",example = "空闲",position = 7)
    private String workStatus;
    @ApiModelProperty(value = "充电状态",example = "OFF",position = 8)
    private String chargeState;
    @ApiModelProperty(value = "上线状态",example = "在線",position = 9)
    private String onlineStatus;
    @ApiModelProperty(value = "顶升状态",example = "ON",position = 10)
    private String jackState;
    @ApiModelProperty(value = "报错码",example = "0",position = 11)
    private String errorCode;
    @ApiModelProperty(value = "报错消息",example = "正常",position = 12)
    private String errorMsg;
    @ApiModelProperty(value = "电量",example = "86",position = 13)
    private Integer energyLevel;
    @ApiModelProperty(value = "当前站点",example = "1002",position = 14)
    private String currentStation;
    @ApiModelProperty(value = "獲取當前的任務ID",example = "-1",position = 15)
    private String currentTaskId;
    @ApiModelProperty(value = "当前位置 X 坐标",example = "24105",position = 16)
    private String posX;
    @ApiModelProperty(value = "当前位置 Y坐标",example = "80395",position = 17)
    private String posY;
    @ApiModelProperty(value = "当前位置角度",example = "-177",position = 18)
    private String angle;
    @ApiModelProperty(value = "path",example = "[]",position = 19)
    private String path;
    @ApiModelProperty(value = "currentLock",example = "False",position = 20)
    private String currentLock;
    @ApiModelProperty(value = "unfinishedPath",example = "",position = 21)
    private String unfinishedPath;
    @ApiModelProperty(value = "detectStepList",example = "[]",position = 22)
    private String detectStepList;
    @ApiModelProperty(value = "UPTime",example = "2025-02-25 11:15:29",position = 23)
    private String uPTime;
}

