package cc.mrbird.febs.procedurefileuploading.dao;

import cc.mrbird.febs.procedurefileuploading.entity.dto.TDataProcedureFileUpload;
import cc.mrbird.febs.procedurefileuploading.entity.qo.GetProcedureFileUploadDataQo;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;

@DS("mssql")
public interface ProcedureFileUploadMapper {
    int  insertFileData(TDataProcedureFileUpload tDataProcedureFileUpload);
    int  insertFileDataByUpdate(TDataProcedureFileUpload tDataProcedureFileUpload);

    List<String> getPL();

    List<String> getDiscern(String pl,String enginery);

    List<String> getProduction(String pl, String discern);

    List<String> getProductionLine(String pl, String discern, String production);

    List<TDataProcedureFileUpload> getProcedureFileUploadData(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);
    List<String> getProcedureFileUploadEquipmentName(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getProductionByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getProductionLineByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getEquipmentNameByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getEquipmentCodeByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);
    //判断是否是第一次新增
    int    isFirstInsert();
    //根据ID修改ShowID
    int updateShowId(Integer id);
    //根据ID获取更新次数
    int queryUpdateNum(Integer id);
    String sendEmail(String name);
    String selectSecondName(String name);

    String queryEngineryByWorkId(String workId);
}
