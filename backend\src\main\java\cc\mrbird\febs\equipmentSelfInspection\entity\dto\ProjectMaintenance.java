package cc.mrbird.febs.equipmentSelfInspection.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 覃金華
 * Date: 2024-08-12
 * Time: 下午 12:56
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProjectMaintenance {
    @ApiModelProperty(value = "數據表ID",position = 0)
    private Integer id;
    @ApiModelProperty(value = "設備ID",position = 1)
    private String equipmentId;
    @ApiModelProperty(value = "點檢類型",position = 2)
    private String selfInspectionType;
    @ApiModelProperty(value = "點檢方式",position = 3)
    private String selfInspectionWay;
    @ApiModelProperty(value = "點檢位置",position = 4)
    private String selfInspectionPosition;
    @ApiModelProperty(value = "點檢標準、基準",position = 5)
    private String selfInspectionStandard;
    @ApiModelProperty(value = "點檢狀態",position = 6)
    private String status;
    @ApiModelProperty(value = "產品處",position = 7)
    private String pl;
    @ApiModelProperty(value = "機能",position = 8)
    private String enginery;
    @ApiModelProperty(value = "課別",position = 9)
    private String discern;
    @ApiModelProperty(value = "系列",position = 10)
    private String production;
    @ApiModelProperty(value = "線體",position =11)
    private String productionLine;
    @ApiModelProperty(value = "設備名稱",position =12)
    private String equipmentName;

    public ProjectMaintenance(String pl, String enginery, String discern, String production, String productionLine, String equipmentName, String selfInspectionType, String status, String selfInspectionPosition, String selfInspectionStandard) {
    }
}
