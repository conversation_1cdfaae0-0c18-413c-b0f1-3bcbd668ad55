package cc.mrbird.febs.jjkpi.controller;
import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.jjkpi.service.ISixGdDjxxService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@ResponseBody
@RequestMapping("/jjkpi/six-gd-djxx")
@RequiresPermissions("zyc")
public class SixGdDjxxController {

    @Autowired
    private ISixGdDjxxService iSixGdDjxxService;

    /***
     *
     * 线长交接表查找投单数据
     * */

    //kpi表查询
    @GetMapping("/query")
    public FebsResponse query(@RequestParam("line")String line,@RequestParam("classInfo")String classInfo,@RequestParam("production")String production){
        if (production.equals("Type-C三屏")){
            production="Type-C人工組";
        }
        if(production.equals("8371SKT")){
            production="LGA8371";
        }
        if (production.equals("DMD車業")||production.equals("DMD非車業")){
            production="DMD";
        }
        if (production.equals("3647五金")&&line.equals("PA")){
            production="3647SKTP五金";
            line="B2";
        }
        if (production.equals("3647五金")&&line.equals("P2")){
            production="3647SKTP五金";
            line="D1";
        }
        if (production.equals("3647五金")&&line.equals("P3")){
            production="3647SKTP五金";
            line="B1";
        }
        return new FebsResponse().data(iSixGdDjxxService.queryLineForm(line,classInfo,production));
    }
    //查询线长相关信息
    @GetMapping("/queryLineInfo")
    public FebsResponse queryLineInfo(@RequestParam("cardId")String cardId){
        List<String> lines=iSixGdDjxxService.queryLineByCardId(cardId);
        return new FebsResponse().data(iSixGdDjxxService.queryLineInfo(cardId,lines));
    }


}
