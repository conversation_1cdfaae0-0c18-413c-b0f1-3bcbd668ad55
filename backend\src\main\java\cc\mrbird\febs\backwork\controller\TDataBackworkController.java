package cc.mrbird.febs.backwork.controller;

import cc.mrbird.febs.backwork.common.R;
import cc.mrbird.febs.backwork.dao.TDataBackworkMapper;
import cc.mrbird.febs.backwork.entity.*;
import cc.mrbird.febs.backwork.service.CheckService;
import cc.mrbird.febs.backwork.service.Email;
import cc.mrbird.febs.backwork.service.TDataBackworkService;
import cc.mrbird.febs.backwork.util.ArtMessage;
import cc.mrbird.febs.common.domain.FebsResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@ResponseBody
@Api(tags = "重工记录表")
@RequestMapping("/backWork")
@Slf4j
public class TDataBackworkController {
    @Autowired
    private TDataBackworkMapper tDataBackworkMapper;
    @Autowired
    private Email emailMapper;

    @Autowired
    private TDataBackworkService tDataBackworkService;
    @Autowired
    private CheckService checkService;
    ArtMessage artMessage =new ArtMessage();
   final    String tips ="请使用谷歌浏览器打开：";
    @PostMapping("/queryBackWork")
    @ApiOperation(value = "设备重工记录表查詢頁面接口", notes = "设备重工记录表查詢頁面接口")
    public FebsResponse selectALl(@RequestParam("uuid") String uuid) {
        List<TDataBackwork> newList = new ArrayList<>();
        DecimalFormatSymbols symbols = new DecimalFormatSymbols(Locale.getDefault());
        symbols.setDecimalSeparator('.'); //设置小数点符号
        DecimalFormat df = new DecimalFormat("0.00", symbols);
        df.setRoundingMode(RoundingMode.FLOOR); //设置舍入模式为下舍入
        for (TDataBackwork item : tDataBackworkMapper.selectByproductionLike(uuid)) {
            double cancellationCost = Double.parseDouble(item.getCancellationCost());
            //格式化cancellationCost，保留两位小数，不进行四舍五入
            String formattedCancellationCostStr = df.format(cancellationCost);
            double formattedCancellationCost = Double.parseDouble(formattedCancellationCostStr);
            item.setCancellationCost(String.valueOf(formattedCancellationCost));
            newList.add(item);
        }
        return new FebsResponse().data(newList);
    }

    //條件查詢接口
    @PostMapping("/queryBackWorkByPartNumber")
    @ApiOperation(value = "設備重工記錄表按日期、料號查詢", notes = "設備重工記錄表按日期、料號查詢")
    public FebsResponse queryBackWorkByPartNumber(@RequestBody TDataBackWorkDto backWorkDto) {
        List<TDataBackwork> list =new ArrayList<>();
        DecimalFormatSymbols symbols = new DecimalFormatSymbols(Locale.getDefault());
        symbols.setDecimalSeparator('.'); //设置小数点符号
        DecimalFormat df = new DecimalFormat("0.00", symbols);
        df.setRoundingMode(RoundingMode.FLOOR); //设置舍入模式为下舍入
        for (TDataBackwork item :  tDataBackworkMapper.queryByPartNumber(backWorkDto)) {
            double cancellationCost = Double.parseDouble(item.getCancellationCost());
            //格式化cancellationCost，保留两位小数，不进行四舍五入
            String formattedCancellationCostStr = df.format(cancellationCost);
            double formattedCancellationCost = Double.parseDouble(formattedCancellationCostStr);
            item.setCancellationCost(String.valueOf(formattedCancellationCost));
            list.add(item);
        }
        return new FebsResponse().data(list);
    }

    @PostMapping("/queryAllPartNumber")
    @ApiOperation(value = "設備重工記錄表查詢所有料號", notes = "設備重工記錄表查詢所有料號")
    public FebsResponse queryAllPartNumber() {
        List<String> list = tDataBackworkMapper.selectAllPartNumber();
        return new FebsResponse().data(list);
    }

    @PostMapping("/uploadFile")
    @ApiOperation("文件上传接口")
    public R<Map<String, String>> uploadFile(@RequestParam MultipartFile multipartFile) {
        return R.<Map<String, String>>ok().setData(tDataBackworkService.backWorkFile(multipartFile));
    }
    //@CrossOrigin(origins = "http://**************:8081")
    @GetMapping("/downloadFile")
    @CrossOrigin(origins = "*")
    @ApiOperation(("文件下载接口"))
    public void downloadFile(String filePath, HttpServletResponse response) {
        try {
            URLDecoder.decode(filePath, "UTF-8");
            File file = new File(filePath);
            String fileName = file.getName();
            String ext = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            FileInputStream fis = new FileInputStream(file);
            InputStream is = new BufferedInputStream(fis);
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            is.close();
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode(fileName, "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            response.addHeader("content-Length", "" + file.length());
            response.setHeader("Access-Control-Allow-Origin", "*");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 查询重工单位
     */
    @ApiOperation(value = "查詢重工責任單位", notes = "查詢重工責任單位")
    @PostMapping("/selectUnit")
    public FebsResponse selectUnit() {
        List<String> list = tDataBackworkMapper.selectUnit();
        list = list.stream().distinct().collect(Collectors.toList()); //list特有的去重方法
        return new FebsResponse().code("200").data(list);
    }
    /*
    * 线组长：--->1新建并带出名字
    *
    *
    * */
    @ApiOperation(value = "查询身份 1->线长、组长 2->课长 3->品保 4->责任单位负责人 5->电镀内部签核", notes = "查询身份")
    @GetMapping("/selectRole")
    public FebsResponse selectRole(@RequestParam("workId") String workId,@RequestParam("uuid") String uuid) {
        System.out.println("工号是---->"+workId+"uuid是---->"+uuid);
        List<UserRoleDto> list1 = tDataBackworkMapper.selectRoleByWorkId(workId);
        List<UserRoleDto> list2 = tDataBackworkMapper.selectState(uuid);
        List<UserRoleDto> mergedList = new ArrayList<>();
        int size = Math.min(list1.size(), list2.size());
        for (int i = 0; i < size; i++) {
            UserRoleDto user1 = list1.get(i);
            UserRoleDto user2 = list2.get(i);
            UserRoleDto mergedUser = new UserRoleDto();
            // 将第一个 UserRoleDto 对象的属性赋值给合并后的对象
            mergedUser.setRole(user1.getRole());
            mergedUser.setName(user1.getName());
            // 将第二个 UserRoleDto 对象的属性赋值给合并后的对象
            mergedUser.setStNo(user2.getStNo());
            mergedUser.setQhStatus(user2.getQhStatus());
            mergedUser.setShowId(user2.getShowId());
            mergedList.add(mergedUser);
        }

        // 如果其中一个列表比较长，将剩下的元素添加到最终的列表中
        if (list1.size() > size) {
            mergedList.addAll(list1.subList(size, list1.size()));
        } else if (list2.size() > size) {
            mergedList.addAll(list2.subList(size, list2.size()));
        }
        return new FebsResponse().code("200").data(mergedList);
    }
    @ApiOperation(value = "查询责任单位对应负责人")
    @PostMapping("/selectUnitName")
    public FebsResponse selectUnitName(@RequestParam("unit") String unit){
        List<String> list =tDataBackworkMapper.findUnitName(unit);
        return new FebsResponse().code("200").data(list);
    }

    //新增
    @PostMapping("/insertAll")
    @ApiOperation(value = "设备重工记录表新增接口", notes = "设备重工记录表新增接口")
    public FebsResponse insertAll(@RequestBody TDataBackwork tDataBackwork) throws IOException {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String uuid = "R" + sdf.format(new Date());
            String isGoBack="0";
            tDataBackwork.setUuid(uuid);
            tDataBackwork.setIsBack(isGoBack);
            String showId=    tDataBackworkMapper.selectWorkIdByNewAdd(tDataBackwork.getQh1());
            tDataBackwork.setShowId(showId);
            System.out.println(tDataBackwork);
          //新增表单接口
            boolean b = checkService.insertNotNull(tDataBackwork);
            if (tDataBackwork.getPl().equals("IDS1")){
                tDataBackworkMapper.insertSelective(tDataBackwork);
                String email=tDataBackworkMapper.findEmail(tDataBackwork.getQh1());
                //String email="<EMAIL>";
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                //String subject="tt";
                //String body="当前流程为:"+tDataBackworkMapper.findLeaderName(tDataBackwork.getUuid())+"(承辦)"+"等待您簽核"+"<br>如有問題,請聯繫<br><568-25023:王業桂><br><br><5068-26332:李光翔><br><br><br><br><br><br><br>請勿回復此邮件";
                //System.out.println(artMessage.getHtmlCode());
                String encodedUrl=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackwork.getLineLeaderName()+"(承辦)"+"--->"+tDataBackwork.getQh1()+"(等待您簽核)"+"<br>";
                //System.out.println("线长名字是---->"+tDataBackworkMapper.findLeaderName(uuid));
                String body = URLEncoder.encode(encodedUrl, "UTF-8");
                body=body+artMessage.getHtmlCode();
                subject=URLEncoder.encode(subject,"UTF-8");
                String cc="<EMAIL>";
                emailMapper.selectEmail(email,subject,body,cc);
            }else if (b){
                tDataBackworkMapper.insertSelective(tDataBackwork);
                String email=tDataBackworkMapper.findEmail(tDataBackwork.getQh1());
                //String email="<EMAIL>";
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                //String subject="tt";
                //String body="当前流程为:"+tDataBackworkMapper.findLeaderName(tDataBackwork.getUuid())+"(承辦)"+"等待您簽核"+"<br>如有問題,請聯繫<br><568-25023:王業桂><br><br><5068-26332:李光翔><br><br><br><br><br><br><br>請勿回復此邮件";
                //System.out.println(artMessage.getHtmlCode());
                String encodedUrl=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackwork.getLineLeaderName()+"(承辦)"+"--->"+tDataBackwork.getQh1()+"(等待您簽核)"+"<br>";
                System.out.println("线长名字是---->"+tDataBackworkMapper.findLeaderName(uuid));
                String body = URLEncoder.encode(encodedUrl, "UTF-8");
                body=body+artMessage.getHtmlCode();
                subject=URLEncoder.encode(subject,"UTF-8");
                //String body="thisatestemail";
                //String cc="<EMAIL>,<EMAIL>";
                String cc="<EMAIL>";
                emailMapper.selectEmail(email,subject,body,cc);
            }else {
                return new FebsResponse().code("500").message("未正确选择填写签核人，请检查您的签核流程");
            }

        } catch (Exception e) {
            System.out.println(e);
            return new FebsResponse().code("500").message("出现未知异常，请联系开发人员");
        }
       return new FebsResponse().code("200").message("插入成功!!!");
    }

    @PostMapping("/findQc")
    @ApiOperation(value = "查詢QC", notes = "查詢QC")
    public FebsResponse findQc(@RequestParam("pl") String pl) {
        List<String> list = tDataBackworkMapper.findQc(pl);
        return new FebsResponse().code("200").data(list);
    }
    @PostMapping("/findDiscernLeader")
    @ApiOperation(value = "根据role查询下一个推送人接口")
    public  FebsResponse findDiscernLeader(@RequestBody RoleAndPl roleAndPl){
        String pl =roleAndPl.getPl();
        String role =roleAndPl.getRole();
        if (role.matches("[1-6]")) {
            role = Integer.toString(Integer.parseInt(role) + 1);
        }
        roleAndPl.setRole(role);
        roleAndPl.setPl(pl);
        List<String> list =tDataBackworkMapper.findDiscernLeader(roleAndPl);
        return new FebsResponse().code("200").data(list);
    }
   @PostMapping("/check")
   @ApiOperation(value = "簽核接口", notes = "簽核接口")
    public FebsResponse check(@RequestBody CheckDto checkDto){
       System.out.println(checkDto);
       switch (checkDto.getPl()) {
           case "IDS1":
               log.info("单号："+checkDto.getUuid()+"签核人"+checkDto.getName());
               return checkService.CheckByPl1(checkDto);
           case "IDS2":
               log.info("单号："+checkDto.getUuid()+"签核人"+checkDto.getName());
               return checkService.CheckByPl2(checkDto);
           case "IDS3":
               log.info("单号："+checkDto.getUuid()+"签核人"+checkDto.getName());
               return checkService.CheckByPl3(checkDto);
           default:
               log.info("单号："+checkDto.getUuid()+"签核失败");
               return new FebsResponse().code("500").message("签核失败");
       }

   }
    @PostMapping("/selectNoName")
    public FebsResponse selectNoName(@RequestBody NoName noName){
        List<String> list = tDataBackworkMapper.selectNoName(noName);
        return  new FebsResponse().code("200").data(list);
    }
    @ApiOperation(value = "退签接口",notes = "退签接口")
    @PostMapping("/goBackCheck")
    public  FebsResponse goBackCheck(@RequestBody GoBackVo goBackVo) throws IOException {
        String uuid =goBackVo.getUuid();
        String pl=    tDataBackworkMapper.selectPLByUUID(uuid);
        switch (pl) {
            case "IDS1":
                return checkService.goBackCheckByPl1(goBackVo);
            case "IDS2":
                return checkService.goBackCheckByPl2(goBackVo);
            case "IDS3":
                return checkService.goBackCheckByPl3(goBackVo);
            default:
                return new FebsResponse().code("500").message("系统出现问题，请拨打25023联系韦安琪");
        }

    }


    
    @ApiOperation(value = "测试接口",notes = "测试接口")
    @PostMapping("testInsert")
    public boolean testInsert(@RequestBody TDataBackwork tDataBackwork){
       return   checkService.insertNotNull(tDataBackwork);
    }

    @ApiOperation(value = "退簽之後,綫、組長第一次推送接口",notes = "先請把頁面所有數據傳送")
    @PostMapping("/goBackCheckOnFirst")
    public FebsResponse goBackCheck(@RequestBody TDataBackwork tDataBackwork) throws IOException {
        String workId= tDataBackworkMapper.selectWorkIdByQh1( tDataBackwork.getQh1());
        tDataBackwork.setShowId(workId);
        tDataBackwork.setStNo("1");
        tDataBackwork.setQhStatus("0");
        String uuid =tDataBackwork.getUuid();
         int b=   tDataBackworkMapper.delByUUID(uuid);
        String emailNumber=   tDataBackworkMapper.findEmail(tDataBackwork.getQh1());
        String subject="<<<表單无纸化专案----->重工记录表需要您签核";
        String encodedUrl=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackwork.getLineLeaderName()+"(承辦)"+"--->"+tDataBackwork.getQh1()+"(等待您簽核)"+"<br>";
        subject=URLEncoder.encode(subject,"UTF-8");
        String body = URLEncoder.encode(encodedUrl, "UTF-8");
        body=body+artMessage.getHtmlCode();
         if (b==1){
             Integer i=    tDataBackworkMapper.insertSelective(tDataBackwork);
             emailMapper.selectEmail(emailNumber,subject,body,"<EMAIL>");
             return new FebsResponse().code("200").message("呈送成功");
         }else {
             return new FebsResponse().code("500").message("呈送失敗，請聯繫開發工程師");
         }
    }

}