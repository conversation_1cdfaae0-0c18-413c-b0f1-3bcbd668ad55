package cc.mrbird.febs.comparation136.controller;

import cc.mrbird.febs.comparation136.VO.*;
import cc.mrbird.febs.comparation136.dao.Comparation136Mapper;
import cc.mrbird.febs.comparation136.entity.*;
import cc.mrbird.febs.comparation136.result.Result;
import cc.mrbird.febs.comparation136.service.ComparationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

@RestController
@RequestMapping("/136comparation")
@Api(tags = "電鍍136對比相關接口")
@Slf4j
public class Comparation136Controller {


    @Autowired
    private ComparationService comparationService;

    @Autowired
    private Comparation136Mapper comparation136Mapper;








    /*@GetMapping(value ="queryByWorkId")
    @ApiOperation("工號查詢登錄人的產品處和機能")
    public Result<QueryByWorkIdVO> queryByWorkId(String Ins_UserId){
        log.info("通過工號查詢登錄人的產品處和機能{}",Ins_UserId);
        QueryByWorkIdVO queryByWorkIdVO = comparation136Mapper.queryByWorkId(Ins_UserId);
        return Result.success(queryByWorkIdVO);
    }*/



    /*@GetMapping(value = "/queryPerson")
    @ApiOperation("根據課別和線體查詢組長，品保信息")
    public Result<ArrayList<String>> queryPerson(@RequestParam("LX_Type") String LX_Type,@RequestParam("section") String section) {
        log.info("人員表查詢組長，現場品保信息：{}",LX_Type,section);
        ArrayList<String> list = new ArrayList<>();
        list=  comparation136Mapper.queryPersonInformation(LX_Type,section);
        LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
        ArrayList<String> newlist = new ArrayList<>(hashSet);
        return Result.success(newlist);
    }*/

    @GetMapping("/querySBU")
    @ApiOperation("通过工号查询组织")
    public Result<String> querySBU (@RequestParam("workId") String workId){
        log.info("通过工号查询组织:{}",workId);
        String SBU = comparation136Mapper.querySBU(workId);
        return Result.success(SBU);
    }

    @GetMapping("/queryfunctions")
    @ApiOperation("通过工号查询机能")
    public Result<String> queryFunctions (@RequestParam("workId") String workId){
        log.info("通过工号查询机能：{}",workId);
        String section = comparation136Mapper.queryFunctions(workId);
        return Result.success(section);
    }

    @GetMapping("/querySection")
    @ApiOperation("通過工號查詢課別")
   public Result <String> querySection (@RequestParam("workId") String workId){
       log.info("通過工號查詢課別:{}",workId);
       String section = comparation136Mapper.querySection(workId);
       return Result.success(section);
   }

   @GetMapping("/queryLine")
   @ApiOperation("通過課別查詢線體")
   public Result<ArrayList<String>> queryLine(@RequestParam("section") String section){
        log.info("通過課別查詢線體：{}",section);
        ArrayList<String> list = comparation136Mapper.queryLine(section);
       LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
       ArrayList<String > newlist = new ArrayList<>(hashSet);
        return Result.success(newlist);
   }



   @PostMapping("/selectMaterialNumber")
   @ApiOperation("根据课别线体信息查出料号信息")
   public Result<ArrayList<String>> selectMaterialNumber(@RequestBody QueryMaterialNumber queryMaterialNumber){
        log.info("通过课别线体查出料号信息:{}",queryMaterialNumber);
        ArrayList<String> list = comparation136Mapper.selectMaterialNumber(queryMaterialNumber);
       LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
       ArrayList<String > newlist = new ArrayList<>(hashSet);
       return Result.success(newlist);
   }

    @GetMapping("/queryIPQC")
    @ApiOperation("通過課別查詢品保信息")
    public Result<List<QueryIPQCVO>> queryIPQC(@RequestParam("section") String section){
        log.info("通過課別查詢品保信息",section);
        QueryIPQCVO queryIPQCVO = new QueryIPQCVO();
        List<QueryIPQCVO> list = comparation136Mapper.queryIPQC(section);
        LinkedHashSet<QueryIPQCVO> hashSet = new LinkedHashSet<>(list);
        ArrayList<QueryIPQCVO> newlist = new ArrayList<>(hashSet);
        /*queryIPQCVO.setQueryIPQCS(newlist);*/
        return Result.success(newlist);
    }

    @GetMapping("/queryZZ")
    @ApiOperation("通過課別查詢組長信息")
    public Result<ArrayList<QueryIPQCVO>> queryZZ(@RequestParam("section") String section){
        log.info("通過課別查詢組長信息",section);
        QueryZZVO queryZZVO = new QueryZZVO();
       ArrayList<QueryIPQCVO> list1 = comparation136Mapper.queryZZ(section);
        LinkedHashSet<QueryIPQCVO> hashSet = new LinkedHashSet<>(list1);
        ArrayList<QueryIPQCVO> newlist = new ArrayList<>(hashSet);
        /*queryZZVO.setQueryIPQCS(newlist);*/
        return Result.success(newlist);
    }

/*    @PostMapping("querymaterialNumber")
    @ApiOperation("通過課別、線體帶出料號信息")
    public Result<> querymaterialNumber(){
        return null;
    }*/


/*
    @GetMapping("/querymaterialNumber")
    @ApiOperation("136對比表查詢料號信息")
    public Result<ArrayList<String>> queryMaterialNumber(String CPLH_PFN){
        log.info("查詢產品料號：{}",CPLH_PFN);
        ArrayList<String> list = new ArrayList<>();
        list = comparation136Mapper.queryMaterialNumber(CPLH_PFN);
        LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
        ArrayList<String> newlist = new ArrayList<>(hashSet);
        return Result.success(newlist);
    }*/

    /*  @GetMapping("/queryDate")
    @ApiOperation("136對比表按日期查詢")
    public Result<ArrayList<String>> queryDate(String BD_Date){
        log.info("查詢產品料號：{}",BD_Date);
        ArrayList<String> list = new ArrayList<>();
        list = comparation136Mapper.queryDate(BD_Date);
        LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
        ArrayList<String> newlist = new ArrayList<>(hashSet);
        return Result.success(newlist);
    }*/


   /* *//**
     * 分页查询
     * @param comparation136QueryPage
     * @return
     *//*
    @PostMapping(value ="/page")
    @ApiOperation("136對比表分頁查詢接口")
    public Result<PageResult> page(@RequestBody Comparation136QueryPage comparation136QueryPage){
        log.info("136對比表分頁查詢:{}",comparation136QueryPage);
        PageResult pageResult = comparationService.pageQuery(comparation136QueryPage);
        return Result.success(pageResult);
    }*/


    @PostMapping("/page")
    @ApiOperation("136對比表按課別、班別、料號、日期、線體查詢")
    public Result<List<QueryInformationVO>> page(@RequestBody Comparation136QueryPage comparation136QueryPage){
        log.info("136對比表按課別、班別、料號、日期、線體查詢：{}",comparation136QueryPage);
        List<QueryInformationVO> list = comparation136Mapper.queryPageInformation(comparation136QueryPage);
        LinkedHashSet<QueryInformationVO> hashSet = new LinkedHashSet<>(list);
        List<QueryInformationVO> newlist = new ArrayList<>(hashSet);
        return Result.success(newlist);
    }


    @GetMapping(value = "/getById")
    @ApiOperation("136對比表通過id來查詢比對信息")
    public Result<QueryIdComparation136VO> getById(@RequestParam("id") Long id){
        log.info("根据id来查询136比对信息：{}",id);
        QueryIdComparation136VO queryIdComparation136VO = comparationService.getByIdWithInformation(id);
        return Result.success(queryIdComparation136VO);
    }

    @PostMapping(value ="/insertInformation")
    @ApiOperation("136對比表新增信息")
    public Result  saveComparationInformation(@RequestBody SaveComparationInformation saveComparationInformation) {
        log.info("新增136对比信息：{}",saveComparationInformation);
        comparationService.saveComparationInformation(saveComparationInformation);
        return Result.success("添加数据成功");
    }

    @PostMapping(value ="/checkComparation136")
    @ApiOperation("签核相关接口")
    public Result<CheckComparation136VO> checkComparation136 (@RequestBody CheckComparation136 checkComparation136) {
        log.info("签核相关接口：{}",checkComparation136);
        CheckComparation136VO checkComparation136VO = comparationService.checkComparation136(checkComparation136);
        return Result.success(checkComparation136VO);
    }

    @PostMapping(value = "/updateInformation")
    @ApiOperation("被驳回重新编辑相关接口")
    public Result updateComparationInformation(@RequestBody SaveComparationInformation saveComparationInformation){
        log.info("被驳回重新编辑相关接口:{}",saveComparationInformation);
        comparationService.updateComparationInformation(saveComparationInformation);
        return Result.success("重新编辑成功");
    }

    @PostMapping("/test")
    @ApiOperation("測試返回值接口")
    public Result<List<QueryInformationVO>> test(@RequestBody Comparation136QueryPage comparation136QueryPage){
        log.info("136對比表按課別、班別、料號、日期、線體查詢：{}",comparation136QueryPage);
        List<QueryInformationVO> list = comparation136Mapper.queryPageInformation(comparation136QueryPage);
        LinkedHashSet<QueryInformationVO> hashSet = new LinkedHashSet<>(list);
        List<QueryInformationVO> newlist = new ArrayList<>(hashSet);
        return Result.data(newlist);
    }


    @GetMapping("/queryWorkId")
    @ApiOperation("根據工號推送給需要帶簽核的人員")
    public Result<List<QueryWork>> queryWorkId(@RequestParam("id") Long id){
        log.info("通過工號推送給相關人員：{}",id);
        List<QueryWork> list = comparation136Mapper.queryWorkId(id);
        return Result.success(list);
    }

    @GetMapping("")
    @ApiOperation("查詢駁回人員")
    public Result<List<TypePerson>> queryType(@RequestParam("id") Long id){
        log.info("查詢駁回人員：{}",id);
        List<TypePerson> list = comparation136Mapper.queryType(id);
        LinkedHashSet<TypePerson> hashSet = new LinkedHashSet<>(list);
        List<TypePerson> newlist = new ArrayList<>(hashSet);
        return Result.success(newlist);
    }

}
