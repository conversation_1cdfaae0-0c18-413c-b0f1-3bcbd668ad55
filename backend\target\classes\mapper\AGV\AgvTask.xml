<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.agv.dao.AGVTaskListMapper">


    <select id="queryAGVTaskList" resultType="cc.mrbird.febs.agv.entity.AGVTaskList"
            parameterType="cc.mrbird.febs.agv.param.AGVTaskListQueryParam">
        SELECT
        [id]              as id,
        [RobotId]          as robotId,
        [TaskCreateUserName]            as taskCreateUserName,
        [TaskCreateUserID]      as taskCreateUserId           ,
        [TaskCreateDate]            as taskCreateDate,
        [SL_SiteID]            as slSiteId,
        [SL_SiteName]           as slSiteName,
        [XL_SiteID]              as xlSiteId,
        [XL_SiteName]          as xlSiteName,
        [TaskStatus]            as taskStatus,
        [ReturnInfo]      as returnInfo           ,
        [STATUS]            as status
        FROM [APPGCL_Process].[dbo].[AGV_TaskList]
        <where>
            <if test="robotId != null and robotId != ''">
                AND RobotId = #{robotId}
            </if>
            <if test="taskCreateUserName != null and taskCreateUserName != ''">
                AND TaskCreateUserName = #{taskCreateUserName}
            </if>
            <if test="taskCreateUserId != null and taskCreateUserId != ''">
                AND TaskCreateUserID = #{taskCreateUserId}
            </if>
            <if test="taskCreateDate != null and taskCreateDate != ''">
                AND  DateDIFF(day,TaskCreateDate,#{taskCreateDate}) =0
            </if>
        </where>
    </select>

</mapper>