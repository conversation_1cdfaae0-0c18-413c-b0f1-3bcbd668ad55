package cc.mrbird.febs.nightDuty.entity;

import cc.mrbird.febs.common.annotation.NotBlank;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "值夜信息表")
public class NightDutyInfo implements Serializable {

    //地點ID
    private Integer ID;

    @ExcelProperty({"值夜信息","樓棟"})
    private String building;

    @ExcelProperty({"值夜信息","樓層"})
    private String floor;

    @ExcelProperty( {"值夜信息","區域"})
    private String area;

    @ExcelProperty({"值夜信息","工號"})
    private String workId;

    @ExcelProperty({"值夜信息","名字"})
    private String name;

    @ExcelProperty({"值夜信息","簽到狀態"})
    private String status;

    @ExcelProperty({"值夜信息","簽到時間"})
    private String time;

    @ExcelProperty({"值夜信息","是否異常"} )
    private String NGStatus;

    @ExcelProperty({"值夜信息","異常圖片1"} )
    private String picture1;

    @ExcelProperty({"值夜信息","異常圖片2"} )
    private String picture2;

    private List<String> picture = new ArrayList<>();

    @ExcelProperty({"值夜信息","異常原因"})
    private String description;

    @ExcelProperty({"值夜信息","異常部門"})
    private String department;

    @ExcelProperty({"值夜信息","責任主管"})
    private String header;


}
