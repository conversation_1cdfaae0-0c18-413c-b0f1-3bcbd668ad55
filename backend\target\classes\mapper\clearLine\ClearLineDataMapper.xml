<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.clearLine.dao.ClearLineDataMapper">
    <select id="selectProduction" resultType="java.lang.String">
        USE IDS_BK_PaperLessSystem
        SELECT DISTINCT production
        FROM t_data_clearLine
        WHERE pl = #{pl}
          AND discern = #{discern,jdbcType=VARCHAR}
          AND production != ''
    </select>

    <select id="selectProductionLine" resultType="java.lang.String">
        USE IDS_BK_PaperLessSystem
        SELECT DISTINCT productionLine
        FROM t_data_clearLine
        WHERE discern = #{discern}
          AND production = #{production}
    </select>

    <insert id="addSheet">
        USE IDS_BK_PaperLessSystem
        INSERT INTO t_data_clearLine_checkItem( id
                                              , tableTopByLine, groundByLine, defectiveBoxByLine
                                              , materialContainerByLine, tagByLine
                                              , manufacturingFixtureByLine, offLineFixtureByLine
                                              , offLineWorkStationByLine, badMaintainByLine
                                              , projectDrawingFileByLine, auxiliaryMaterialsByLine
                                              , materialStagingAreaByLine, productPackagingByLine, productDisplayByLine
                                              , tableTopByQC, groundByQC, defectiveBoxByQC
                                              , materialContainerByQC, tagByQC
                                              , manufacturingFixtureByQC, offLineFixtureByQC
                                              , offLineWorkStationByQC, badMaintainByQC
                                              , projectDrawingFileByQC, auxiliaryMaterialsByQC
                                              , materialStagingAreaByQC, productPackagingByQC, productDisplayByQC)
        values (#{id}, #{tableTopByLine}, #{groundByLine}, #{defectiveBoxByLine}, #{materialContainerByLine},
                #{tagByLine},
                #{manufacturingFixtureByLine},
                #{offLineFixtureByLine}, #{offLineWorkStationByLine}, #{badMaintainByLine}, #{projectDrawingFileByLine},
                #{auxiliaryMaterialsByLine},
                #{materialStagingAreaByLine}, #{productPackagingByLine}, #{productDisplayByLine}, #{tableTopByQC},
                #{groundByQC}, #{defectiveBoxByQC}, #{materialContainerByQC}, #{tagByQC},
                #{manufacturingFixtureByQC},
                #{offLineFixtureByQC}, #{offLineWorkStationByQC}, #{badMaintainByQC}, #{projectDrawingFileByQC},
                #{auxiliaryMaterialsByQC},
                #{materialStagingAreaByQC}, #{productPackagingByQC}, #{productDisplayByQC})
    </insert>

    <insert id="addSheetByPointData">
        USE IDS_BK_PaperLessSystem
        INSERT INTO t_data_clearLine(pl, discern, production, productionLine, recordDate, startTime, endTime,
                                     clearLineTime, oldOrder, newOrder, oldPartNumber, newPartNumber, oldState,
                                     newState, remark, linkId, qh1, qh2, qh3, stNo, qhStatus, showId)
        values (#{pl}, #{discern}, #{production}, #{productionLine}, #{recordDate}, #{startTime}, #{endTime},
                #{clearLineTime}, #{oldOrder}, #{newOrder}, #{oldPartNumber}, #{newPartNumber}, #{oldState},
                #{newState}, #{remark}, #{linkId}, #{qh1}, #{qh2}, #{qh3}, #{stNo}, #{qhStatus}, #{showId})
    </insert>

    <select id="selectAllDataPojo" resultType="cc.mrbird.febs.clearLine.entity.pojo.SelectAllDataPojo">
        USE IDS_BK_PaperLessSystem
        SELECT DISTINCT *
        FROM t_data_clearLine AS a
                 inner join t_data_clearLine_checkItem AS b
                            ON a.linkId = b.id   <!--left join t_data_clearline_PicUrl as c ON a.id=c.mainInfoId-->
        <where>
            <if test="pl != null and pl != ''">
                AND pl=#{pl}
            </if>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND recordDate >= CONVERT(date, #{startDate})
                      AND recordDate &lt;= CONVERT(date, #{endDate})
            </if>
            <if test="production != null and production != ''">
                AND production = #{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND productionLine = #{productionLine}
            </if>
            <if test="linkId != null and linkId != ''">
                AND linkId = #{linkId}
            </if>
            <if test="discern != null and discern != ''">
                AND discern=#{discern}
            </if>
        </where>
        ORDER BY a.id DESC
    </select>

    <select id="selectQC" resultType="java.lang.String">
        USE Schedule
        SELECT name
        FROM t_configure_backwork_audit
        WHERE pl = #{pl}
          AND role = '2'
    </select>

    <select id="selectSection" resultType="java.lang.String">
        USE Schedule
        SELECT name
        FROM t_configure_backwork_audit
        WHERE pl = #{pl}
          AND role = '4'
    </select>

    <select id="selectCheckData" resultType="cc.mrbird.febs.clearLine.entity.TDataClearLineCheckByData">
        USE IDS_BK_PaperLessSystem
        SELECT qh1, qh2, qh3, qhStatus, stNo
        FROM t_data_clearLine
        WHERE id = #{id}
    </select>

    <update id="updateQhStatus">
        USE IDS_BK_PaperLessSystem
        UPDATE t_data_clearLine
        <set>
            qhStatus =#{qhStatus},
            stNo=#{stNo},
            showId=#{showId}
            WHERE id = #{id}
        </set>
    </update>

    <select id="selectShowByName" resultType="java.lang.String">
        USE Schedule
        SELECT workId
        FROM t_configure_backwork_audit
        WHERE name = #{name}
    </select>

    <update id="updateAllDateByMastData">
        USE IDS_BK_PaperLessSystem
        UPDATE t_data_clearLine
        <set>
            recordDate=#{recordDate},
            startTime=#{startTime},
            endTime=#{endTime},
            clearLineTime=#{clearLineTime},
            oldOrder=#{oldOrder},
            newOrder=#{newOrder},
            oldPartNumber=#{oldPartNumber},
            newPartNumber=#{newPartNumber},
            oldState=#{oldState},
            newState=#{newState},
            remark=#{remark},
            linkId=#{linkId},
            qh1=#{qh1},
            qh2=#{qh2},
            qh3=#{qh3},
            stNo=#{stNo},
            qhStatus=#{qhStatus},
            showId=#{showId}
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateAllDateBySalveData">
        USE IDS_BK_PaperLessSystem
        UPDATE t_data_clearLine_checkItem
        <set>

                tableTopByLine=#{tableTopByLine}, groundByLine=#{groundByLine}, defectiveBoxByLine=#{defectiveBoxByLine}
            , materialContainerByLine=#{materialContainerByLine}, tagByLine=#{tagByLine}
            , manufacturingFixtureByLine=#{manufacturingFixtureByLine}, offLineFixtureByLine=#{offLineFixtureByLine}
            , offLineWorkStationByLine=#{offLineWorkStationByLine}, badMaintainByLine=#{badMaintainByLine}
            , projectDrawingFileByLine=#{projectDrawingFileByLine}, auxiliaryMaterialsByLine=#{auxiliaryMaterialsByLine}
            , materialStagingAreaByLine=#{materialStagingAreaByLine}, productPackagingByLine=#{productPackagingByLine}, productDisplayByLine=#{productDisplayByLine}
            , tableTopByQC=#{tagByQC}, groundByQC=#{groundByQC}, defectiveBoxByQC=#{defectiveBoxByQC}
            , materialContainerByQC=#{materialContainerByQC}, tagByQC=#{tagByQC}
            , manufacturingFixtureByQC=#{manufacturingFixtureByQC}, offLineFixtureByQC=#{offLineFixtureByQC}
            , offLineWorkStationByQC=#{offLineWorkStationByQC}, badMaintainByQC=#{badMaintainByQC}
            , projectDrawingFileByQC=#{projectDrawingFileByQC}, auxiliaryMaterialsByQC=#{auxiliaryMaterialsByQC}
            , materialStagingAreaByQC=#{materialStagingAreaByQC}, productPackagingByQC=#{productPackagingByQC}, productDisplayByQC=#{productDisplayByQC}
        </set>  WHERE id=#{id}
    </update>

    <update id="goBackByQC" >
        USE IDS_BK_PaperLessSystem
        update t_data_clearLine
        <set>
            stNo='1',qhStatus='1',showId=#{param2},backMark=#{param3}
        </set>
        WHERE id =#{param1}
    </update>

    <select id="selectQh1" resultType="java.lang.String">
        USE IDS_BK_PaperLessSystem
        select qh1 from t_data_clearLine where id=#{id}
    </select>

    <select id="selectLineLeaderNumber" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT LineLeaderNumber FROM T_Configure_Equipment WHERE LineLeaderName=#{name}
    </select>

    <select id="selectGroupLeaderNumber" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT GroupLeaderNumber FROM T_Configure_Equipment WHERE LineLeaderName=#{name}
    </select>

    <select id="selectGroupLeader" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT GroupLeaderName FROM T_Configure_Equipment where PL=#{param1,jdbcType=VARCHAR} AND GroupLeaderName is not null AND GroupLeaderName !='/' AND Discern=#{param2,jdbcType=VARCHAR}
    </select>

    <select id="selectQCByIDS3" resultType="java.lang.String">
        USE Schedule
        SELECT name
        FROM t_configure_backwork_audit
        WHERE pl = #{pl}
        AND role = '3'
    </select>

    <select id="selectQCByIDS1" resultType="java.lang.String">
    </select>

    <select id="selectClearLineByPicURL" resultType="cc.mrbird.febs.clearLine.entity.pojo.PicURL">
        USE IDS_BK_PaperLessSystem
        SELECT picUrl+'/'+picName AS picUrl,stationName
        FROM [IDS_BK_PaperLessSystem].[dbo].[t_data_clearline_PicUrl]
        where mainInfoId=#{mainInfoId}
        ORDER BY picOrder ASC
    </select>

    <select id="selectProductionByAdd" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT Production
        FROM T_Configure_Equipment
        WHERE PL = #{pl}
        AND Discern = #{discern,jdbcType=VARCHAR}
        AND Production != ''
    </select>

    <select id="selectProductionLineByAdd" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT ProductionLine
        FROM T_Configure_Equipment
        WHERE PL = #{pl}
        AND Discern = #{discern,jdbcType=VARCHAR}
        AND Production != ''
    </select>

    <select id="selectQhNumber" resultType="java.lang.String">
        USE Schedule
        SELECT CardId  FROM T_Data_Group WHERE UserName=#{name,jdbcType=VARCHAR}
    </select>
</mapper>