package cc.mrbird.febs.wxby.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "T_Data_Group")
public class TGroupDate {
    @TableField(value = "UserName")
    private String userName;
    @TableField(value = "CardId")
    private String cardId;
    @TableField(value = "GroupName")
    private String groupName;
    @TableField(value = "RoleName")
    private String roleName;
    @TableField(value = "RoleId")
    private String roleId;
}
