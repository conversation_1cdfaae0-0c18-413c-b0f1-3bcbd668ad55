package cc.mrbird.febs.line.service;

import cc.mrbird.febs.line.dao.LineMapper;
import cc.mrbird.febs.line.entity.LineInfo;
import cc.mrbird.febs.line.entity.LineInfos;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ILineServiceImpl implements ILineService{
    @Autowired
    private LineMapper lineMapper;



    @Override
    public String queryNameByCardId(String cardId) {
        return lineMapper.queryName(cardId);
    }
}
