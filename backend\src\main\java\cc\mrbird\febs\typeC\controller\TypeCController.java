package cc.mrbird.febs.typeC.controller;

import cc.mrbird.febs.common.domain.FebsResponse;

import cc.mrbird.febs.typeC.dao.TypeCMapper;
import cc.mrbird.febs.typeC.entity.qo.ProductionAndProductionLineQo;
import cc.mrbird.febs.typeC.entity.qo.TableColumnName;
import cc.mrbird.febs.typeC.service.TypeCCommMapper;
import cc.mrbird.febs.typeC.service.impl.TypeCCommMapperImpl;
import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@ResponseBody
@Api(tags = "TypeC网页版数据展示")
@Slf4j
public class TypeCController {

    private final TypeCMapper typeCMapper;
    private final TypeCCommMapper typeCCommMapper;

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public TypeCController(TypeCMapper typeCMapper, TypeCCommMapperImpl typeCCommMapper, JdbcTemplate jdbcTemplate) {
        this.typeCMapper = typeCMapper;
        this.typeCCommMapper = typeCCommMapper;
        this.jdbcTemplate = jdbcTemplate;
    }


   /* @GetMapping("/selectAllTable")
    @ApiOperation(value = "获取所有数据表名称")
    public FebsResponse selectAllTable(){
        List<TableNameDto> list = typeCMapper.selectAllTable();
        return  new FebsResponse().code("200").data(list);
    }*/

    @GetMapping("/selectProduction")
    @ApiOperation(value = "获取系列")
    public FebsResponse selectProduction(){
        List<String> list = typeCMapper.selectAllProduction();
        return  new FebsResponse().code("200").data(list);
    }

    //根据系列获取客户
    @GetMapping("/selectCustomerName")
    @ApiOperation("/根据系列获取客户名称")
    public FebsResponse selectCustomerName(@RequestParam("production")String production){
        List<String> list =typeCMapper.selectCustomerName(production);
        return  new FebsResponse().code("200").data(list);
    }

    @GetMapping("/selectAllLineByProduction")
    @ApiOperation(value = "根据系列、客户名称获取线体")
    public FebsResponse selectAllLineByProduction(@RequestParam("production")String production,
                                                  @RequestParam("customerName")String customerName){
        List<String> list = typeCMapper.selectAllLineByProduction(production,customerName);
        return  new FebsResponse().code("200").data(list);
    }

    /*根据系列线体查询对应表*/
    @PostMapping("/selectSheetNameByProductionAndProductionLine")
    @ApiOperation("根据系列、客户名称、线体查询对应的表名称")
    public FebsResponse selectSheetNameByProductionAndProductionLine(@RequestBody ProductionAndProductionLineQo qo){
        List<String> list =typeCMapper.selectAllSheetName(qo);
        return  new FebsResponse().code("200").data(list);
    }


    @PostMapping("/selectDataByTableName")
    @ApiOperation(value = "根据客户名称、表名称或者视图名称查询对应的全部数据",notes = "参数值为表名称或者视图名称")
    public  FebsResponse selectDataByTableName(@RequestBody TableColumnName tableColumnName){
        try{
            String result=null;
            boolean b=isNull(tableColumnName.getStartTime(), tableColumnName.getEndTime());
           String customerName = tableColumnName.getCustomerName();
           if (tableColumnName.getEndTime() != null && !tableColumnName.getEndTime().equals("") && tableColumnName.getEndTime().length()!=0) {
               String dateStr = tableColumnName.getEndTime();
               DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
               LocalDate date = LocalDate.parse(dateStr, formatter);
               // 对日期进行加一天操作
               date = date.plusDays(1);
               // 将LocalDate转回String
               result = date.format(formatter);
           }
           // System.out.println("加了一天的的日期为:-------->"+result);
            List<Map<String, Object>> list = null;
            if (customerName.equals("A客戶")&& b==true){
                tableColumnName.setEndTime(result);
                //A客户
               // System.out.println("我運行了1111");
                list = typeCCommMapper.getTableData1(jdbcTemplate,tableColumnName);
            }else if (customerName.equals("非A客戶") && b==true){
                tableColumnName.setEndTime(result);
               // System.out.println("2222");
                //非A客户
                list = typeCCommMapper.getTableData2(jdbcTemplate,tableColumnName);
            } else if (customerName.equals("A客戶") && !b){
                tableColumnName.setEndTime(result);
                //System.out.println("333");
                list = typeCCommMapper.getTableDataByA1000(jdbcTemplate,tableColumnName);
            }else if (customerName.equals("非A客戶") && !b){
                tableColumnName.setEndTime(result);
               // System.out.println("4444");
                list=typeCCommMapper.getTableDataByNotA1000(jdbcTemplate,tableColumnName);
            }
            return  new FebsResponse().code("200").data(list);
        }catch (Exception e){
            e.printStackTrace();
            log.info(e.toString());
        }
        return  new FebsResponse().code("400").message("错误的参数值，请检查。"+"传的参数值为:---->"+tableColumnName);
    }
    @PostMapping("/selectDataByTestNotA")
    public  FebsResponse selectDataByTestNotA(@RequestBody TableColumnName tableColumnName){
        List<Map<String,Object>> list =typeCCommMapper.getTableDataByTest(jdbcTemplate,tableColumnName);
        return  new FebsResponse().code("200").data(list);
    }



/*    @GetMapping("/getTableDataByCondition")
    @ApiOperation(value = "根据条件查询表数据",notes = "参数例子: xx字段 = ''   写成 xx=''    ")
    public FebsResponse getTableDataByCondition(@RequestParam("tableName")String tableName,@RequestParam("sql") String sql){
        List<Map<String,Object>> map=typeCCommoMapper.getTableDataByCondition(jdbcTemplate,tableName,sql);
        return  new FebsResponse().code("200").data(map);
    }*/




    @GetMapping("/selectAllViews")
    @ApiOperation("获取所有视图名称")
    public FebsResponse selectAllViews(){
        return new FebsResponse().code("200").data(typeCMapper.selectAllViews());
    }


    private boolean isNull(String startTime,String endTime) {
        if (startTime ==null && endTime==null) return false;
        if (startTime !=null&&startTime.length() != 0 && startTime.equals("") != true && endTime.length() != 0 && endTime.equals("") != true) {
            return true;
        } else {
            return false;
        }
    }


    //根据表、字段查询数据
 /*   @PostMapping("/selectDataByTableAndTime")
    @ApiOperation("根据表名、字段名查询数据")
    public FebsResponse selectDataByTableAndTime(){
        return new FebsResponse().code("200").data(null);
    }*/




   /* @PostMapping("/excel")
    @CrossOrigin(origins = "*")
    public void export(@RequestParam("tableName")String tableName,@RequestParam("sql") String sql, HttpServletResponse response) throws FebsException {
            try{
                if(sql.isEmpty()){
                    sql="1=1";
                }
                List<Map<String,Object>> map=typeCCommoMapper.getTableDataByCondition(jdbcTemplate,tableName,sql);
                exportListMapToExcelUseEasyExcel(response,map);
            }catch (Exception e){
                String message = "导出Excel失败";
                log.error(message, e);
                throw new FebsException(message);
            }
    }*/
    public void exportListMapToExcelUseEasyExcel(HttpServletResponse response, List<Map<String, Object>> dataList) {
        try {
            if(!dataList.isEmpty()){
                // 提取表头
                Map<String, Object> headMap = dataList.get(0);
                List<List<String>> list = new ArrayList<>();
                for (String key : headMap.keySet()) {
                    List<String> head = new ArrayList<>();
                    head.add(key);
                    list.add(head);
                }
                // 使用当前时间作为文件名
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                String fileName = LocalDateTime.now().format(formatter);
                //response.setContentType("application/vnd.ms-excel");
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("utf-8");
               // response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode(fileName, "UTF-8")+".xlsx");
                response.setHeader("Access-Control-Allow-Origin", "*");
                EasyExcel.write(response.getOutputStream()).head(list).sheet("Sheet1").doWrite(toListString(dataList));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public List<List<String>> toListString(List<Map<String,Object>> maps){
        List<List<String>> result = new ArrayList<>();
        for(Map<String,Object> map:maps){
            List<String> list = new ArrayList<>();
            for(String key:map.keySet()){
                list.add(map.get(key).toString());
            }
            result.add(list);
        }
        return result;
    }



}
