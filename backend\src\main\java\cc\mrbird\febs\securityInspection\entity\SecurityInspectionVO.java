package cc.mrbird.febs.securityInspection.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityInspectionVO {

    private Integer id;
    private String building;
    private String floor;
    private String station;
    private String name;
    private String date;
    private String status;
    private String description;
    private List<String> imgs;
    private String img1;
    private String img2;
    
    /**
     * 初始化imgs集合，将img1和img2添加到imgs集合中
     * 该方法会在getter方法被调用时执行
     * @return 包含img1和img2的imgs集合
     */
    public List<String> getImgs() {
        if (this.imgs == null) {
            this.imgs = new ArrayList<>();
        }
        
        // 清空集合，避免重复添加
        this.imgs.clear();
        
        // 将非空的img1添加到集合
        if (this.img1 != null && !this.img1.isEmpty()) {
            this.imgs.add(this.img1);
        }
        
        // 将非空的img2添加到集合
        if (this.img2 != null && !this.img2.isEmpty()) {
            this.imgs.add(this.img2);
        }
        
        return this.imgs;
    }
}
