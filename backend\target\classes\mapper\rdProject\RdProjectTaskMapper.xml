<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectTaskMapper">

    <!-- 查詢廠區列表 -->
    <select id="selectSiteAreaList" resultType="java.lang.String">
        SELECT DISTINCT site_area
        FROM rd_project_user_extra
        WHERE site_area IS NOT NULL
    </select>

    <!-- 根據廠區查詢產品処列表 -->
    <select id="selectSbuBySiteArea" resultType="java.lang.String">
        SELECT DISTINCT sbu
        FROM rd_project_user_extra
        WHERE site_area = #{siteArea}
        AND sbu IS NOT NULL
    </select>

    <!-- 根據廠區和產品処查詢機能列表 -->
    <select id="selectFunctionsBySiteAreaAndSbu" resultType="java.lang.String">
        SELECT DISTINCT functions
        FROM rd_project_user_extra
        WHERE site_area = #{siteArea} 
        AND sbu = #{sbu}
        AND functions IS NOT NULL
    </select>

    <!-- 根據廠區、產品処和機能查詢課別列表 -->
    <select id="selectSectionBySiteAreaAndSbuAndFunctions" resultType="java.lang.String">
        SELECT DISTINCT section
        FROM rd_project_user_extra
        WHERE site_area = #{siteArea} 
        AND sbu = #{sbu} 
        AND functions = #{functions}
        AND section IS NOT NULL
    </select>

    <select id="queryRdProjectTaskMainInfo" resultType="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo">
        SELECT
        task_id,
        parent_id,
        tree_path,
        site_area,
        sbu,
        functions,
        section,
        task_name,
        start_time,
        end_time,
        next_end_time,
        status,
        creator_worker_id
        FROM rd_project_task_main_info
        <where>
            <!-- 精确匹配条件 -->
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="creatorWorkerId != null and creatorWorkerId != ''">
                AND creator_worker_id = #{creatorWorkerId}
            </if>

            <!-- 模糊匹配条件 -->
            <if test="treePath != null and treePath != ''">
                AND tree_path LIKE CONCAT(#{treePath}, '%')
            </if>
            <if test="siteArea != null and siteArea != ''">
                AND site_area LIKE CONCAT('%', #{siteArea}, '%')
            </if>
            <if test="sbu != null and sbu != ''">
                AND sbu LIKE CONCAT('%', #{sbu}, '%')
            </if>
            <if test="functions != null and functions != ''">
                AND functions LIKE CONCAT('%', #{functions}, '%')
            </if>
            <if test="section != null and section != ''">
                AND section LIKE CONCAT('%', #{section}, '%')
            </if>
            <if test="taskName != null and taskName != ''">
                AND task_name LIKE CONCAT('%', #{taskName}, '%')
            </if>

            <!-- 日期范围查询 -->
            <if test="startTime != null">
                AND start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND end_time &lt;= #{endTime}
            </if>
            <if test="nextEndTime != null">
                AND next_end_time &lt;= #{nextEndTime}
            </if>
        </where>
        ORDER BY end_time desc
    </select>

    <select id="selectTasksByWorkerId" resultType="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo">
        SELECT DISTINCT
        main.task_id,
        main.parent_id,
        main.tree_path,
        main.site_area,
        main.sbu,
        main.functions,
        main.section,
        main.task_name,
        main.start_time,
        main.end_time,
        main.next_end_time,
        main.status,
        main.creator_worker_id
        FROM
        rd_project_task_main_info main
        JOIN
        rd_project_task_assignments assign
        ON main.task_id = assign.task_id
        LEFT JOIN
        rd_project_team_user team_user
        ON assign.assignee_type = 1 AND assign.assignee_id = CAST(team_user.team_id AS CHAR)
        <where>
            <!-- 工号/团队任务查询条件 -->
            (
            (assign.assignee_type = 0 AND assign.assignee_id = #{workerId})
            OR
            (assign.assignee_type = 1 AND team_user.worker_id = #{workerId})
            )

            <!-- 封装在condition对象中的多条件查询 -->
            <if test="condition.siteArea != null and condition.siteArea != ''">
                AND main.site_area = #{condition.siteArea}
            </if>
            <if test="condition.sbu != null and condition.sbu != ''">
                AND main.sbu = #{condition.sbu}
            </if>
            <if test="condition.functions != null and condition.functions != ''">
                AND main.functions = #{condition.functions}
            </if>
            <if test="condition.section != null and condition.section != ''">
                AND main.section = #{condition.section}
            </if>
            <if test="condition.taskName != null and condition.taskName != ''">
                AND main.task_name LIKE CONCAT('%', #{condition.taskName}, '%')
            </if>
            <if test="condition.status != null">
                AND main.status = #{condition.status}
            </if>
            <if test="condition.creatorWorkerId != null and condition.creatorWorkerId != ''">
                AND main.creator_worker_id = #{condition.creatorWorkerId}
            </if>

            <!-- 日期范围查询 -->
            <if test="condition.startTime != null">
                AND start_time >= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                AND end_time &lt;= #{condition.endTime}
            </if>
            <if test="condition.nextEndTime != null">
                AND next_end_time &lt;= #{condition.nextEndTime}
            </if>
        </where>
        ORDER BY main.end_time DESC
    </select>

    <select id="selectTaskTreesByWorkerId" resultType="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo">
        SELECT DISTINCT
        main.task_id,
        main.parent_id,
        main.tree_path,
        main.site_area,
        main.sbu,
        main.functions,
        main.section,
        main.task_name,
        main.start_time,
        main.end_time,
        main.next_end_time,
        main.status,
        main.creator_worker_id
        FROM
        rd_project_task_main_info main
        WHERE
        EXISTS (
        SELECT 1
        FROM (
        <!-- 获取所有相关任务的根节点ID -->
        SELECT DISTINCT
        SUBSTRING_INDEX(main_tree.tree_path, ',', 1) AS root_task_id
        FROM
        rd_project_task_main_info main_tree
        JOIN
        rd_project_task_assignments assign_tree
        ON main_tree.task_id = assign_tree.task_id
        LEFT JOIN
        rd_project_team_user team_user_tree
        ON (assign_tree.assignee_type = 1 AND assign_tree.assignee_id = CAST(team_user_tree.team_id AS CHAR))
        WHERE
        (
        (assign_tree.assignee_type = 0 AND assign_tree.assignee_id = #{workerId})
        OR
        (assign_tree.assignee_type = 1 AND team_user_tree.worker_id = #{workerId})
        )
        ) related_roots
        <!-- 匹配任务树路径 -->
        WHERE main.tree_path LIKE CONCAT(related_roots.root_task_id, ',%')
        OR main.tree_path = related_roots.root_task_id
        )
        <!-- 多条件查询 -->
        <if test="condition.siteArea != null and condition.siteArea != ''">
            AND main.site_area = #{condition.siteArea}
        </if>
        <if test="condition.sbu != null and condition.sbu != ''">
            AND main.sbu = #{condition.sbu}
        </if>
        <if test="condition.functions != null and condition.functions != ''">
            AND main.functions = #{condition.functions}
        </if>
        <if test="condition.section != null and condition.section != ''">
            AND main.section = #{condition.section}
        </if>
        <if test="condition.taskName != null and condition.taskName != ''">
            AND main.task_name LIKE CONCAT('%', #{condition.taskName}, '%')
        </if>
        <if test="condition.status != null">
            AND main.status = #{condition.status}
        </if>
        <if test="condition.creatorWorkerId != null and condition.creatorWorkerId != ''">
            AND main.creator_worker_id = #{condition.creatorWorkerId}
        </if>
        <!-- 日期范围查询 -->
        <if test="condition.startTime != null">
            AND start_time >= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
            AND end_time &lt;= #{condition.endTime}
        </if>
        <if test="condition.nextEndTime != null">
            AND next_end_time &lt;= #{condition.nextEndTime}
        </if>
        ORDER BY main.end_time desc
    </select>

    <select id="selectBatchByTaskIds" resultType="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo">
        SELECT
        task_id, parent_id, tree_path, site_area, sbu, functions, section,
        task_name, start_time, end_time, next_end_time, status, creator_worker_id
        FROM rd_project_task_main_info
        WHERE task_id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        ORDER BY task_id asc
    </select>

    <select id="selectTaskTreeRootIdsByWorkerId" resultType="java.lang.String">
        SELECT DISTINCT
        SUBSTRING_INDEX(main.tree_path, ',', 1) AS root_task_id  -- 提取tree_path第一个ID
        FROM
        rd_project_task_main_info main
        WHERE
        EXISTS (
        <!-->查詢出該工號對應的所有任務節點<-->
        SELECT 1
        FROM (
        SELECT DISTINCT
        SUBSTRING_INDEX(main_tree.tree_path, ',', 1) AS root_task_id
        FROM
        rd_project_task_main_info main_tree
        JOIN
        rd_project_task_assignments assign_tree
        ON main_tree.task_id = assign_tree.task_id
        LEFT JOIN
        rd_project_team_user team_user_tree
        ON (assign_tree.assignee_type = 1 AND assign_tree.assignee_id = CAST(team_user_tree.team_id AS CHAR))
        WHERE
        (
        (assign_tree.assignee_type = 0 AND assign_tree.assignee_id = #{workerId})
        OR
        (assign_tree.assignee_type = 1 AND team_user_tree.worker_id = #{workerId})
        )
        ) related_roots
        WHERE
        main.tree_path LIKE CONCAT(related_roots.root_task_id, ',%')
        OR main.tree_path = related_roots.root_task_id
        )
        <!-- 多条件查询 -->
        <if test="condition.siteArea != null and condition.siteArea != ''">
            AND main.site_area = #{condition.siteArea}
        </if>
        <if test="condition.sbu != null and condition.sbu != ''">
            AND main.sbu = #{condition.sbu}
        </if>
        <if test="condition.functions != null and condition.functions != ''">
            AND main.functions = #{condition.functions}
        </if>
        <if test="condition.section != null and condition.section != ''">
            AND main.section = #{condition.section}
        </if>
        <if test="condition.taskName != null and condition.taskName != ''">
            AND main.task_name LIKE CONCAT('%', #{condition.taskName}, '%')
        </if>
        <if test="condition.status != null">
            AND main.status = #{condition.status}
        </if>
        <if test="condition.creatorWorkerId != null and condition.creatorWorkerId != ''">
            AND main.creator_worker_id = #{condition.creatorWorkerId}
        </if>
        <!-- 日期范围查询 -->
        <if test="condition.startTime != null">
            AND start_time >= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
            AND end_time &lt;= #{condition.endTime}
        </if>
        <if test="condition.nextEndTime != null">
            AND next_end_time &lt;= #{condition.nextEndTime}
        </if>
        ORDER BY main.end_time DESC
    </select>

    <select id="selectTaskTreeRootIds" resultType="java.lang.String">
        SELECT DISTINCT
        SUBSTRING_INDEX(main.tree_path, ',', 1) AS root_task_id  -- 提取tree_path第一个ID
        FROM
        rd_project_task_main_info main
        <where>
        <!-- 多条件查询 -->
        <if test="condition.siteArea != null and condition.siteArea != ''">
            AND main.site_area = #{condition.siteArea}
        </if>
        <if test="condition.sbu != null and condition.sbu != ''">
            AND main.sbu = #{condition.sbu}
        </if>
        <if test="condition.functions != null and condition.functions != ''">
            AND main.functions = #{condition.functions}
        </if>
        <if test="condition.section != null and condition.section != ''">
            AND main.section = #{condition.section}
        </if>
        <if test="condition.taskName != null and condition.taskName != ''">
            AND main.task_name LIKE CONCAT('%', #{condition.taskName}, '%')
        </if>
        <if test="condition.status != null">
            AND main.status = #{condition.status}
        </if>
        <if test="condition.creatorWorkerId != null and condition.creatorWorkerId != ''">
            AND main.creator_worker_id = #{condition.creatorWorkerId}
        </if>
        <!-- 日期范围查询 -->
        <if test="condition.startTime != null">
            AND start_time >= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
            AND end_time &lt;= #{condition.endTime}
        </if>
        <if test="condition.nextEndTime != null">
            AND next_end_time &lt;= #{condition.nextEndTime}
        </if>
        </where>
        ORDER BY main.end_time DESC
    </select>

    <select id="selectTaskTreesByRootIds" resultType="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo">
        SELECT DISTINCT
        main.task_id,
        main.parent_id,
        main.tree_path,
        main.site_area,
        main.sbu,
        main.functions,
        main.section,
        main.task_name,
        main.start_time,
        main.end_time,
        main.next_end_time,
        main.status,
        main.creator_worker_id
        FROM
        rd_project_task_main_info main
        WHERE
        <!-- 直接使用传入的ID集合匹配根节点 -->
        <choose>
            <!-- 处理ids集合为空的情况 -->
            <when test="ids == null or ids.isEmpty()">
                1 = 0  <!-- 返回空结果集 -->
            </when>
            <otherwise>
                <!-- 使用foreach遍历ids集合 -->
                (
                <foreach collection="ids" item="id" separator=" OR ">
                    main.tree_path LIKE CONCAT(#{id}, ',%')
                    OR main.tree_path = #{id}
                </foreach>
                )
            </otherwise>
        </choose>
        <!-- 多条件查询 -->
        <if test="condition.siteArea != null and condition.siteArea != ''">
            AND main.site_area = #{condition.siteArea}
        </if>
        <if test="condition.sbu != null and condition.sbu != ''">
            AND main.sbu = #{condition.sbu}
        </if>
        <if test="condition.functions != null and condition.functions != ''">
            AND main.functions = #{condition.functions}
        </if>
        <if test="condition.section != null and condition.section != ''">
            AND main.section = #{condition.section}
        </if>
        <if test="condition.taskName != null and condition.taskName != ''">
            AND main.task_name LIKE CONCAT('%', #{condition.taskName}, '%')
        </if>
        <if test="condition.status != null">
            AND main.status = #{condition.status}
        </if>
        <if test="condition.creatorWorkerId != null and condition.creatorWorkerId != ''">
            AND main.creator_worker_id = #{condition.creatorWorkerId}
        </if>
        <!-- 日期范围查询 -->
        <if test="condition.startTime != null">
            AND main.start_time >= #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
            AND main.end_time &lt;= #{condition.endTime}
        </if>
        <if test="condition.nextEndTime != null">
            AND main.next_end_time &lt;= #{condition.nextEndTime}
        </if>
        ORDER BY main.end_time DESC
    </select>


</mapper>
