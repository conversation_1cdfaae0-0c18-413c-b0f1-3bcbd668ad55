package cc.mrbird.febs.nightDuty.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.net.URL;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("值夜信息表")
public class NightDutyInfoVo implements Serializable {


    @ExcelProperty({"值夜記錄","樓棟"})
    private String building;

    @ExcelProperty({"值夜記錄","樓層"})
    private String floor;

    @ExcelProperty( {"值夜記錄","區域"})
    private String area;

    @ExcelProperty({"值夜記錄","工號"})
    private String workId;

    @ExcelProperty({"值夜記錄","名字"})
    private String name;

    @ExcelProperty({"值夜記錄","簽到狀態"})
    private String status;

    @ExcelProperty({"值夜記錄","簽到時間"})
    private String time;

    @ExcelProperty({"值夜記錄","是否異常"} )
    private String NGStatus;

    @ExcelProperty({"值夜記錄","異常圖片1"} )
    private URL picture1;

    @ExcelProperty({"值夜記錄","異常圖片2"} )
    private URL picture2;

    @ExcelProperty({"值夜記錄","異常原因"})
    private String description;

    @ExcelProperty({"值夜記錄","異常部門"})
    private String department;

    @ExcelProperty({"值夜記錄","責任主管"})
    private String header;


}
