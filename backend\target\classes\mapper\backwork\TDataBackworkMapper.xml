<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.backwork.dao.TDataBackworkMapper">
    <select id="selectByproductionLike" resultType="cc.mrbird.febs.backwork.entity.TDataBackwork">
        USE Schedule
        select
            id,uuid
             ,production
             ,recordDate
             ,backWorkUnit
             ,backWorkType
             ,partNumber
             ,dc
             ,backWorkNumber
             ,backWorkCause
             ,backWorkQuantity
             ,nod
             ,backWorkHour
             ,backWorkMethod
             ,backWorkUser
             ,backWorkLimit
             ,remark
             ,yesUser
             ,disUser
             ,nqTalk
             ,lsTalk
             ,trainer
             ,completionTime
             ,why1Cause
             ,why2Cause
             ,why3Cause
             ,why4Cause
             ,why5Cause
             ,why1Outflow
             ,why2Outflow
             ,why3Outflow
             ,why4Outflow
             ,why5Outflow
             ,itrackingWhy
             ,picWhy
             ,exTimeWhy
             ,endTimeWhy
             ,sMethod
             ,insItem
             ,aNumber
             ,naNumber
             ,naTalk
             ,moment
             ,qh1
             ,qh2
             ,qh3
             ,fileId
             ,itrackingOut
             ,picOut
             ,exTimeOut
             ,endTimeOut,fileName,filePath,laborCost,processCost,cancellationCost,qh4,workId,qhStatus,qh5,stNo,lineLeaderName,zrqc,qh6,isOther,pl,isBack,goBackWhy
        from t_data_backwork   WHERE uuid=#{uuid}
    </select>
    <select id="queryByPartNumber" resultType="cc.mrbird.febs.backwork.entity.TDataBackwork">
        USE Schedule
        SELECT  uuid,partNumber,dc,fileId,backWorkCause,stNo,backWorkUnit,cancellationCost,recordDate,qh1,qh2,qh3,qh4,qh5,qhStatus,showId,isBack,goBackWhy FROM t_data_backwork
        <where>
            <if test ="partNumber != null and partNumber !=''">
                AND partNumber = #{partNumber}
            </if>
            <if test="recordDate !=null and recordDate !=''">
                AND recordDate = #{recordDate}
            </if>
            AND pl=#{pl}
       <!-- AND id !=227-->
        </where>
        ORDER BY  id desc
    </select>
    <select id="selectAllPartNumber" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT partNumber FROM  t_data_backwork
    </select>
    <select id="selectByFileName" resultType="java.lang.String">
        USE Schedule
        SELECT fileName FROM t_data_backwork WHERE id=#{id}
    </select>
    <insert id="insertSelective">
        USE Schedule
        insert into t_data_backwork
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uuid != null ">uuid,</if>
           <if test="production != null">production,</if>
            <if test="productionline != null">productionLine,</if>
            <if test="recorddate != null">recordDate,</if>
            <if test="backworkunit != null">backWorkUnit,</if>
            <if test="backworktype != null">backWorkType,</if>
            <if test="partnumber != null">partNumber,</if>
            <if test="dc != null">dc,</if>
            <if test="backworknumber != null">backWorkNumber,</if>
            <if test="backworkcause != null">backWorkCause,</if>
            <if test="backworkquantity != null">backWorkQuantity,</if>
            <if test="nod != null">nod,</if>
            <if test="backworkhour != null">backWorkHour,</if>
            <if test="backworkmethod != null">backWorkMethod,</if>
            <if test="backworkuser != null">backWorkUser,</if>
            <if test="backworklimit != null">backWorkLimit,</if>
            <if test="remark != null">remark,</if>
            <if test="yesuser != null">yesUser,</if>
            <if test="disuser != null">disUser,</if>
            <if test="nqtalk != null">nqTalk,</if>
            <if test="lstalk != null">lsTalk,</if>
            <if test="trainer != null">trainer,</if>
            <if test="completiontime != null">completionTime,</if>
            <if test="why1cause != null">why1Cause,</if>
            <if test="why2cause != null">why2Cause,</if>
            <if test="why3cause != null">why3Cause,</if>
            <if test="why4cause != null">why4Cause,</if>
            <if test="why5cause != null">why5Cause,</if>
            <if test="why1outflow != null">why1Outflow,</if>
            <if test="why2outflow != null">why2Outflow,</if>
            <if test="why3outflow != null">why3Outflow,</if>
            <if test="why4outflow != null">why4Outflow,</if>
            <if test="why5outflow != null">why5Outflow,</if>
            <if test="itrackingWhy != null">itrackingWhy,</if>
            <if test="picWhy != null">picWhy,</if>
            <if test="extimeWhy != null">exTimeWhy,</if>
            <if test="endtimeWhy != null">endTimeWhy,</if>
            <if test="smethod != null">sMethod,</if>
            <if test="insitem != null">insItem,</if>
            <if test="anumber != null">aNumber,</if>
            <if test="nanumber != null">naNumber,</if>
            <if test="natalk != null">naTalk,</if>
            <if test="moment != null">moment,</if>
            <if test="qh1 != null">qh1,</if>
            <if test="qh2 != null">qh2,</if>
            <if test="qh3 != null">qh3,</if>
            <if test="fileId !=null">fileId,</if>
            <if test="itrackingOut != null">itrackingOut,</if>
            <if test="picOut != null">picOut,</if>
            <if test="exTimeOut != null">exTimeOut,</if>
            <if test="endTimeOut != null">endTimeOut,</if>
            <if test="fileName !=null">fileName,</if>
            <if test="filePath !=null">filePath,</if>
            <if test="laborCost !=null">laborCost,</if>
            <if test="processCost !=null">processCost,</if>
            <if test="cancellationCost !=null">cancellationCost,</if>
            <if test="qh4 !=null">qh4,</if>
            <if test="workId !=null">workId,</if>
            <if test="qhStatus !=null">qhStatus,</if>
            <if test="qh5 !=null">qh5,</if>
            <if test="stNo != null">stNo,</if>
            <if test="lineLeaderName != null">lineLeaderName,</if>
            <if test="zrqc != null">zrqc,</if>
            <if test="showId != null">showId,</if>
            <if test="pl != null">pl,</if>
            <if test="isOther != null">isOther,</if>
            <if test="qh6 != null">qh6,</if>
            <if test="isBack != null">isBack,</if>
            <if test="goBackWhy != null">goBackWhy,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uuid !=null">#{uuid,jdbcType=VARCHAR},</if>
           <if test="production != null">#{production,jdbcType=VARCHAR},</if>
            <if test="productionline != null">#{productionline,jdbcType=VARCHAR},</if>
            <if test="recorddate != null">#{recorddate,jdbcType=VARCHAR},</if>
            <if test="backworkunit != null">#{backworkunit,jdbcType=VARCHAR},</if>
            <if test="backworktype != null">#{backworktype,jdbcType=VARCHAR},</if>
            <if test="partnumber != null">#{partnumber,jdbcType=VARCHAR},</if>
            <if test="dc != null">#{dc,jdbcType=VARCHAR},</if>
            <if test="backworknumber != null">#{backworknumber,jdbcType=VARCHAR},</if>
            <if test="backworkcause != null">#{backworkcause,jdbcType=VARCHAR},</if>
            <if test="backworkquantity != null">#{backworkquantity,jdbcType=VARCHAR},</if>
            <if test="nod != null">#{nod,jdbcType=VARCHAR},</if>
            <if test="backworkhour != null">#{backworkhour,jdbcType=VARCHAR},</if>
            <if test="backworkmethod != null">#{backworkmethod,jdbcType=VARCHAR},</if>
            <if test="backworkuser != null">#{backworkuser,jdbcType=VARCHAR},</if>
            <if test="backworklimit != null">#{backworklimit,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="yesuser != null">#{yesuser,jdbcType=VARCHAR},</if>
            <if test="disuser != null">#{disuser,jdbcType=VARCHAR},</if>
            <if test="nqtalk != null">#{nqtalk,jdbcType=VARCHAR},</if>
            <if test="lstalk != null">#{lstalk,jdbcType=VARCHAR},</if>
            <if test="trainer != null">#{trainer,jdbcType=VARCHAR},</if>
            <if test="completiontime != null">#{completiontime,jdbcType=VARCHAR},</if>
            <if test="why1cause != null">#{why1cause,jdbcType=VARCHAR},</if>
            <if test="why2cause != null">#{why2cause,jdbcType=VARCHAR},</if>
            <if test="why3cause != null">#{why3cause,jdbcType=VARCHAR},</if>
            <if test="why4cause != null">#{why4cause,jdbcType=VARCHAR},</if>
            <if test="why5cause != null">#{why5cause,jdbcType=VARCHAR},</if>
            <if test="why1outflow != null">#{why1outflow,jdbcType=VARCHAR},</if>
            <if test="why2outflow != null">#{why2outflow,jdbcType=VARCHAR},</if>
            <if test="why3outflow != null">#{why3outflow,jdbcType=VARCHAR},</if>
            <if test="why4outflow != null">#{why4outflow,jdbcType=VARCHAR},</if>
            <if test="why5outflow != null">#{why5outflow,jdbcType=VARCHAR},</if>
            <if test="itrackingWhy != null">#{itrackingWhy,jdbcType=VARCHAR},</if>
            <if test="picWhy != null">#{picWhy,jdbcType=VARCHAR},</if>
            <if test="extimeWhy != null">#{extimeWhy,jdbcType=VARCHAR},</if>
            <if test="endtimeWhy != null">#{endtimeWhy,jdbcType=VARCHAR},</if>
            <if test="smethod != null">#{smethod,jdbcType=VARCHAR},</if>
            <if test="insitem != null">#{insitem,jdbcType=VARCHAR},</if>
            <if test="anumber != null">#{anumber,jdbcType=VARCHAR},</if>
            <if test="nanumber != null">#{nanumber,jdbcType=VARCHAR},</if>
            <if test="natalk != null">#{natalk,jdbcType=VARCHAR},</if>
            <if test="moment != null">#{moment,jdbcType=VARCHAR},</if>
            <if test="qh1 != null">#{qh1,jdbcType=VARCHAR},</if>
            <if test="qh2 != null">#{qh2,jdbcType=VARCHAR},</if>
            <if test="qh3 != null">#{qh3,jdbcType=VARCHAR},</if>
            <if test="fileId != null">#{fileId,jdbcType=VARCHAR},</if>
            <if test="itrackingOut != null">#{itrackingOut,jdbcType=VARCHAR},</if>
            <if test="picOut != null">#{picOut,jdbcType=VARCHAR},</if>
            <if test="exTimeOut != null">#{exTimeOut ,jdbcType=VARCHAR},</if>
            <if test="endTimeOut != null">#{endTimeOut,jdbcType=VARCHAR},</if>
            <if test="fileName !=null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="filePath !=null">#{filePath,jdbcType=VARCHAR},</if>
            <if test="laborCost !=null">#{laborCost,jdbcType=VARCHAR},</if>
            <if test="processCost !=null">#{processCost,jdbcType=VARCHAR},</if>
            <if test ="cancellationCost !=null">#{cancellationCost,jdbcType=VARCHAR},</if>
            <if test ="qh4 !=null">#{qh4,jdbcType=VARCHAR},</if>
            <if test ="workId !=null">#{workId,jdbcType=VARCHAR},</if>
            <if test ="qhStatus !=null">#{qhStatus,jdbcType=VARCHAR},</if>
            <if test="qh5 !=null">#{qh5,jdbcType=VARCHAR},</if>
            <if test="stNo != null">#{stNo,jdbcType=VARCHAR},</if>
            <if test="lineLeaderName != null">#{lineLeaderName,jdbcType=VARCHAR},</if>
            <if  test="zrqc != null">#{zrqc,jdbcType=VARCHAR},</if>
            <if  test="showId != null ">#{showId,jdbcType=VARCHAR},</if>
            <if test="pl != null ">#{pl,jdbcType=VARCHAR},</if>
            <if test="isOther != null">#{isOther,jdbcType=VARCHAR},</if>
            <if test="qh6 != null ">#{qh6,jdbcType=VARCHAR},</if>
            <if test="isBack != null ">#{isBack,jdbcType=VARCHAR},</if>
            <if test="goBackWhy != null ">#{goBackWhy,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <select id="selectUnit" resultType="java.lang.String">
        USE Schedule
        select  unit from t_configure_backwork_audit WHERE pl='IDS1'
    </select>

    <select id="selectRoleByWorkId" resultType="cc.mrbird.febs.backwork.entity.UserRoleDto">
        USE Schedule
        SELECT name, role  from t_configure_backwork_audit where workId=#{workId}
    </select>

    <select id="queryRoleByWorkId" resultType="java.lang.Integer">
        USE Schedule
        SELECT role from t_configure_backwork_audit where workId=#{workId}
    </select>

    <select id="findQc" resultType="java.lang.String">
        USE Schedule
        SELECT name FROM t_configure_backwork_audit WHERE role ='3' AND pl=#{pl}
    </select>
<!--課長簽核-->
    <update id="QC1">
        USE Schedule
        UPDATE t_data_backwork set qh2=#{name}, qhStatus='1',stNo='2',showId=#{showId}   WHERE uuid=#{uuid}
    </update>
<!--品保第一次簽核並敲定重工責任單位-->
    <select id="QC2">
        USE Schedule
        UPDATE t_data_backwork set qh3 =#{name},qhStatus='2',backWorkUnit=#{unit},stNo='3',workId=#{workId} ,laborCost=#{laborCost},
        processCost=#{processCost},cancellationCost=#{cancellationCost},showId=#{showId}
        WHERE uuid=#{uuid}
    </select>
<!--電鍍重工責任單位簽核-->
    <update id="QC3">
        USE Schedule
        UPDATE  t_data_backwork set qh4 =#{name},qhStatus='3',stNo='4',showId=#{showId} WHERE uuid=#{uuid}
    </update>
<!--電鍍內部簽核-->
    <update id="QC4" >
        USE Schedule
        UPDATE t_data_backwork set qh5 =#{name},qhStatus='4',stNo='5',showId=#{showId} WHERE uuid=#{uuid}
    </update>
<!--品保最終判定-->
    <update id="QC5" >
        USE Schedule
        UPDATE t_data_backwork set qhStatus='5',stNo='6',showId=#{showId} WHERE uuid=#{uuid}
    </update>
    <select id="findEmailANdRole" resultType="cc.mrbird.febs.backwork.entity.UserRoleDto">
        USE Schedule
        select role,emailNumber from t_configure_backwork_audit where name =#{name}
    </select>
    <update id="updateStNo">
        USE Schedule
        update t_data_backwork set stNo=#{stNo} WHERE uuid =#{uuid}
    </update>
    <select id="findStNo" resultType="java.lang.String">
        USE Schedule
        select stNo from t_data_backwork where uuid=#{uuid}
    </select>
    <select id="countUnit" resultType="int">
        USE Schedule
        select case when backWorkUnit like '%電鍍%' then 1  else 0  end AS num from t_data_backwork where uuid=#{uuid}
    </select>
    <select id="findUnitName" resultType="java.lang.String">
        USE Schedule
        select name from t_configure_backwork_audit where unit =#{unit} AND role !='1'
    </select>
    <select id="findEmail" resultType="java.lang.String">
       USE Schedule
        SELECT emailNumber from Schedule.dbo.t_configure_backwork_audit where name=#{name}
    </select>
    <select id="findLeaderName" resultType="java.lang.String">
        USE Schedule
        SELECT lineLeaderName from  Schedule.dbo.t_data_backwork where uuid=#{uuid}
    </select>
    <select id="findQh1" resultType="java.lang.String">
        USE Schedule
        SELECT qh1 from  Schedule.dbo.t_data_backwork where uuid=#{uuid}
    </select>
    <select id="findQh2" resultType="java.lang.String">
        USE Schedule
        SELECT qh2 from  Schedule.dbo.t_data_backwork where uuid=#{uuid}
    </select>
    <select id="findQh3" resultType="java.lang.String">
        USE Schedule
        SELECT qh3 from  Schedule.dbo.t_data_backwork where uuid=#{uuid}
    </select>
    <select id="findQh4" resultType="java.lang.String">
        USE Schedule
        SELECT qh4 from  Schedule.dbo.t_data_backwork where uuid=#{uuid}
    </select>
    <select id="findQh5" resultType="java.lang.String">
        USE Schedule
        SELECT qh5 from  Schedule.dbo.t_data_backwork where uuid=#{uuid}
    </select>
    <update id="QCNotSP">
        USE Schedule
        UPDATE  t_data_backwork set qh5 =#{name},qhStatus='3',stNo='4',showId=#{showId} WHERE uuid=#{uuid}
    </update>

    <select id="selectState" resultType="cc.mrbird.febs.backwork.entity.UserRoleDto">
        USE Schedule
        SELECT stNo,qhStatus,showId FROM t_data_backwork WHERE uuid=#{uuid}
    </select>

    <select id="selectWorkIdByNewAdd" resultType="java.lang.String">
        USE Schedule
        select workId from t_configure_backwork_audit WHERE name =#{name}
    </select>

    <select id="selectQh2Name" resultType="java.lang.String">
        USE Schedule
        SELECT qh2 FROM t_data_backwork WHERE uuid=#{uuid}
    </select>
    <select id="selectWorkIdByQh1" resultType="java.lang.String">
        USE Schedule
        select workId from t_configure_backwork_audit WHERE name =#{name}
    </select>
    <!--PL2、PL3签核流程-->
    <!--小品保填写内容-->
    <!--QH1   -->
    <select id="QH1ByPL2AndPL3" resultType="java.lang.Integer">
        USE Schedule
        UPDATE t_data_backwork set
        sMethod=#{sMethod},insItem=#{insItem},aNumber=#{aNumber},
        naNumber=#{naNumber},naTalk=#{naTalk},
        qh2=#{name}, qhStatus='1',stNo='2',showId=#{showId}
        WHERE uuid=#{uuid}
    </select>
<!--QE-->
    <select id="QH2ByPL2AndPL3" resultType="java.lang.Integer">
        USE Schedule
        UPDATE t_data_backwork set qh3=#{name}, qhStatus='2',stNo='3',showId=#{showId}   WHERE uuid=#{uuid}
    </select>
<!--装配课长-->
    <select id="QH3ByPL2AndPL3" resultType="java.lang.Integer">
        USE Schedule
        UPDATE t_data_backwork set qh4=#{name}, qhStatus='3',stNo='4',showId=#{showId}   WHERE uuid=#{uuid}
    </select>
<!--品保课长-->
    <select id="QH4ByPL2AndPL3" resultType="java.lang.Integer">
        USE Schedule
        UPDATE t_data_backwork set qh5=#{name}, qhStatus='4',stNo='5',showId=''   WHERE uuid=#{uuid}
    </select>
<!--装配部长-->
    <select id="QH5ByPL2AndPL3" resultType="java.lang.Integer">
        USE Schedule
        UPDATE t_data_backwork set qh6=#{name}, qhStatus='5',stNo='6',showId=#{showId}   WHERE uuid=#{uuid}
    </select>
<!--品保部长-->
    <select id="QH6ByPL2AndPL3" resultType="java.lang.Integer">
        USE Schedule
        UPDATE t_data_backwork set  qhStatus='6',stNo='7',showId=''  WHERE uuid=#{uuid}
    </select>

    <select id="findQh6" resultType="java.lang.String">
        USE Schedule
        SELECT qh6 FROM t_data_backwork WHERE uuid=#{uuid}
    </select>

    <select id="findDiscernLeader" resultType="java.lang.String">
        USE Schedule
        SELECT name FROM t_configure_backwork_audit WHERE pl=#{pl} AND role=#{role}
    </select>


   <!-- 客诉类原因品保课长签核-->
    <select id="QH4ByPL2AndPL3ToKs" resultType="java.lang.Integer">
        USE Schedule
        UPDATE t_data_backwork set qh5=#{name}, qhStatus='4',stNo='5',showId=#{showId}   WHERE uuid=#{uuid}
    </select>

    <select id="selectNoName" resultType="java.lang.String">
     USE Schedule
        SELECT name FROM t_configure_backwork_audit WHERE  pl =#{pl} AND role=#{role}
    </select>

    <select id="selectPLByUUID" resultType="java.lang.String">
        USE Schedule
        SELECT pl FROM t_data_backwork WHERE uuid=#{uuid}
    </select>

    <select id="selectStNoByUUID" resultType="java.lang.Integer">
        USE Schedule
        SELECT stNo FROM t_data_backwork WHERE uuid=#{uuid}
    </select>

    <update id="updateByGoBackCheck">
        USE Schedule
        UPDATE  t_data_backwork set showId=#{showId},stNo=#{stNo},isBack='1',goBackWhy=#{why}  WHERE uuid=#{uuid}
    </update>

    <select id="selectWorkIdByName" resultType="java.lang.String">
        USE Schedule
        SELECT workId FROM t_configure_backwork_audit WHERE name=#{name}
    </select>

    <delete id="delByUUID">
        USE Schedule
        delete from t_data_backwork WHERE uuid=#{uuid}
    </delete>



    <select id="selectQHByQh1" resultType="java.lang.String">
        USE Schedule
        SELECT qh1 FROM t_data_backwork WHERE uuid =#{uuid}
    </select>

    <select id="selectQHByQh3" resultType="java.lang.String">
        USE Schedule
        SELECT qh3 FROM t_data_backwork WHERE uuid =#{uuid}
    </select>

    <update id="QHByPL1SameUnit" >
        USE Schedule
        UPDATE t_data_backwork
        <set>
          qh1=#{qh1},qh3=#{qh3},showId='',stNo='6',qh5=#{qh5},backWorkUnit=#{backWorkUnit}
        </set>
        WHERE uuid =#{uuid}
    </update>

    <select id="selectLineLeaderByUUID" resultType="java.lang.String">
        USE Schedule
        select lineLeaderName from t_data_backwork where uuid =#{uuid}
    </select>

    <select id="selectQHByZRQC" resultType="java.lang.String">
        USE Schedule
        select zrqc from t_data_backwork where uuid =#{uuid}
    </select>

    <select id="selectPartnumber" resultType="java.lang.String">
        USE Schedule
        select partNumber from t_data_backwork WHERE uuid=#{uuid}
    </select>
</mapper>
