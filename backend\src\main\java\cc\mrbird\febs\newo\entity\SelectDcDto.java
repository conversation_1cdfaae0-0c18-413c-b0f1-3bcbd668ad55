package cc.mrbird.febs.newo.entity;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SelectDcDto {
    @ApiModelProperty(value = "dc",position = 1)
    private String dc;
    @ApiModelProperty(value = "日期",position = 2)
    private String dateTime;
    @ApiModelProperty(value = "班别",position = 3)
    private String classInfo;

}
