package cc.mrbird.febs.jjkpi.service;

import cc.mrbird.febs.jjkpi.entity.DjxxDto;
import cc.mrbird.febs.jjkpi.entity.LineInfo;
import cc.mrbird.febs.jjkpi.entity.SixGdDjxx;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISixGdDjxxService extends IService<SixGdDjxx> {

    List<DjxxDto> queryLineForm(String line,String classInfo,String production);

    List<LineInfo> queryLineInfo(String cardId,List<String> lines);

    List<String> queryLineByCardId(String cardId);
}
