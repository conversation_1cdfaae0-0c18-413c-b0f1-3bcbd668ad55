package cc.mrbird.febs.common.utils;

import cc.mrbird.febs.common.domain.Tree;
import cc.mrbird.febs.common.domain.router.RouterMeta;
import cc.mrbird.febs.common.domain.router.VueRouter;

import java.util.*;

public class TreeUtil {

    protected TreeUtil() {

    }

    private final static String TOP_NODE_ID = "0";

    /**
     * 用于构建菜单或部门树
     *
     * @param nodes nodes
     * @param <T>   <T>
     * @return <T> Tree<T>
     */
    public static <T> Tree<T> build(List<Tree<T>> nodes) {
        if (nodes == null) {
            return null;
        }
        List<Tree<T>> topNodes = new ArrayList<>();
        nodes.forEach(node -> {
            String pid = node.getParentId();
            if (pid == null || TOP_NODE_ID.equals(pid)) {
                topNodes.add(node);
                return;
            }
            for (Tree<T> n : nodes) {
                String id = n.getId();
                if (id != null && id.equals(pid)) {
                    if (n.getChildren() == null)
                        n.initChildren();
                    n.getChildren().add(node);
                    node.setHasParent(true);
                    n.setHasChildren(true);
                    n.setHasParent(true);
                    return;
                }
            }
            if (topNodes.isEmpty())
                topNodes.add(node);
        });


        Tree<T> root = new Tree<>();
        root.setId("0");
        root.setParentId("");
        root.setHasParent(false);
        root.setHasChildren(true);
        root.setChildren(topNodes);
        root.setText("root");
        return root;
    }


    /**
     * 构造前端路由
     *
     * @param routes routes
     * @param <T>    T
     * @return ArrayList<VueRouter<T>>
     */
    public static <T> ArrayList<VueRouter<T>> buildVueRouter(List<VueRouter<T>> routes) {
        if (routes == null) {
            return null;
        }
        List<VueRouter<T>> topRoutes = new ArrayList<>();
        VueRouter<T> router = new VueRouter<>();
        router.setName("系统主页");
        router.setPath("/home");
        router.setComponent("HomePageView");
        router.setIcon("home");
        router.setChildren(null);
        router.setMeta(new RouterMeta(false, true));
        topRoutes.add(router);

        routes.forEach(route -> {
            String parentId = route.getParentId();
            if (parentId == null || TOP_NODE_ID.equals(parentId)) {
                topRoutes.add(route);
                return;
            }
            for (VueRouter<T> parent : routes) {
                String id = parent.getId();
                if (id != null && id.equals(parentId)) {
                    if (parent.getChildren() == null)
                        parent.initChildren();
                    parent.getChildren().add(route);
                    parent.setHasChildren(true);
                    route.setHasParent(true);
                    parent.setHasParent(true);
                    return;
                }
            }
        });
        router = new VueRouter<>();
        router.setPath("/profile");
        router.setName("个人中心");
        router.setComponent("personal/Profile");
        router.setIcon("none");
        router.setMeta(new RouterMeta(true, false));
        topRoutes.add(router);

        ArrayList<VueRouter<T>> list = new ArrayList<>();
        VueRouter<T> root = new VueRouter<>();
        root.setName("主页");
        root.setComponent("MenuView");
        root.setIcon("none");
        root.setPath("/");
        root.setRedirect("/home");
        root.setChildren(topRoutes);
        list.add(root);

        root = new VueRouter<>();
        root.setName("404");
        root.setComponent("error/404");
        root.setPath("*");
        list.add(root);

        return list;
    }


    /**
     * 优化的构建树的方法,原先的构建树方法为o(n的平方)-->现在o(2n)
     * @param nodes 节点列表
     * @param <T> 携带的数据
     * @return
     */
    public static <T> Tree<T> buildOptimized(List<Tree<T>> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return createEmptyRoot();
        }

        // 1. 创建快速查找的节点映射 (ID -> Node)
        Map<String, Tree<T>> nodeMap = new HashMap<>(nodes.size());
        List<Tree<T>> topNodes = new ArrayList<>();

        // 2. 第一遍遍历：填充映射表
        for (Tree<T> node : nodes) {
            // 防止ID重复导致数据覆盖
            if (node.getId() != null) {
                if (nodeMap.containsKey(node.getId())) {
                    throw new IllegalArgumentException("Duplicate node ID found: " + node.getId());
                }
                nodeMap.put(node.getId(), node);
            }
        }

        // 3. 第二遍遍历：构建树结构
        for (Tree<T> node : nodes) {
            String pid = node.getParentId();

            // 处理根节点
            if (isRootNode(pid)) {
                // 是根节点
                topNodes.add(node);
                node.setHasParent(false);
                continue;
            }

            // 查找父节点
            Tree<T> parent = pid != null ? nodeMap.get(pid) : null;

            if (parent != null) {
                // 初始化子节点列表
                if (parent.getChildren() == null) {
                    parent.initChildren();
                }
                parent.getChildren().add(node);
                node.setHasParent(true);
                parent.setHasChildren(true);
            } else {
                // 处理孤儿节点：作为顶级节点
                topNodes.add(node);
                node.setHasParent(false);

                // 可选的日志警告
                // Logger.warn("Orphan node detected, ID: " + node.getId() + " ParentID: " + pid);
            }
        }

        // 4. 创建虚拟根节点
        return createRootNode(topNodes);
    }

    // 判断是否为根节点条件
    private static <T> boolean isRootNode(String parentId) {
        return parentId == null
                || parentId.isEmpty()
                || TOP_NODE_ID.equals(parentId);
    }

    // 创建空根节点
    private static <T> Tree<T> createEmptyRoot() {
        Tree<T> root = new Tree<>();
        root.setId(TOP_NODE_ID);
        root.setParentId("");
        root.setHasParent(false);
        root.setHasChildren(false);
        root.setText("root");
        // 确保子节点列表不为null
        root.initChildren();
        return root;
    }

    // 创建带子节点的根节点
    private static <T> Tree<T> createRootNode(List<Tree<T>> children) {
        Tree<T> root = createEmptyRoot();
        root.setChildren(children);
        root.setHasChildren(!children.isEmpty());
        return root;
    }
}