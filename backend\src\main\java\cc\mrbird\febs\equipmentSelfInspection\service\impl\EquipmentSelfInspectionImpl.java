package cc.mrbird.febs.equipmentSelfInspection.service.impl;

import cc.mrbird.febs.equipmentSelfInspection.dao.EquipmentSelfInspectionMapper;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentPersonDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.ProjectMaintenance;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentQueryParam;
import cc.mrbird.febs.equipmentSelfInspection.service.EquipmentSelfInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

/**
 * Author: 覃金華
 * Date: 2024-08-12
 * Time: 下午 01:06
 */
@Service
public class EquipmentSelfInspectionImpl implements EquipmentSelfInspectionService {
    private final EquipmentSelfInspectionMapper emapper;

    @Autowired
    public EquipmentSelfInspectionImpl(EquipmentSelfInspectionMapper emapper) {
        this.emapper = emapper;
    }


    //查詢設備數據
    @Override
    public List<EquipmentInspectionDto> queryEquipmentInspectionData(EquipmentQueryParam equipmentQueryParam) {
        List<EquipmentInspectionDto> list =emapper.selectSelfInspectionData(equipmentQueryParam);

        return list;
    }


    //查詢人員數據
    @Override
    public List<EquipmentPersonDto> queryEquipmentPersonData(EquipmentQueryParam equipmentQueryParam) {
        List<EquipmentPersonDto> list =emapper.selectEquipmentPersonData(equipmentQueryParam);
        return list;
    }


    //查詢項目維護數據
    @Override
    public List<ProjectMaintenance> queryProjectMaintenanceData(EquipmentQueryParam equipmentQueryParam) {
        List<ProjectMaintenance> list =emapper.selectProjectMaintenanceData(equipmentQueryParam);

        return list;
    }

    @Override
    public List<String> queryDiscern() {
        List<String> list =emapper.selectDiscern();
        return list;
    }

    @Override
    public List<String> queryProduction(String discern) {
        List<String> list = emapper.selectProduction(discern);
        return list;
    }

    @Override
    public List<String> queryProductionLine(String discern, String production) {
        List<String> list =emapper.selectProductionLine(discern,production);
        return list;
    }

    @Override
    public List<String> queryEquipmentName(String discern, String production, String productionLine) {
        List<String> list = emapper.selectEquipmentName(discern, production, productionLine);
        return list;
    }

    @Override
    public List<String> querySelfInspectionType() {
        List<String> list =emapper.selectSelfInspectionType();
        return list;
    }

    @Override
    public String getEquipmentPersonExportFile() {
        return null;
    }

    @Override
    public String getEquipmentDataExportFile() {
        try{
            File file =new File("D:\\Fit\\equipmentSelfInspection\\person.xls");
            openInputStream(file);
        }catch (Exception e){
            return "下載失敗";
        }
        return "OK";
    }

    @Override
    public String getSelfInspectionDataExportFile() {
        return null;
    }

    public static FileInputStream openInputStream(File file) throws IOException {
        if (file.exists()) {
            if (file.isDirectory()) {
                throw new IOException("File '" + file + "' exists but is a directory");
            } else if (!file.canRead()) {
                throw new IOException("File '" + file + "' cannot be read");
            } else {
                return new FileInputStream(file);
            }
        } else {
            throw new FileNotFoundException("File '" + file + "' does not exist");
        }
    }

    @Override
    public int updateByEquipment(EquipmentInspectionDto equipmentInspectionDto) {
        int i =emapper.updateByEquipmentData(equipmentInspectionDto);
        return i;
    }

    @Override
    public int updateByProjectMaintenance(ProjectMaintenance projectMaintenance) {
       int i = emapper.updateByProjectMaintenance(projectMaintenance);
        return i;
    }

    @Override
    public int updateByEquipmentPerson(EquipmentPersonDto equipmentPersonDto) {
        int i =emapper.updateByEquipmentPerson(equipmentPersonDto);
        return i;
    }

    @Override
    public int delByEquipmentPerson(Integer id) {
        int i =emapper.delByEquipmentPerson(id);
        return i;
    }

    @Override
    public int insertByEquipmentPerson(EquipmentPersonDto equipmentPersonDto) {
        int i =emapper.insertByEquipmentPerson(equipmentPersonDto);
        return  i;
    }

    @Override
    public int delByEquipmentData(Integer id) {
        int i =emapper.delByEquipmentData(id);
        return  i;
    }

    @Override
    public void inertByEquipmentInspection(EquipmentInspectionDto equipmentInspectionDto) {
        emapper.insertByEquipmentInspection(equipmentInspectionDto);
    }

    @Override
    public void insertByProjectMaintenance(ProjectMaintenance projectMaintenance) {
        emapper.insertByProjectMaintenance(projectMaintenance);
    }

    @Override
    public int countByEquipmentInspection(String production, String productionLine, String equipmentName) {

        return emapper.countByEquipmentInspection(production, productionLine, equipmentName);
    }

    @Override
    public int delByProjectMaintenance(Integer id) {
        int i =emapper.delByProjectMaintenance(id);
        return i;
    }

    @Override
    public String selectEquipment(String production, String productionLine, String equipmentName) {
     String equipmentId  = emapper.selectEquipmentId(production, productionLine, equipmentName);
        return equipmentId;
    }

    @Override
    public List<EquipmentInspectionDto> selectExampleData() {
        List<EquipmentInspectionDto> list =emapper.exampleDataByEquipment();
        return list;
    }

    @Override
    public List<ProjectMaintenance> selectExampleDataByProject() {
        List<ProjectMaintenance> list = emapper.selectExampleDataByProject();
        return list;
    }
}
