package cc.mrbird.febs.badRelease.dao;


import cc.mrbird.febs.badRelease.entity.dto.AdditionalInfoDto;
import cc.mrbird.febs.badRelease.entity.dto.BadReleaseDetailDto;
import cc.mrbird.febs.badRelease.entity.qo.ConditionUseingQueryQo;
import cc.mrbird.febs.badRelease.entity.vo.BadReleaseInfoVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("mssql-paperless")
public interface BadReleaseMapper {

    /**
     * 查詢產品處
     * @param workId
     * @return
     */
    String querySBU(String workId);

    /**
     * 查詢機能
     * @param workId
     * @return
     */
    String queryFunction(String workId);

    /**
     * 查詢課別
     * @return
     */
    List<String> querySecntion();

    /**
     * 查詢線體
     * @return
     */
    List<String> queryLine();

    /**
     * 查詢料號
     * @return
     */
    List<String> queryMaterialNumber();

    /**
     * 查詢不良釋放日期
     * @return
     */
    List<String> queryBadReleaseDate();

    /**
     * 條件查詢
     * 條件為：課別、線體、料號、不良釋放日期
     * @param conditionUseingQuery
     * @return
     */
    List<BadReleaseInfoVo> queryCondition(ConditionUseingQueryQo conditionUseingQuery);

    /**
     * 詳情展示
     * id查詢
     * @param id
     * @return
     */
    BadReleaseDetailDto queryById(Integer id);

    /**
     * 詳情展示
     * id查詢
     * 附表補充料號查詢
     * @param materialNumber
     * @return
     */
    List<AdditionalInfoDto> queryByMaterialNumber(String materialNumber);
}
