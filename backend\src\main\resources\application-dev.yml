server:
  port: 9527
spring:
  datasource:
    dynamic:
      # 是否开启 SQL日志输出，生产环境建议关闭，有性能损耗
      p6spy: false
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: select 1
        pool-name: FebsHikariCP
      # 配置默认数据源
      primary: primary
      datasource:
        # 数据源-1，名称为 primary
        primary:
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************************************************
        # 数据源-2，名称为 test
        mssql:
          username: Admin01
          password: Password@admin01
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: jdbc:sqlserver://************:1433;DatabaseName=Schedule
        mssql_android:
          username: Admin01
          password: Password@admin01
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ***********************************************************
  aop:
    proxy-target-class: true

  messages:
    encoding: utf-8

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false

  redis:
    host: 127.0.0.1
    port: 6379
    password:
    jedis:
      pool:
        min-idle: 8
        max-idle: 500
        max-active: 2000
        max-wait: 10000
    timeout: 0


#mybatis plus 设置
mybatis-plus:
  type-aliases-package: cc.mrbird.febs.system.domain,cc.mrbird.febs.job.domain
  mapper-locations: classpath:mapper/*/*.xml
  configuration:
    jdbc-type-for-null: null
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 关闭 mybatis-plus的 banner
    banner: false

management:
  endpoints:
    web:
      exposure:
        include: ['httptrace', 'metrics']


febs:
  # 是否异步记录用户操作日志
  openAopLog: true
  # 单次最大批量入库数量
  max:
    batch:
      insert:
        num: 1000
  shiro:
    # 后端免认证接口 url
    anonUrl: /login,/logout/**,/regist,/user/check/**,/swagger-resources/**,/webjars/**,/v2/**,/swagger-ui.html/**,/favicon.ico,/customerCondition/customer-condition-upload/**,/customerCondition/downloadLatestCustomerConditionSummaryFile
    # token有效期，单位秒
    jwtTimeOut: 3600000
  # Swagger相关配置
  swagger:
    basePackage: cc.mrbird.febs
    title: FEBS VUE API
    description: FEBS VUE API Document.
    version: 2.0
    author: MrBird
    url: https://mrbird.cc
    email: <EMAIL>
    license: Apache 2.0
    licenseUrl: https://www.apache.org/licenses/LICENSE-2.0.html

# 最大导出数量
export:
  maxCount: 10000
