package cc.mrbird.febs.indexpage.service;

import cc.mrbird.febs.indexpage.entity.*;
import cc.mrbird.febs.indexpage.entity.qo.SelectLineNumberQo;
import cc.mrbird.febs.indexpage.entity.vo.SheetNameDataVo;
import org.apache.poi.ss.formula.functions.T;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface IndexPagerDataService {
     List<TDataByCountProductionNum> getProductionNum(ProductionNum productionNum);

     Map<String,String> getSubState(String workId);

    int getQhState(String workId);

    int getBackNum(String workId);

    List<PartNumberByIndex> getPartNumber(DateAndWorkIdQo  andWorkIdQo);

    String determineShift(LocalDateTime localDateTime);
    Map<String, SheetNameDataVo> selectAllDataByQo(SelectLineNumberQo selectLineNumberQo) throws ParseException;
}
