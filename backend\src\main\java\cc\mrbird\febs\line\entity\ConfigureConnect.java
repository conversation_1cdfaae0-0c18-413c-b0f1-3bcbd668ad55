package cc.mrbird.febs.line.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("configure_connect")
@AllArgsConstructor
@NoArgsConstructor
public class ConfigureConnect implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "Id", type = IdType.AUTO)
    private Long Id;

    /**
     * 厂区
     */
    @ApiModelProperty(value = "厂区",required = true)
        @TableField("Factory")
    private String factory;

    /**
     * 产业处
     */
    @ApiModelProperty(value = "产业处",required = true)
        @TableField("Industry")
    private String industry;

    /**
     * 机能
     */
    @ApiModelProperty(value = "机能",required = true)
        @TableField("Enginery")
    private String enginery;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期",required = true)
    @TableField("DateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date dateTime;

    /**
     * 课别
     */
    @ApiModelProperty(value = "课别",required = true)
        @TableField("Discern")
    private String discern;

    /**
     * 系列
     */
    @ApiModelProperty(value = "系列",required = true)
        @TableField("Production")
    private String production;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体",required = true)
        @TableField("ProductionLine")
    private String productionLine;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号",required = true)
        @TableField("PartNumber")
    private String partNumber;

    /**
     * 交接人
     */
    @ApiModelProperty(value = "交接人",required = true)
        @TableField("Person")
    private String person;

    /**
     * 班别
     */
    @ApiModelProperty(value = "班别",required = true)
        @TableField("ClassInfo")
    private String classInfo;

    @ApiModelProperty(value = "姓名",required = true)
    private String name;

    /**
     * a.早会人员出勤点到
     */
    @ApiModelProperty(value = "早会人员出勤点到",required = true)
        @TableField("AttenDance")
    private Boolean attenDance;

    /**
     * 仪容整理
     */
    @ApiModelProperty(value = "仪容整理",required = true)
        @TableField("Appearance")
    private Boolean appearance;

    /**
     * 节能&安全宣导
     */
    @ApiModelProperty(value = "节能&安全宣导",required = true)
        @TableField("Energy")
    private Boolean energy;

    /**
     * 品质异常宣导
     */
    @ApiModelProperty(value = "品质异常宣导",required = true)
        @TableField("QC")
    private Boolean qc;

    /**
     * 纪律宣导
     */
    @ApiModelProperty(value = "纪律宣导",required = true)
        @TableField("Discipline")
    private Boolean discipline;

    /**
     * 当日生产人员安排
     */
    @ApiModelProperty(value = "当日生产人员安排",required = true)
        @TableField("Arrange")
    private Boolean arrange;

    /**
     * 政令宣导
     */
    @ApiModelProperty(value = "政令宣导",required = true)
        @TableField("Decree")
    private Boolean decree;

    /**
     * 人员精神状态确认
     */
    @ApiModelProperty(value = "人员精神状态确认",required = true)
        @TableField("Spirit")
    private Boolean spirit;

    /**
     * 品质状态:0是OK :1是NG
     */
    @ApiModelProperty(value = "品质状态",required = true)
        @TableField("Quality")
    private Boolean quality;

    @ApiModelProperty(value = "上一班現場安全情況和存在的問題",required = true)
    @TableField("newText1")
    private Boolean newText1;

    @ApiModelProperty(value = "本班具體明確的安全注意事項和處理方法",required = true)
    @TableField("newText2")
    private Boolean newText2;

    @ApiModelProperty(value = "強調崗位危險因素及禁忌之違章情況",required = true)
    @TableField("newText3")
    private Boolean newText3;







    /**
     * 品质异常说明
     */
    @ApiModelProperty(value = "品质异常说明",required = true)
        @TableField("Abnormal")
    private String abnormal;

    /**
     * 明细选择
     */
    @ApiModelProperty(value = "明细选择",required = true)
        @TableField("Particulars")
    private String particulars;

    /**
     * 异常明细说明
     */
    @ApiModelProperty(value = "异常明细说明",required = true)
        @TableField("AbnormalTalk")
    private String abnormalTalk;

    /**
     * 制程
     */
    @ApiModelProperty(value = "制程",required = true)
        @TableField("Processing")
    private String processing;

    /**
     * 处理
     */
    @ApiModelProperty(value = "处理",required = true)
        @TableField("TDW")
    private String tdw;

    /**
     * 制程明细
     */
    @ApiModelProperty(value = "制程明细",required = true)
        @TableField("ProcessingDetail")
    private String processingDetail;

    /**
     * 制程明细说明
     */
    @ApiModelProperty(value = "制程明细说明",required = true)
        @TableField("ProcessingTalk")
    private String processingTalk;

    /**
     * 交接事项说明
     */
    @ApiModelProperty(value = "交接事项说明",required = true)
        @TableField("ConnectTalk")
    private String connectTalk;

    /**
     * 其他事项说明
     */
    @ApiModelProperty(value = "其他事项说明",required = true)
        @TableField("OtherTalk")
    private String otherTalk;

    /**
     * 与kpi表建立连接的id
     */
    @ApiModelProperty(value = "kpi表建立连接的id",required = true)
    @TableField("ClassKPI_Id")
    private String classkpiId;
    @ApiModelProperty(value = "图片地址")
    @TableField("PicUrl")
    private String picUrl;
    @TableField("PicName")
    private String        picName;


}
