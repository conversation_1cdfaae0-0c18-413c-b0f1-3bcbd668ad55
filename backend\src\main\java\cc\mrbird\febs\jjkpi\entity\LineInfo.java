package cc.mrbird.febs.jjkpi.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LineInfo {
     //课别
     private String section;
     //系列
     private String series;
     //线体
     private String line;
     //料号
     private String cplh_pfn;

     //交接人,默认为该账号
     private List<String> passPerson;

}
