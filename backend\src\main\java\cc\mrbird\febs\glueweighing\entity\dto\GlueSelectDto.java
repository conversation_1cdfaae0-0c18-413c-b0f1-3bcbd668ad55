package cc.mrbird.febs.glueweighing.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GlueSelectDto {
    @ApiModelProperty(value = "課別")
    private String discern;
    @ApiModelProperty(value = "系列")
    private String production;
    @ApiModelProperty(value ="線體" )
    private String productionLine;
    @ApiModelProperty(value = "班別")
    private String classInfo;
    @ApiModelProperty(value = "量測指標")
    private String measurementIndex;
    @ApiModelProperty(value = "开始日期")
    private String recordDateStart;
    @ApiModelProperty(value = "结束日期")
    private String recordDateEnd;
}
