package cc.mrbird.febs.glueweighing.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Value;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GlueEditDto {
    @ApiModelProperty(value = "",position=0)
    private String uuid;
    @ApiModelProperty(value = "频率",position = 1)
    private String frequency;
    @ApiModelProperty(value = "温度",position=2)
    private String temperature;
    @ApiModelProperty(value = "时间",position=3)
    private String otherTime;
    @ApiModelProperty(value = "洗阀",position=4)
    private String washValve;
    @ApiModelProperty(value = "换膠",position=5)
    private String glueChange;
}
