package cc.mrbird.febs.comparation136.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.datetime.DateFormatter;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckComparation136 implements Serializable {

    private static final long seriaVersionUID = 1L;

    //需要簽核的單據的數據庫id
    private Long id;

   //簽核狀態
    private String status;


    //組長簽核狀態
    private String status2;

    //退單原因
    private String TD_TDYY;

}
