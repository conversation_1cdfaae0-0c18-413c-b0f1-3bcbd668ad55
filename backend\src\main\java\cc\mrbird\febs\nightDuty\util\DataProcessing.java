package cc.mrbird.febs.nightDuty.util;

import cc.mrbird.febs.nightDuty.entity.NightDutyInfo;

import java.util.ArrayList;
import java.util.List;

public class DataProcessing {
    public static List<NightDutyInfo> dataProcessing(List<NightDutyInfo> list, List<NightDutyInfo> lists,
                                                     List<String> area, List<String> department, List<String> header
            , List<String> floor, String buildings) {

        if (buildings.equals("A2-A3")) {

            for (int i = 0; i < 35; i++) {
                NightDutyInfo nightDutyInfo = new NightDutyInfo();
                NightDutyInfo nightDutyInfo1 = list.get(0);

                String s = buildings;
                s = s + "-" + floor.get(i);
                nightDutyInfo.setID(28 + i);
                nightDutyInfo.setFloor(s);
                nightDutyInfo.setWorkId(nightDutyInfo1.getWorkId());
                nightDutyInfo.setName(nightDutyInfo1.getName());
                nightDutyInfo.setTime("未完成簽到");
                nightDutyInfo.setNGStatus("異常");
                nightDutyInfo.setPicture1("");
                nightDutyInfo.setPicture2("");
                nightDutyInfo.setDescription("");
                nightDutyInfo.setDepartment(department.get(i));
                nightDutyInfo.setHeader(header.get(i));
                nightDutyInfo.setArea(area.get(i));

                for (int j = 0; j < list.size(); j++) {
                    List<String> picture = new ArrayList<>();
                    NightDutyInfo nightDutyInfo2 = list.get(j);
                    if (nightDutyInfo.getID().equals(nightDutyInfo2.getID())) {
                        switch (nightDutyInfo2.getNGStatus()) {
                            case "Y":
                                nightDutyInfo.setNGStatus("異常");
                                break;
                            case "N":
                                nightDutyInfo.setNGStatus("正常");
                                break;
                        }

                        if (!nightDutyInfo2.getPicture1().isEmpty()) {
                            picture.add(nightDutyInfo2.getPicture1());
                        }

                        if (!nightDutyInfo2.getPicture2().isEmpty()) {
                            if (nightDutyInfo2.getPicture2().equals("http://10.196.5.227:3000/ZgzyPicUpload/")) {
                                nightDutyInfo.setPicture2("");
                            }
                        }

                        if (!nightDutyInfo2.getPicture2().isEmpty()) {
                            picture.add(nightDutyInfo2.getPicture2());
                        }
                        nightDutyInfo.setPicture(picture);

                        if (!nightDutyInfo2.getTime().isEmpty()) {
                            nightDutyInfo.setTime(nightDutyInfo2.getTime());
                        }
                    }
                }
                lists.add(nightDutyInfo);
            }
        }

        if (buildings.equals("A4-A5")) {
            for (int i = 0; i < 27; i++) {
                NightDutyInfo nightDutyInfo = new NightDutyInfo();
                NightDutyInfo nightDutyInfo1 = list.get(0);

                String s = buildings;
                s = s + "-" + floor.get(i);
                nightDutyInfo.setID(1 + i);
                nightDutyInfo.setFloor(s);
                nightDutyInfo.setWorkId(nightDutyInfo1.getWorkId());
                nightDutyInfo.setName(nightDutyInfo1.getName());
                nightDutyInfo.setTime("未完成簽到");
                nightDutyInfo.setNGStatus("異常");
                nightDutyInfo.setPicture1("");
                nightDutyInfo.setPicture2("");
                nightDutyInfo.setDescription("");
                nightDutyInfo.setDepartment(department.get(i));
                nightDutyInfo.setHeader(header.get(i));
                nightDutyInfo.setArea(area.get(i));

                for (int j = 0; j < list.size(); j++) {
                    List<String> picture = new ArrayList<>();
                    NightDutyInfo nightDutyInfo2 = list.get(j);
                    if (nightDutyInfo.getID().equals(nightDutyInfo2.getID())) {
                        switch (nightDutyInfo2.getNGStatus()) {
                            case "Y":
                                nightDutyInfo.setNGStatus("異常");
                                break;
                            case "N":
                                nightDutyInfo.setNGStatus("正常");
                                break;
                        }

                        if (!nightDutyInfo2.getPicture1().isEmpty()) {
                            picture.add(nightDutyInfo2.getPicture1());
                        }

                        if (!nightDutyInfo2.getPicture2().isEmpty()) {
                            if (nightDutyInfo2.getPicture2().equals("http://10.196.5.227:3000/ZgzyPicUpload/")) {
                                nightDutyInfo.setPicture2("");
                            }
                        }

                        if (!nightDutyInfo2.getPicture2().isEmpty()) {
                            picture.add(nightDutyInfo2.getPicture2());
                        }
                        nightDutyInfo.setPicture(picture);

                        if (!nightDutyInfo2.getTime().isEmpty()) {
                            nightDutyInfo.setTime(nightDutyInfo2.getTime());
                        }
                    }
                }
                lists.add(nightDutyInfo);
            }
        }
        return lists;
    }
/*        for (int i = 0; i < list.size(); i++) {
            List<String> picture = new ArrayList<>();
            NightDutyInfo nightDutyInfo = list.get(i);
            lists.add(nightDutyInfo);
            switch (nightDutyInfo.getNGStatus()) {
                case "Y":
                    nightDutyInfo.setNGStatus("異常");
                    break;
                case "N":
                    nightDutyInfo.setNGStatus("正常");
                    break;
            }
            if (i < area.size()) {
                String s = buildings;
                s = s + "-" + floor.get(i);
                nightDutyInfo.setFloor(s);
                nightDutyInfo.setBuilding("");


                if (!nightDutyInfo.getPicture1().isEmpty()) {
                    picture.add(nightDutyInfo.getPicture1());
                }

                if (!nightDutyInfo.getPicture2().isEmpty()) {
                    if (nightDutyInfo.getPicture2().equals("http://10.196.5.227:3000/ZgzyPicUpload/")) {
                        nightDutyInfo.setPicture2("");
                    }
                }

                if (!nightDutyInfo.getPicture2().isEmpty()) {
                    picture.add(nightDutyInfo.getPicture2());
                }
                nightDutyInfo.setPicture(picture);

                if (!area.get(i).equals(nightDutyInfo.getArea())) {
                    NightDutyInfo nightDutyInfo1 = new NightDutyInfo();
                    s = buildings;
                    s = s + "-" + floor.get(i);
                    nightDutyInfo1.setFloor(s);
                    nightDutyInfo1.setWorkId(nightDutyInfo.getWorkId());
                    nightDutyInfo1.setName(nightDutyInfo.getName());
                    nightDutyInfo1.setTime("未完成簽到");
                    nightDutyInfo1.setNGStatus("異常");
                    nightDutyInfo1.setPicture1("");
                    nightDutyInfo1.setPicture2("");
                    nightDutyInfo1.setDescription("");
                    nightDutyInfo1.setDepartment(department.get(i));
                    nightDutyInfo1.setHeader(header.get(i));
                    nightDutyInfo1.setArea(area.get(i));
                    list.add(i, nightDutyInfo1);
                    lists.add(nightDutyInfo1);
                }
            }

            if (i == list.size()) {
                if (list.size() < area.size() - 1) {
                    while (true) {
                        NightDutyInfo nightDutyInfo1 = new NightDutyInfo();
                        String s = buildings;
                        s = s + "-" + floor.get(i);
                        nightDutyInfo1.setFloor(s);
                        nightDutyInfo1.setWorkId(nightDutyInfo.getWorkId());
                        nightDutyInfo1.setName(nightDutyInfo.getName());
                        nightDutyInfo1.setTime("未完成簽到");
                        nightDutyInfo1.setNGStatus("異常");
                        nightDutyInfo1.setPicture1("");
                        nightDutyInfo1.setPicture2("");
                        nightDutyInfo1.setDescription("");
                        nightDutyInfo1.setDepartment(department.get(i));
                        nightDutyInfo1.setHeader(header.get(i));
                        nightDutyInfo1.setArea(area.get(i));
                        list.add(i, nightDutyInfo1);
                        lists.add(nightDutyInfo1);
                        i++;
                        if (list.size() == area.size() - 1) {
                            break;
                        }
                    }
                }
            }
        }*/

/*        if (list.size() < area.size()) {
            NightDutyInfo nightDutyInfo1 = new NightDutyInfo();
            NightDutyInfo nightDutyInfo = list.get(list.size() - 1);
            String s = buildings;
            s = s + "-" + floor.get(area.size() - 1);
            nightDutyInfo1.setFloor(s);
            nightDutyInfo1.setWorkId(nightDutyInfo.getWorkId());
            nightDutyInfo1.setName(nightDutyInfo.getName());
            nightDutyInfo1.setTime("未完成签到");
            nightDutyInfo1.setNGStatus("異常");
            nightDutyInfo1.setPicture1("");
            nightDutyInfo1.setPicture2("");
            nightDutyInfo1.setDescription("");
            nightDutyInfo1.setDepartment(department.get(area.size() - 1));
            nightDutyInfo1.setHeader(header.get(area.size() - 1));
            nightDutyInfo1.setArea(area.get(area.size() - 1));
            list.add(area.size() - 1, nightDutyInfo1);
            lists.add(nightDutyInfo1);
        }*/


    public static List<NightDutyInfo> dataStatus(List<NightDutyInfo> finList, List<NightDutyInfo> lists, String status) {
        if (status.equals("全部")) {
            for (int i = 0; i < lists.size(); i++) {
                NightDutyInfo nightDutyInfo = lists.get(i);
                finList.add(nightDutyInfo);
            }
        }
        for (int i = 0; i < lists.size(); i++) {
            NightDutyInfo nightDutyInfo = lists.get(i);
            if (nightDutyInfo.getNGStatus().equals(status)) {
                finList.add(nightDutyInfo);
            }
        }
        return finList;
    }
}