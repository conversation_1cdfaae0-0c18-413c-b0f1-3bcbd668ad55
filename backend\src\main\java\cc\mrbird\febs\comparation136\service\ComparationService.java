package cc.mrbird.febs.comparation136.service;

import cc.mrbird.febs.comparation136.VO.CheckComparation136VO;
import cc.mrbird.febs.comparation136.VO.QueryIdComparation136VO;
import  cc.mrbird.febs.comparation136.entity.CheckComparation136;
import  cc.mrbird.febs.comparation136.entity.Comparation136QueryPage;
import cc.mrbird.febs.comparation136.entity.QueryInformation;
import  cc.mrbird.febs.comparation136.entity.SaveComparationInformation;
import cc.mrbird.febs.comparation136.result.PageResult;

public interface ComparationService {


/*    *//**
     * 电镀136对比分页查询
     * @param comparation136QueryPage
     * @return
     *//*
    PageResult pageQuery(Comparation136QueryPage comparation136QueryPage);*/

    /**
     * 电镀136对比通过id查询
     * @param id
     * @return
     */
    QueryIdComparation136VO getByIdWithInformation(Long id);

    /**
     * 新增136对比信息
     * @param saveComparationInformation
     */
    void saveComparationInformation(SaveComparationInformation saveComparationInformation);

    /**
     * 签核相关接口
     * @param checkComparation136
     * @return
     */
    CheckComparation136VO checkComparation136(CheckComparation136 checkComparation136);

    /**
     * 被驳回之后重新编辑相
     * @param saveComparationInformation
     */
    void updateComparationInformation(SaveComparationInformation saveComparationInformation);
}
