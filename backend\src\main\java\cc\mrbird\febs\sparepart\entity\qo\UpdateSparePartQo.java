package cc.mrbird.febs.sparepart.entity.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateSparePartQo {
    @ApiModelProperty(value = "表唯一ID",position = 0,example = "1")
    private Integer id;
    @ApiModelProperty(value = "操作原因",position = 1,example = "壽命到期")
    private String operationalReason;
    @ApiModelProperty(value = "操作類型",position = 2,example = "換新")
    private String operationType;
    @ApiModelProperty(value = "修改時間",position = 3,example = "2024-04-01")
    private String operationTime;
    @ApiModelProperty(value = "修改人",position = 4,example = "宇智波斑")
    private String operationUser;
    @ApiModelProperty(value = "线体",position = 5,example = "V1")
    private String productionLine;
    @ApiModelProperty(value = "设备名称",position=6,example = "自动机")
    private String equipmentName;
    @ApiModelProperty(value = "备品名称",position=7,example = "电池")
    private String sparePartName;
    @ApiModelProperty(value = "寿命上限",position = 8,example = "1000000")
    private String lifeLimit;
    @ApiModelProperty(value = "实际产量",position = 9,example = "110000")
    private String actualOutput;

}
