package cc.mrbird.febs.productionday.service.impl;

import cc.mrbird.febs.productionday.entity.CapacityTrend;
import cc.mrbird.febs.productionday.entity.CapacityTrendDto;
import cc.mrbird.febs.productionday.entity.TConfigureProductionday;
import cc.mrbird.febs.productionday.dao.TConfigureProductiondayMapper;
import cc.mrbird.febs.productionday.service.ITConfigureProductiondayService;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TConfigureProductiondayServiceImpl extends ServiceImpl<TConfigureProductiondayMapper, TConfigureProductionday> implements ITConfigureProductiondayService {

    @Autowired
    private TConfigureProductiondayMapper tConfigureProductiondayMapper;

    @Override
    public CapacityTrend queryCapacity(String discern, String date, String name) {
        Date date1= DateUtil.parseDate(date);
        Date beginMonthDate=DateUtil.beginOfMonth(date1);
        Date endMonthDate=DateUtil.endOfMonth(date1);
        int day=DateUtil.dayOfMonth(endMonthDate);
        List<Integer> PQ=new ArrayList<>();
        List<Integer> AQ=new ArrayList<>();
        List<Integer> DV=new ArrayList<>();
        List<String> achievementRate=new ArrayList<>();
        CapacityTrend capacityTrend=new CapacityTrend(PQ,AQ,DV,achievementRate);
        for(int i=0;i<day;i++){
            Date beginDate=DateUtil.offsetDay(beginMonthDate,i);
            Date beginOfDay=DateUtil.beginOfDay(beginDate);
            String begindate=DateUtil.format(beginOfDay,"yyyy-MM-dd HH:mm:ss");
            Date endOfDay=DateUtil.endOfDay(beginDate);
            String endDate=DateUtil.format(endOfDay,"yyyy-MM-dd HH:mm:ss");
            CapacityTrendDto capacityTrendDto=tConfigureProductiondayMapper.queryCapacityTrend(discern,begindate,endDate);
            if(capacityTrendDto ==null){
                capacityTrend.getAQ().add(null);
                capacityTrend.getDV().add(null);
                capacityTrend.getPQ().add(null);
                capacityTrend.getAchievementRate().add(null);
            }else{
                capacityTrend.getAQ().add(capacityTrendDto.getAQ());
                capacityTrend.getDV().add(capacityTrendDto.getDV());
                capacityTrend.getPQ().add(capacityTrendDto.getPQ());
                capacityTrend.getAchievementRate().add(capacityTrendDto.getAchievementRate());
            }
        }
        return capacityTrend;
    }
}
