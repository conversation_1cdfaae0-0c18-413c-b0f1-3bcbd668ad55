package cc.mrbird.febs.clearLine.service;

import cc.mrbird.febs.clearLine.entity.AddSheet;
import cc.mrbird.febs.clearLine.entity.pojo.CheckDtoByClearLine;

public interface ClearLineService {
    int addSheet(AddSheet addSheet);

    String checkFunctionByPL2(CheckDtoByClearLine checkDto);  //签核接口
    String checkFunctionByPL1(CheckDtoByClearLine checkDto);  //签核接口

    String rebackCheckFunction(AddSheet addSheet);
    String rebackCheckFunctionByIDS1(AddSheet addSheet);


}
