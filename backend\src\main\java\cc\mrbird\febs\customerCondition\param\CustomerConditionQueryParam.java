package cc.mrbird.febs.customerCondition.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄查詢參數
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionQueryParam {

    @ApiModelProperty(value = "產品処", example = "IDS1", position = 0)
    private String sbu;

    @ApiModelProperty(value = "機能", example = "裝配", position = 1)
    private String functions;

    @ApiModelProperty(value = "廠區", example = "寳科園區", position = 2)
    private String siteArea;

    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;

    @ApiModelProperty(value = "客戶ID", example = "1", position = 4)
    private Integer customerId;

    @ApiModelProperty(value = "客戶名稱", example = "重慶廣達", position = 5)
    private String customerName;

//    @ApiModelProperty(value = "開始日期", example = "2024-01-01", position = 6)
//    private Date startDate;
//
//    @ApiModelProperty(value = "結束日期", example = "2024-12-31", position = 7)
//    private Date endDate;

    @ApiModelProperty(value = "上傳用戶工號", example = "EMP001", position = 8)
    private String uploadUserWorkId;

    @ApiModelProperty(value = "上傳用戶名", example = "張三", position = 9)
    private String uploadUserName;
}
