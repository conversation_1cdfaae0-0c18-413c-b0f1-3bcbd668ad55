package cc.mrbird.febs.jjkpi.dao;

import cc.mrbird.febs.jjkpi.entity.DjxxDto;
import cc.mrbird.febs.jjkpi.entity.LineInfo;
import cc.mrbird.febs.jjkpi.entity.SixGdDjxx;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SixGdDjxxMapper extends BaseMapper<SixGdDjxx> {

    List<DjxxDto> queryForm(String line,String classInfo,String production);

    List<String> queryProducitonLine(String cardId);

    LineInfo queryProductionLineInfo(String pl);
}
