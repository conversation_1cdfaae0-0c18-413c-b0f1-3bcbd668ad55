package cc.mrbird.febs.customerCondition.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄數據傳輸對象
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionDto {


    @ApiModelProperty(value = "客戶條件記錄主要信息ID", position = 0)
    private Integer customerConditionMainInfoId;

    @ApiModelProperty(value = "客戶條件記錄表ID", position = 1)
    private Integer customerConditionFileId;

    @ApiModelProperty(value = "客戶條件記錄表名", position = 2)
    private String customerConditionFileName;

    @ApiModelProperty(value = "上傳日期", position = 3)
    private Date uploadDate;

    @ApiModelProperty(value = "上傳用戶工號", position = 4)
    private String uploadUserWorkId;

    @ApiModelProperty(value = "上傳用戶名", position = 5)
    private String uploadUserName;

    @ApiModelProperty(value = "產品処", position = 6)
    private String sbu;

    @ApiModelProperty(value = "機能", position = 7)
    private String functions;

    @ApiModelProperty(value = "廠區", position = 8)
    private String siteArea;

    @ApiModelProperty(value = "課別", position = 9)
    private String section;

    @ApiModelProperty(value = "客戶ID", position = 10)
    private Integer customerId;

    @ApiModelProperty(value = "客戶名稱", position = 11)
    private String customerName;

    @ApiModelProperty(value = "更新備註", position = 12)
    private String updateRemark;

    @ApiModelProperty(value = "文件URL", position = 13)
    private String fileUrl;

    @ApiModelProperty(value = "版本號", position = 14)
    private String version;
}
