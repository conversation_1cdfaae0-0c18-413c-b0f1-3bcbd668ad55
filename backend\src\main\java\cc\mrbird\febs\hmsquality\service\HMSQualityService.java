package cc.mrbird.febs.hmsquality.service;

import cc.mrbird.febs.hmsquality.entity.HmsPicture;
import cc.mrbird.febs.hmsquality.entity.dto.HMSQualityDto;
import cc.mrbird.febs.hmsquality.param.HMSQualityQueryParam;

import java.util.List;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:14
 */
public interface HMSQualityService {
    //查詢紅墨水品質記錄（帶圖片）
    List<HMSQualityDto> queryHMSQualityDataWithPic(HMSQualityQueryParam hmsQualityQueryParam);

    //根據紅墨水品質記錄ID查詢圖片
    List<HmsPicture> selectHmsPics(List<Integer> ids);

    //查詢系列
    List<String> queryProduction();

    //查詢綫體
    List<String> queryProductionLine(String production);

    //查詢日期代碼
    List<String> queryDateCode(String production, String productionLine);

    List<String> queryPFN(String production, String productionLine, String dateCode);

    List<String> queryUser(String production, String productionLine, String dateCode, String pfn);
}
