package cc.mrbird.febs.clearLine.controller;

import cc.mrbird.febs.clearLine.dao.ClearLineDataMapper;
import cc.mrbird.febs.clearLine.entity.*;
import cc.mrbird.febs.clearLine.entity.pojo.*;
import cc.mrbird.febs.clearLine.service.ClearLineService;
import cc.mrbird.febs.common.domain.FebsResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@ResponseBody
@Api(tags = "清、换线记录表")
@RequestMapping("/clearLine")
@Slf4j
public class ClearLineController {
    @Autowired
    private ClearLineDataMapper clearLineDataMapper;
    @Autowired
    private ClearLineService clearLineService;

    @PostMapping("/selectProductionByAdd")
    @ApiOperation(value = "根据pl、課別查詢系列----清换线新建")
    public FebsResponse selectProductionByAdd(@RequestBody TClearLineData clearLine) {
        List<String> list = clearLineDataMapper.selectProductionByAdd(clearLine);
        list.removeAll(Collections.singleton(null));
        return new FebsResponse().code("200").data(list);
    }

    @PostMapping("/selectProductionLineByAdd")
    @ApiOperation(value = "根據課別、系列查詢線體----清换线新建")
    public FebsResponse selectProductionLineByAdd(@RequestBody QueryLine queryLine) {
        List<String> list = clearLineDataMapper.selectProductionLineByAdd(queryLine);
        return new FebsResponse().code("200").data(list);
    }




    @PostMapping("/selectProduction")
    @ApiOperation(value = "根据pl、課別查詢系列")
    public FebsResponse selectProduction(@RequestBody TClearLineData clearLine) {
        List<String> list = clearLineDataMapper.selectProduction(clearLine);
        list.removeAll(Collections.singleton(null));
        return new FebsResponse().code("200").data(list);
    }

    @PostMapping("/selectProductionLine")
    @ApiOperation(value = "根據課別、系列查詢線體")
    public FebsResponse selectProductionLine(@RequestBody QueryLine queryLine) {
        List<String> list = clearLineDataMapper.selectProductionLine(queryLine);
        return new FebsResponse().code("200").data(list);
    }

    //新建插入接口
    @PostMapping("/addSheet")
    @ApiOperation(value = "新增填写记录")
    public FebsResponse addSheet(@RequestBody AddSheet addSheet) {
        try {
            //查询showId
            String showId;
            if (addSheet.getTDataClearLine().getPl().equals("IDS1")){
                showId=queryShowIdByName(addSheet.getTDataClearLine().getQh3());
            }else {
                showId = queryShowIdByName(addSheet.getTDataClearLine().getQh2());
            }

            //设置stNo和qhStatus  流程控制
            String stNo = "1";
            String qhStatus = "0";
            addSheet.getTDataClearLine().setQhStatus(qhStatus);
            addSheet.getTDataClearLine().setStNo(stNo);
            addSheet.getTDataClearLine().setShowId(showId);
            if (clearLineService.addSheet(addSheet) == 1) {
                return new FebsResponse().code("200").message("提交成功");
            }
            return new FebsResponse().code("500").message("提交失败");
        }catch (Exception e){
            return new FebsResponse().code("500").message("提交失败");
        }
    }

    @PostMapping("/selectAllData")
    @ApiOperation(value = "查询数据")
    public FebsResponse selectAllData(@RequestBody SelectCondition selectCondition) {
        List<SelectAllDataPojo> list = clearLineDataMapper.selectAllDataPojo(selectCondition);
        return new FebsResponse().code("200").data(list);
    }
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value="编号id,39|40|45|46",example = "46"),
    })
    @GetMapping("/selectPicURL")
    @ApiOperation(value = "查询图片地址")
    public FebsResponse selectPicURL(@RequestParam(value = "id")String id) {

       List<PicURL> picURL=clearLineDataMapper.selectClearLineByPicURL(id);
        return new FebsResponse().code("200").data(picURL);
    }




    //查询品保、课长接口
    @ApiOperation(value = "查询品保、课长接口", notes = "查询品保、课长接口")
    @GetMapping("/selectQCAndSectionManage")
    public FebsResponse selectQCAndSectionManage(@RequestParam("pl") String pl,
                                                 @RequestParam("discern")String discern
                                                 ) {
        List<String> list = null;
        if (pl.equals("IDS2")){
       list = clearLineDataMapper.selectQC(pl);
        }else if (pl.equals("IDS3") || pl.equals("IDS1")){
            list= clearLineDataMapper.selectQCByIDS3(pl);
        }
       /* List<String> list2 = clearLineDataMapper.selectSection(pl);*/
        List<String> list3 = clearLineDataMapper.selectGroupLeader(pl,discern);
        list3=list3.stream().filter(Objects::nonNull).filter(s->!s.trim().isEmpty()).collect(Collectors.toList());
       /* list2.addAll(list3);*/
        Map<String, List<String>> map = new HashMap<>();
        map.put("qcName", list);
        map.put("sectionMangeName", list3);
        return new FebsResponse().code("200").data(map);
    }

    //签核接口
    @GetMapping("/checkFunction")
    @ApiOperation(value = "签核接口", notes = "直接调用即可")
    public FebsResponse checkFunction(@RequestParam("id") String id,String pl) {
        String message;
        CheckDtoByClearLine checkDto =new CheckDtoByClearLine();
        checkDto.setId(id);
        if (pl.equals("IDS1")){
             message=clearLineService.checkFunctionByPL1(checkDto);
        }else {
            message = clearLineService.checkFunctionByPL2(checkDto);
        }
        return new FebsResponse().code("200").message(message);
    }

    @GetMapping("/goBack")
    @ApiOperation(value = "退签接口",notes = "直接传id即可")
    public FebsResponse goBack(@RequestParam("id")String id,@RequestParam("backMark") String backMark){
        String name =queryQh1(id);
        String showId=queryShowIdByName(name);
        clearLineDataMapper.goBackByQC(id,showId,backMark);
        return new FebsResponse().code("200").message("退签成功");
    }

    @PostMapping("/reBackCheck")
    @ApiOperation(value = "退签后重新发起第一次签核接口", notes = "直接调用即可")
    public FebsResponse reBackCheck(@RequestBody AddSheet addSheet) {
        String message ;
        if (addSheet.getTDataClearLine().getPl().equals("IDS1")){
            message=clearLineService.rebackCheckFunctionByIDS1(addSheet);
        }else {
            message=clearLineService.rebackCheckFunction(addSheet);
        }
        return new FebsResponse().code("200").message(message);
    }

    private String queryShowIdByName(String name) {
        String showId = clearLineDataMapper.selectShowByName(name);
        if (showId==null ||showId.equals("")){
            showId=clearLineDataMapper.selectLineLeaderNumber(name);
        } else if (showId==null ||showId.equals("")){
            showId=clearLineDataMapper.selectGroupLeaderNumber(name);
        }else if (showId==null ||showId.equals("")){
            showId=clearLineDataMapper.selectQhNumber(name);
        }
        return showId;
    }

    private String queryQh1(String id){
        return clearLineDataMapper.selectQh1(id);
    }

}