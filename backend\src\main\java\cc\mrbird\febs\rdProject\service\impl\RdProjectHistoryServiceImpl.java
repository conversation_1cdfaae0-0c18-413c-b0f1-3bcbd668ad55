package cc.mrbird.febs.rdProject.service.impl;

import cc.mrbird.febs.rdProject.dao.RdProjectHistoryMapper;
import cc.mrbird.febs.rdProject.entity.RdProjectHistory;
import cc.mrbird.febs.rdProject.service.RdProjectHistoryService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/7/21
 * @Time: 13:16
 */
@Service
public class RdProjectHistoryServiceImpl implements RdProjectHistoryService {

    @Autowired
    private RdProjectHistoryMapper mapper;

    @Async // 使用Spring异步
    @DSTransactional()
    @Override
    public void saveLog(RdProjectHistory rdProjectHistory) {
            mapper.saveHistory(rdProjectHistory);
    }

    @Override
    public PageInfo<RdProjectHistory> selectHistory(RdProjectHistory rdProjectHistory, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        return new PageInfo<>(mapper.selectHistory(rdProjectHistory));
    }
}
