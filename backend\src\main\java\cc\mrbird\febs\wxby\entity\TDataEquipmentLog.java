package cc.mrbird.febs.wxby.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;

/**
*
* <AUTHOR>
* @date 2022/11/23
 * 叫修APP数据整合实体类
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("V_Data_Equipment_MaintenanceLog")
public class TDataEquipmentLog {
        @TableField(value = "ProductionRange")
        private String productionRange;
        @TableField(value = "productionLine")
        private String productionLine;
        @TableField(value = "productionLineCode")
        private String productionLineCode;
        @TableField(value = "GroupName")
        private String groupName;
        @TableField(value = "DepartmentCode")
        private String departmentCode;
        @TableField(value = "Equipment_Name")
        private String equipmentName;
        @TableField(value = "Equipment_id")
        private String equipmentId;
        @TableField(value = "classId")
        private String classId;
        @TableField(value = "classdate")
        private String classDate;
        @TableField(value = "faultID")
        private String faultID;
        @TableField(value ="ErrorDesc")
        private String errorDesc;
        @TableField(value = "Begin_DT")
        private String beginDT;    //开始时间
        @TableField(value = "End_DT")
        private String endDT;     //结束时间
        @TableField(value = "transactor")
        private String transactor; //生技维修人员
        @TableField(value = "transactorId")
        private String transactorId;  //生技维修人员工号
        @TableField(value = "Causation")
        private String causation;          //异常原因
        @TableField(value = "Countermeasures")
        private String  counterMeasures;
        @TableField(value = "name")
        private String name;
        @TableField(value = "WorkId")
        private String WorkId;
        @TableField(value = "breakdown")
        private String breakdown;
        @TableField(value = "status1")
        private String status1;
        @TableField(value = "ErrorType")
        private String errorType;
        @TableField(value = "AutoId")
        private String AutoId;
}
