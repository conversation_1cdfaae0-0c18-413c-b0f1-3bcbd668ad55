package cc.mrbird.febs.lessDaysheet.dao;
import cc.mrbird.febs.lessDaysheet.entity.ConfigureData;
import cc.mrbird.febs.lessDaysheet.entity.LineData;
import cc.mrbird.febs.lessDaysheet.entity.dto.WorkStationDto;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
@DS("mssql")
@Mapper
public interface workStationMapper {
    List<String>  selectWorkStation(WorkStationDto workStationDto);
    List<String>  selectProductionAndProductionLine(ConfigureData configureData);

    List<String>  selectProductionLine(LineData lineData);

    List<String>  selectOrNullByProductioon(ConfigureData configureData);

    List<String> selectNULLByProductionLine(LineData lineData);

    List<String> selectWorkStationByPl2AndPl3(WorkStationDto workStationDto);
}
