package java.cc.mrbird.febs;


import cc.mrbird.febs.jjkpi.entity.DjxxDto;
import cc.mrbird.febs.jjkpi.service.ISixGdDjxxService;
import cc.mrbird.febs.line.dao.ConfigureConnectMapper;
import cc.mrbird.febs.line.entity.ConfigureConnect;
import cc.mrbird.febs.line.entity.ConfigureConnectDto;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.poi.ss.formula.functions.T;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@SpringBootTest()
public class test {
    @Autowired
    private ISixGdDjxxService iSixGdDjxxService;

    @Autowired
    private ConfigureConnectMapper configureConnectMapper;
    @Test
    public void test01(){
        Date date=new Date();
        Date beginDate= DateUtil.beginOfDay(date);
        String beginDate1=DateUtil.format(beginDate,"yyyy-MM-dd HH:mm:ss");
        Date endDate=DateUtil.endOfDay(date);
        String endDate1=DateUtil.format(endDate,"yyyy-MM-dd HH:mm:ss");
        System.out.println(beginDate1);
        System.out.println(endDate1);
    }
    @Test
    public void test02(){
        ConfigureConnectDto configureConnectDto=new ConfigureConnectDto();
        ConfigureConnect configureConnect=new ConfigureConnect();
        configureConnect.setAbnormal("asdasd");
        configureConnect.setAbnormalTalk("1111");
        configureConnectDto.setConfigureConnect(configureConnect);
        List<DjxxDto> list=new ArrayList<>();
        DjxxDto djxxDto=new DjxxDto();
        djxxDto.setCPLH_PFN("asdasdasda");
        list.add(djxxDto);
        //configureConnectDto.setDjxxDtos(list);
        JSON res=JSONUtil.parse(configureConnectDto);
        System.out.println(res);
    }
    @Test
    public void test03(){
            UUID uuid =UUID.fastUUID();
            String uuidStr=uuid.toString();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String newDate =sdf.format(new Date());
            System.out.println("Fit"+newDate);
          //  System.out.println(uuidStr);
    }
}
