package cc.mrbird.febs.securityInspection.utils;

import cc.mrbird.febs.securityInspection.entity.QueryCondition;
import cc.mrbird.febs.securityInspection.entity.SecurityInspectionExportExcelAllPoint;
import cc.mrbird.febs.securityInspection.entity.SecurityInspectionExportExcelFloor;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.PropertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel导出工具类
 */
@Slf4j
public class ExcelExportUtil {
    
    // 定义MergeInfo作为静态内部类
    private static class MergeInfo {
        int startRow;
        int endRow;
        
        MergeInfo(int startRow, int endRow) {
            this.startRow = startRow;
            this.endRow = endRow;
        }
    }

    /**
     * 导出查询点位表
     * @param dataList 需要导出的数据列表
     * @param response HTTP响应对象，用于将Excel文件返回给客户端
     * @throws IOException 如果写入过程中发生IO异常
     */
    public static void exportQueryPointTable(List<SecurityInspectionExportExcelAllPoint> dataList, HttpServletResponse response, QueryCondition queryCondition) throws IOException {
        log.info("开始导出查询点位表");

        String date = queryCondition.getDate();

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("查询点位表");

        // 标题行样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Font titleFont = workbook.createFont();
        titleFont.setFontName("楷体");
        titleFont.setColor(IndexedColors.WHITE.getIndex());
        titleFont.setFontHeightInPoints((short) 30);
        titleStyle.setFont(titleFont);

        Row titleRow = sheet.createRow(0);
        titleRow.setHeight((short) (40 * 20));
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(date + "各點位主管巡查情況");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 7));

        // 表头行样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Font headerFont = workbook.createFont();
        headerFont.setFontName("楷体");
        headerFont.setColor(IndexedColors.BLACK.getIndex());
        headerFont.setFontHeightInPoints((short) 20);
        headerStyle.setFont(headerFont);

        Row headerRow = sheet.createRow(1);
        headerRow.setHeight((short) (35 * 20));
        String[] headers = {"序號", "樓棟", "樓層", "位置", "巡查主管", "巡查任務", "巡查次數", "有異常次數"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 数据行通用样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font dataFont = workbook.createFont();
        dataFont.setFontName("楷体");
        dataFont.setFontHeightInPoints((short) 16);
        dataStyle.setFont(dataFont);

        // 红色异常样式
        CellStyle ngNumberStyle = workbook.createCellStyle();
        ngNumberStyle.setAlignment(HorizontalAlignment.CENTER);
        ngNumberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font ngFont = workbook.createFont();
        ngFont.setFontName("楷体");
        ngFont.setColor(IndexedColors.RED.getIndex());
        ngFont.setFontHeightInPoints((short) 18);
        ngNumberStyle.setFont(ngFont);

        // 黄色背景样式
        CellStyle yellowStyle = workbook.createCellStyle();
        yellowStyle.setAlignment(HorizontalAlignment.CENTER);
        yellowStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font yellowFont = workbook.createFont();
        yellowFont.setFontName("楷体");
        yellowFont.setFontHeightInPoints((short) 18);
        yellowStyle.setFont(yellowFont);
        yellowStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        yellowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 合并单元格样式（与数据样式一致）
        CellStyle mergedCellStyle = workbook.createCellStyle();
        mergedCellStyle.cloneStyleFrom(dataStyle); // 克隆基础样式
        Font mergedFont = workbook.createFont();
        mergedFont.setFontName("楷体");
        mergedFont.setFontHeightInPoints((short) 16);
        mergedCellStyle.setFont(mergedFont);
        mergedCellStyle.setAlignment(HorizontalAlignment.CENTER);
        mergedCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 开始写入数据
        int serialNumber = 1;
        String lastBuilding = null;
        String lastFloor = null;
        String lastStation = null;
        int mergeStartRow = 2;

        for (int i = 0; i < dataList.size(); i++) {
            SecurityInspectionExportExcelAllPoint data = dataList.get(i);
            Row row = sheet.createRow(i + 2);

            String currentBuilding = data.getBuilding();
            String currentFloor = data.getFloor();
            String currentStation = data.getStation();

            boolean isSameGroup = false;
            if (lastBuilding != null &&
                    lastBuilding.equals(currentBuilding) &&
                    lastFloor != null && lastFloor.equals(currentFloor) &&
                    lastStation != null && lastStation.equals(currentStation)) {
                isSameGroup = true;
            }

            // 如果不是同一组，则合并上一组，并自增序号
            if (!isSameGroup && i > 0) {
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, i + 1, 0, 0)); // 序号
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, i + 1, 1, 1)); // 楼栋
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, i + 1, 2, 2)); // 楼层
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, i + 1, 3, 3)); // 位置

                // 设置合并单元格的样式
                Row mergeRow = sheet.getRow(mergeStartRow);
                if (mergeRow != null) {
                    for (int col = 0; col <= 3; col++) {
                        Cell mergedCell = mergeRow.getCell(col);
                        if (mergedCell != null) {
                            mergedCell.setCellStyle(mergedCellStyle);
                        }
                    }
                }

                mergeStartRow = i + 2;
            }

            lastBuilding = currentBuilding;
            lastFloor = currentFloor;
            lastStation = currentStation;

            // 写入数据
            Cell indexCell = row.createCell(0);
            if (!isSameGroup) {
                indexCell.setCellValue(serialNumber); // 先赋值
                serialNumber++;                       // 立即自增
            } else {
                indexCell.setCellValue("");
            }
            indexCell.setCellStyle(dataStyle);

            row.createCell(1).setCellValue(currentBuilding);
            row.createCell(2).setCellValue(currentFloor);
            row.createCell(3).setCellValue(currentStation);
            row.createCell(4).setCellValue(data.getName());

            Cell taskCell = row.createCell(5);
            taskCell.setCellValue(data.getTask());
            taskCell.setCellStyle(yellowStyle);

            Cell numberCell = row.createCell(6);
            numberCell.setCellValue(data.getNumber());
            if (data.getTask() > data.getNumber()) {
                numberCell.setCellStyle(ngNumberStyle);
            } else {
                numberCell.setCellStyle(dataStyle);
            }

            Cell ngNumberCell = row.createCell(7);
            ngNumberCell.setCellValue(data.getNGNumber());
            if (data.getNGNumber() > 0) {
                ngNumberCell.setCellStyle(ngNumberStyle);
            } else {
                ngNumberCell.setCellStyle(dataStyle);
            }

            // 设置其他字段的样式
            for (int j = 1; j <= 4; j++) {
                Cell cell = row.getCell(j);
                if (cell != null) {
                    cell.setCellStyle(dataStyle);
                }
            }
        }

        // 处理最后一组
        if (mergeStartRow < dataList.size() + 2) {
            sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, dataList.size() + 2, 0, 0));
            sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, dataList.size() + 2, 1, 1));
            sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, dataList.size() + 2, 2, 2));
            sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, dataList.size() + 2, 3, 3));

            // 设置最后一组合并单元格的样式
            Row mergeRow = sheet.getRow(mergeStartRow);
            if (mergeRow != null) {
                for (int col = 0; col <= 3; col++) {
                    Cell mergedCell = mergeRow.getCell(col);
                    if (mergedCell != null) {
                        mergedCell.setCellStyle(mergedCellStyle);
                    }
                }
            }
        }

        // 设置列宽（基于表头内容+字体大小计算）
        double baseFontSize = 11.0;

        Row headerRowForWidth = sheet.getRow(1);
        DataFormatter formatter = new DataFormatter();

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRowForWidth.getCell(i);
            if (cell == null) continue;

            String content = formatter.formatCellValue(cell);
            int fontSize = 20; // 表头统一为20号字
            double scaleFactor = fontSize / baseFontSize;
            int charWidth = calculateCharWidth(content);
            int scaledWidth = (int) Math.ceil(charWidth * scaleFactor * 1.1);
            int columnWidth = Math.min(scaledWidth, 255);
            sheet.setColumnWidth(i, columnWidth * 256 + 200);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String fileName = "查询点位表_" + DateUtil.format(new Date(), "yyyyMMdd") + ".xlsx";
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 导出数据
        workbook.write(response.getOutputStream());
        workbook.close();

        log.info("查询点位表导出成功");
    }



    /**
     * 导出查询楼层表
     */
    public static void exportQueryFloorTable(List<SecurityInspectionExportExcelFloor> dataList, HttpServletResponse response, QueryCondition queryCondition) throws IOException {
        log.info("开始导出查询点位表");

        String date = queryCondition.getDate();

        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("查询楼层表");

        // 第一行：标题
        Row titleRow = sheet.createRow(0);
        titleRow.setHeight((short) (40 * 20));
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(date + "各楼层主管巡查情況");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

        // 标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setFontName("楷体");
        titleFont.setColor(IndexedColors.WHITE.getIndex());
        titleFont.setFontHeightInPoints((short) 30); // 大号字体
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleCell.setCellStyle(titleStyle);

        // 第二行：表头
        Row headerRow = sheet.createRow(1);
        headerRow.setHeight((short) (35 * 20));
        String[] headers = {"序號", "樓棟", "樓層", "巡查主管", "應巡查點位數（任務）", "巡查次數", "有異常次數"};

        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setFontName("楷体");
        headerFont.setFontHeightInPoints((short) 20); // 表头字体
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setWrapText(false); // 不换行

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        Font dataFont = workbook.createFont();
        dataFont.setFontName("楷体");
        dataFont.setFontHeightInPoints((short) 16); // 数据行字体
        dataStyle.setFont(dataFont);
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setWrapText(false);

        // 合并后的单元格样式
        CellStyle mergedStyle = workbook.createCellStyle();
        mergedStyle.cloneStyleFrom(dataStyle);
        Font mergedFont = workbook.createFont();
        mergedFont.setFontHeightInPoints((short) 16);
        mergedStyle.setFont(mergedFont);
        mergedStyle.setAlignment(HorizontalAlignment.CENTER);
        mergedStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 异常红色样式
        CellStyle ngNumberStyle = workbook.createCellStyle();
        ngNumberStyle.cloneStyleFrom(dataStyle);
        Font redFont = workbook.createFont();
        redFont.setFontName("楷体");
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setFontHeightInPoints((short) 18);
        ngNumberStyle.setFont(redFont);

        // 黄色背景样式
        CellStyle yellowStyle = workbook.createCellStyle();
        yellowStyle.cloneStyleFrom(dataStyle);
        yellowStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        yellowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 序号特殊样式
        CellStyle serialNumStyle = workbook.createCellStyle();
        serialNumStyle.cloneStyleFrom(dataStyle);
        serialNumStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        serialNumStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 合并控制变量
        int serialNum = 1;
        int mergeStartRow = -1;
        String lastBuilding = null;
        String lastFloor = null;

        if (dataList != null && !dataList.isEmpty()) {
            for (int i = 0; i < dataList.size(); i++) {
                SecurityInspectionExportExcelFloor data = dataList.get(i);
                Row row = sheet.createRow(i + 2); // 从第3行开始写入数据

                String currentBuilding = data.getBuilding() == null ? "" : data.getBuilding();
                String currentFloor = data.getFloor() == null ? "" : data.getFloor();

                boolean isNewGroup = false;
                if (!currentBuilding.equals(lastBuilding) || !currentFloor.equals(lastFloor)) {
                    isNewGroup = true;
                }

                if (isNewGroup && mergeStartRow != -1) {
                    int endRow = i + 1;
                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, endRow, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, endRow, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, endRow, 2, 2));

                    Row mergeRow = sheet.getRow(mergeStartRow);
                    for (int col = 0; col <= 2; col++) {
                        Cell cell = mergeRow.getCell(col);
                        if (cell != null) {
                            cell.setCellStyle(mergedStyle);
                        }
                    }
                }

                if (isNewGroup) {
                    mergeStartRow = i + 2;
                }

                // 写入数据
                Cell indexCell = row.createCell(0);
                indexCell.setCellValue(isNewGroup ? String.valueOf(serialNum++) : "");
                indexCell.setCellStyle(isNewGroup ? serialNumStyle : dataStyle);

                Cell buildingCell = row.createCell(1);
                buildingCell.setCellValue(currentBuilding);
                buildingCell.setCellStyle(dataStyle);

                Cell floorCell = row.createCell(2);
                floorCell.setCellValue(currentFloor);
                floorCell.setCellStyle(dataStyle);

                Cell nameCell = row.createCell(3);
                nameCell.setCellValue(data.getName() == null ? "" : data.getName());
                nameCell.setCellStyle(dataStyle);

                Cell taskCell = row.createCell(4);
                taskCell.setCellValue(data.getTask());
                taskCell.setCellStyle(yellowStyle);

                Cell numberCell = row.createCell(5);
                numberCell.setCellValue(data.getNumber());
                numberCell.setCellStyle(data.getNumber() < data.getTask() ? ngNumberStyle : dataStyle);

                Cell ngNumberCell = row.createCell(6);
                ngNumberCell.setCellValue(data.getNGNumber());
                ngNumberCell.setCellStyle(data.getNGNumber() > 0 ? ngNumberStyle : dataStyle);

                lastBuilding = currentBuilding;
                lastFloor = currentFloor;
            }

            // 处理最后一组合并
            if (mergeStartRow != -1) {
                int endRow = dataList.size() + 1;
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, endRow, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, endRow, 1, 1));
                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, endRow, 2, 2));

                Row mergeRow = sheet.getRow(mergeStartRow);
                for (int col = 0; col <= 2; col++) {
                    Cell cell = mergeRow.getCell(col);
                    if (cell != null) {
                        cell.setCellStyle(mergedStyle);
                    }
                }
            }
        }

        // ✅ 使用预定义字体大小来计算列宽（不再调用 getFont()）
        double baseFontSize = 11.0; // Excel 默认字号单位基准

        Row headerRowForWidth = sheet.getRow(1);
        DataFormatter formatter = new DataFormatter();

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRowForWidth.getCell(i);
            if (cell == null) continue;

            String content = formatter.formatCellValue(cell);

            // 手动指定该列的字体大小（根据你的样式设定）
            int fontSize;
            switch (i) {
                case 0: fontSize = 20; break; // 序號
                case 1: fontSize = 20; break; // 樓棟
                case 2: fontSize = 20; break; // 樓層
                case 3: fontSize = 20; break; // 巡查主管
                case 4: fontSize = 20; break; // 應巡查點位數（任務）
                case 5: fontSize = 20; break; // 巡查次數
                case 6: fontSize = 20; break; // 有異常次數
                default: fontSize = 20; break;
            }

            double scaleFactor = fontSize / baseFontSize;
            int charWidth = calculateCharWidth(content);
            int scaledWidth = (int) Math.ceil(charWidth * scaleFactor * 1.1); // 加10%余量
            int columnWidth = Math.min(scaledWidth, 255); // 最大255字符
            sheet.setColumnWidth(i, columnWidth * 256 + 200); // 设置列宽
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" +
                java.net.URLEncoder.encode("查询楼栋表_" + new Date().getTime() + ".xlsx", "UTF-8"));

        // 输出文件
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    // ✅ 工具方法：计算字符串显示宽度（中文算2，英文算1）
    private static int calculateCharWidth(String str) {
        if (str == null || str.isEmpty()) return 0;
        int width = 0;
        for (char c : str.toCharArray()) {
            if ((c >= 0x4E00 && c <= 0x9FFF) ||
                    (c >= 0x3400 && c <= 0x4DBF) ||
                    (c >= 0xF900 && c <= 0xFAFF) ||
                    (c >= 0xFF00 && c <= 0xFFEF)) {
                width += 2; // 中文字符算2个单位
            } else {
                width += 1; // 英文字符算1个单位
            }
        }
        return width;
    }

}
