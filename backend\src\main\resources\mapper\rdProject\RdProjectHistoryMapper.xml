<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectHistoryMapper">

    <insert id="saveHistory">
        INSERT INTO rd_project_history (history_id, content, time,user_name,worker_id,site_area,sbu,functions,section)
        VALUES (#{historyId}, #{content}, #{time}, #{userName}, #{workerId}, #{siteArea}, #{sbu}, #{functions}, #{section})
    </insert>
    <select id="selectHistory" resultType="cc.mrbird.febs.rdProject.entity.RdProjectHistory">
        SELECT
        history_id,
        content,
        time,
        user_name,
        worker_id,
        site_area,
        sbu,
        functions,
        section
        FROM rd_project_history
        <where>
            <!-- 精确匹配条件 -->
            <if test="condition.historyId != null">
                AND history_id = #{condition.historyId}
            </if>
            <if test="condition.userName != null and condition.userName != ''">
                AND user_name = #{condition.userName}
            </if>
            <if test="condition.workerId != null and condition.workerId != ''">
                AND worker_id = #{condition.workerId}
            </if>

            <!-- 模糊匹配条件 -->
            <if test="condition.siteArea != null and condition.siteArea != ''">
                AND site_area LIKE CONCAT('%', #{condition.siteArea}, '%')
            </if>
            <if test="condition.sbu != null and condition.sbu != ''">
                AND sbu LIKE CONCAT('%', #{condition.sbu}, '%')
            </if>
            <if test="condition.functions != null and condition.functions != ''">
                AND functions LIKE CONCAT('%', #{condition.functions}, '%')
            </if>
            <if test="condition.section != null and condition.section != ''">
                AND section LIKE CONCAT('%', #{condition.section}, '%')
            </if>

            <!-- 日期范围查询 -->
            <if test="condition.time != null">
                AND time >= #{condition.time}
            </if>
        </where>
        ORDER BY time desc
    </select>
</mapper>