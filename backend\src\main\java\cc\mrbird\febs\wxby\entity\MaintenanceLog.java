package cc.mrbird.febs.wxby.entity;
/***
 * 如果swagger-ui没有显示注解@ApiModelProperoty 里的内容,则字段名要以小写开头,遵循驼峰命名规则.
 * */
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "T_Equipment_Maintenance")
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("插入表")
public class MaintenanceLog {

            @ApiModelProperty(value = "系列",required=true,position = 1)
            @TableField(value = "Production")
            private String production;
            @ApiModelProperty(value = "线体",required = true,position = 2)
            @TableField(value = "ProductionLine")
            private String productionLine;
            @ApiModelProperty(value = "填写日期",required = true,position = 3)
            @TableField(value = "RecordDate")
            private String recordDate;
            @TableField(value = "ClassInfo")
            @ApiModelProperty(value = "班别",required = true,position = 4)
            private String classInfo;
            @TableField(value = "EquipmentName")
            @ApiModelProperty(value = "设备名称",required = true,position = 5)
            private String equipmentName;
            @TableField(value = "EquipmentId")
            @ApiModelProperty(value = "设备编号",required = true,position = 6)
            private String equipmentId;
             @TableField(value = "SCLH")
             @ApiModelProperty(value = "生产料号",required = true,position = 7)
            private String sclh;
            @TableField(value = "StartTime")
            @ApiModelProperty(value = "开始时间",required = true,position = 8)
            private String startTime;
            @TableField(value = "EndTime")
            @ApiModelProperty(value = "结束时间",required = true,position = 9)
            private String endTime;
            @TableField(value = "LossTime")
            @ApiModelProperty(value = "损失时间",required = true,position = 10)
            private String lossTime;
            @TableField(value = "MaintenanceNature")
            @ApiModelProperty(value = "维修保养性质",required = true,position =11)
            private String maintenanceNature;
            @TableField(value = "ChangeStation")
            @ApiModelProperty(value = "变更工位",required = true,position = 12)
            private String changeStation;
            @TableField(value = "ChangeNumber")
            @ApiModelProperty(value = "变更编号",required = true,position = 13)
            private String changeNumber;
            @TableField(value = "FaultType")
            @ApiModelProperty(value = "故障类型",required = true,position = 14)
            private String faultType;
            @TableField(value = "FaultCause")
            @ApiModelProperty(value = "故障内容",required = true,position = 15)
            private String faultCause;
            @TableField(value = "CounterPlan")
            @ApiModelProperty(value = "解决对策",required = true,position = 16)
            private String counterPlan;
            @TableField(value = "STATUS")
            @ApiModelProperty(value = "状态",required = true,position = 17)
            private String status;
            @TableField(value = "QH1")
            @ApiModelProperty(value = "线长签核状态",required = true,position = 18)
            private String qh1;       //线长 =>  I 1 true
            @TableField(value = "QH2")
            @ApiModelProperty(value = "责任生技签核状态",required = true,position = 19)
            private String qh2;
            @TableField(value = "QH3")
            @ApiModelProperty(value = "维修審核狀態",required = true,position = 20)
            private String qh3;
            @TableField(value = "QH4")
            @ApiModelProperty(value = "維修核准狀態",required = true,position = 21)
            private String qh4;
            @TableField(value = "QH5")
            @ApiModelProperty(value = "品管确认人签核状态",required = true,position = 38)
            private String qh5;
            @TableField(value = "FQR")
            @ApiModelProperty(value = "線長名字",required = true,position = 22)
            private String fqr;
            @TableField(value = "ZRSJ")
            @ApiModelProperty(value = "責任生技名字",required = true,position = 23)
            private String zrsj;
            @TableField(value = "WXSH")
            @ApiModelProperty(value = "維修審核名字",required = true,position = 24)
            private String wxsh;
            @TableField(value = "WXHZ")
            @ApiModelProperty(value = "维修核准人",required = true,position = 25)
            private String wxhz;
            @TableField(value = "PGQRR")
            @ApiModelProperty(value = "品管確認人名字",required = true,position = 26)
            private String pgqrr;
            @TableField(value = "PGOpinion")
            @ApiModelProperty(value = "品管签核意见",required = true,position = 27)
            private String pgopinion;
            @TableField(value = "TraceStartTime")
            @ApiModelProperty(value = "追溯开始时间",required = true,position = 28)
            private String traceStartTime;
            @TableField("TraceEndTime")
            @ApiModelProperty(value = "追溯结束时间",required = true,position = 29)
            private String traceEndTime;
            @TableField(value = "QualityIdea")
            @ApiModelProperty(value = "品質確認與判定(QE)",required = true,position = 30)
            private String qualityIdea ;
            @TableField(value = "ProductionDC")
            @ApiModelProperty(value = "成品D/C",required = true,position = 31)
            private String productionDC;
            @TableField(value = "ProductionNumber")
            @ApiModelProperty(value = "成品数量",required = true,position = 32)
            private String productionNumber;
            @TableField(value = "NProductionDC")
            @ApiModelProperty(value = "半成品D/C",required = true,position = 33)
            private String nproductionDC;
            @TableField(value = "NProductionNumber")
            @ApiModelProperty(value = "半成品数量",required = true,position = 34)
            private String nproductionNumber;
            @TableField(value = "ProductionMessage")
            @ApiModelProperty(value = "產品追溯",required = true,position = 35)
            private String productionMessage;
            @TableField(value = "QH11")
            @ApiModelProperty(value = "線長簽核狀態BY品保介入情況下",required = true,position = 36)
            private String qh11;
            @TableField(value = "QH22")
            @ApiModelProperty(value = "生產組長簽核狀態BY品保介入情況下",required = true,position = 37)
            private String qh22;
            @TableId(type = IdType.AUTO,value = "Id")
            @ApiModelProperty(value = "主键Id")
            private Integer id;
            @TableField(value = "GroupLeader")
            @ApiModelProperty(value = "生產組長名字",required = true,position = 39)
            private String groupLeader;
            @TableField(value = "QH33")
            @ApiModelProperty(value = "重工追溯狀態",required = true,position = 40)
            private String qh33;
            @ApiModelProperty(value = "事業處",required = true,position = 41)
            private String pl;
            @ApiModelProperty(value = "課別",required = true,position = 42)
            private String discern;
            @ApiModelProperty(value = "危险能源",required = true,position = 44)
            private String wxny;
            @ApiModelProperty(value = "隔离方式",required = true,position = 45)
            private String glfs;
            @ApiModelProperty(value = "上锁部位",required = true,position = 46)
            private String ssbw;
            private String th1;
            private String th2;
            private String th3;
            private String th4;
            private String th5;
            private String th6;
            private String th7;
            private String th8;
            private String th9;
            private String th10;
            private String th1Text;
            private String th2Text;
            private String th3Text;
            private String th4Text;
            private String th5Text;
            private String th6Text;
            private String th7Text;
            private String th8Text;
            private String th9Text;
            private String th10Text;
            private String other;
            private String dataType;
            private Integer insertId;




    public MaintenanceLog(MaintenanceLog maintenanceLog) {
    }

    public MaintenanceLog(Object o) {
    }
}
