package cc.mrbird.febs.line.controller;


import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.common.exception.FebsException;
import cc.mrbird.febs.line.dao.ConfigureConnectMapper;
import cc.mrbird.febs.line.entity.ConfigureConnect;
import cc.mrbird.febs.line.entity.ConfigureConnectDto;
import cc.mrbird.febs.line.entity.TConfigureClasskpi;
import cc.mrbird.febs.opeationalLogs.entity.TOpeationalLogsLine;
import cc.mrbird.febs.opeationalLogs.service.ITOpeationalLogsLineService;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/line/configure-connect")
@ResponseBody
@Slf4j
@RequiresPermissions("zyc")
public class ConfigureConnectController {
    private String message;
     @Autowired
    private ConfigureConnectMapper configureConnectMapper;

     @Autowired
     private ITOpeationalLogsLineService itOpeationalLogsLineService;

     @GetMapping("/query")
     @ApiOperation(value = "获取暂存线体相关信息",notes = "获取暂存线体相关信息")
     @ApiResponses({
             @ApiResponse(code = 200,message = "ok",response = ConfigureConnectDto.class)
     })
     public FebsResponse query(@RequestParam("discern")String discern,@RequestParam("production") String production,
                               @RequestParam("line") String line,@RequestParam("classInfo") String classInfo){
         ConfigureConnect configureConnect=configureConnectMapper.queryConfigureConnect(discern,production,line,classInfo);
         if(configureConnect!=null){
             String kpiId=configureConnect.getClasskpiId();
             List<TConfigureClasskpi> list=configureConnectMapper.queryKpiList(kpiId);
             ConfigureConnectDto configureConnectDto=new ConfigureConnectDto(configureConnect,list);
             return new FebsResponse().data(configureConnectDto);
         }
         else{
             return new FebsResponse().data(configureConnect);
         }

     }

    @GetMapping("/queryByCalender")
    @ApiOperation(value = "根據日曆時間查詢鎖定表數據",notes = "根據日曆時間查詢鎖定表數據")
    @ApiResponses({
            @ApiResponse(code = 200,message = "ok",response = ConfigureConnectDto.class)
    })
    public FebsResponse queryByCalender(@RequestParam("discern")String discern,@RequestParam("production") String production,
                              @RequestParam("line") String line,@RequestParam("classInfo") String classInfo,@RequestParam("time")String time){
        Date date=DateUtil.parse(time);
        Date beginDate=DateUtil.beginOfDay(date);
        Date endDate=DateUtil.endOfDay(date);
        String beginDate1=DateUtil.format(beginDate,"yyyy-MM-dd HH:mm:ss");
        String endDate1=DateUtil.format(endDate,"yyyy-MM-dd HH:mm:ss");
        ConfigureConnect configureConnect=configureConnectMapper.queryConfigureConnectLock(discern,production,line,classInfo,beginDate1,endDate1);
        if(configureConnect!=null){
            String kpiId=configureConnect.getClasskpiId();
            List<TConfigureClasskpi> list=configureConnectMapper.queryKpiListLock(kpiId);
            ConfigureConnectDto configureConnectDto=new ConfigureConnectDto(configureConnect,list);
            return new FebsResponse().data(configureConnectDto);
        }
        else{
            return new FebsResponse().data(configureConnect);
        }

    }

     @PostMapping("/add")
     @ApiOperation(value = "暂存线体相关信息",notes = "暂存线体相关信息")
     @ApiResponses({
             @ApiResponse(code = 200,message = "ok",response = ConfigureConnectDto.class)
     })
    public FebsResponse add(@RequestBody  ConfigureConnectDto configureConnectDto) throws FebsException {
         try {
             TOpeationalLogsLine tOpeationalLogsLine=new TOpeationalLogsLine(null,configureConnectDto.getConfigureConnect().getName(),"暂存表单",new Date());
             itOpeationalLogsLineService.save(tOpeationalLogsLine);
             int count=configureConnectMapper.countConfigure(configureConnectDto.getConfigureConnect());
             String discern=configureConnectDto.getConfigureConnect().getDiscern();
             String production=configureConnectDto.getConfigureConnect().getProduction();
             String productionLine=configureConnectDto.getConfigureConnect().getProductionLine();
             String classInfo=configureConnectDto.getConfigureConnect().getClassInfo();
             if(count>0){
                 //先删除kpi表中的数据
                 String kpi=configureConnectMapper.queryKpi(discern,production,productionLine,classInfo);
                 configureConnectMapper.delKpi(kpi);
                 //生成新的kpiId和configureConnect数据
                 UUID uuid=UUID.randomUUID();
                 String id=uuid.toString();
                 configureConnectDto.getConfigureConnect().setClasskpiId(id);
                 configureConnectMapper.updateConfigure(configureConnectDto.getConfigureConnect());

                 //kpiId赋值
                 for(TConfigureClasskpi tConfigureClasskpi:configureConnectDto.getClasskpiList()){
                     tConfigureClasskpi.setId(id);
                 }
                 //kpi插入
                 configureConnectMapper.insertKpi(configureConnectDto.getClasskpiList());
                 return new FebsResponse().code("200").message("修改交接表成功").status("success");
             }else{
                 UUID uuid=UUID.randomUUID();
                 List<TConfigureClasskpi> list=configureConnectDto.getClasskpiList();
                 String id=uuid.toString();
                 for(TConfigureClasskpi tc:list){
                     tc.setId(id);
                 }
                 configureConnectDto.getConfigureConnect().setClasskpiId(id);
                 configureConnectMapper.insertKpi(list);
                 configureConnectMapper.insertConfigureConnect(configureConnectDto.getConfigureConnect());
                 return new FebsResponse().code("200").message("新增交接表成功").status("success");
             }
         } catch (Exception e) {
             message = "新增交接表失败";
             log.error(message, e);
             throw new FebsException(message);
         }
     }

    @PostMapping("/lock")
    @ApiOperation(value = "锁定线体相关信息",notes = "锁定线体相关信息")
    @ApiResponses({
            @ApiResponse(code = 200,message = "ok",response = ConfigureConnectDto.class)
    })
    public FebsResponse lock(@RequestBody  ConfigureConnectDto configureConnectDto) throws FebsException {
        try {
            TOpeationalLogsLine tOpeationalLogsLine=new TOpeationalLogsLine(null,configureConnectDto.getConfigureConnect().getName(),"锁定表单",new Date());
            itOpeationalLogsLineService.save(tOpeationalLogsLine);
            //查询现在的时间
            Date date=new Date();
            Date begin=DateUtil.beginOfDay(date);
            Date end=DateUtil.endOfDay(date);
            String beginDate= DateUtil.format(begin,"yyyy-MM-dd HH:mm:ss");
            String endDate=DateUtil.format(end,"yyyy-MM-dd HH:mm:ss");
            String discern=configureConnectDto.getConfigureConnect().getDiscern();
            String production=configureConnectDto.getConfigureConnect().getProduction();
            String productionLine=configureConnectDto.getConfigureConnect().getProductionLine();
            String classInfo=configureConnectDto.getConfigureConnect().getClassInfo();
            int count=configureConnectMapper.countConfigureLock(discern,production,productionLine,classInfo,beginDate,endDate);
            if(count>0){
                return new FebsResponse().code("402").message("已經鎖定了");
            }else{
                UUID uuid=UUID.randomUUID();
                List<TConfigureClasskpi> list=configureConnectDto.getClasskpiList();
                String id=uuid.toString();
                for(TConfigureClasskpi tc:list){
                    tc.setId(id);
                }
                configureConnectDto.getConfigureConnect().setClasskpiId(id);
                configureConnectMapper.insertKpiLock(list);
                configureConnectMapper.insertConfigureConnectLock(configureConnectDto.getConfigureConnect());
                return new FebsResponse().code("200").message("新增锁定的交接表成功").status("success");
            }
        } catch (Exception e) {
            message = "新增交接表失败";
            log.error(message, e);
            throw new FebsException(message);
        }
    }
    //已经提交的数据
    @ApiOperation(value ="日历查询提交情况",notes = "日历查询提交情况")
    @GetMapping("/queryCalender")
    public FebsResponse queryCalender(@RequestParam("discern")String discern,
                                      @RequestParam("production")String production,
                                      @RequestParam("line")String line,
                                      @RequestParam("classInfo")String classInfo,
                                      @RequestParam("dateTime")String date){
        Date date1=DateUtil.parseDate(date);
        Date beginMonthDate=DateUtil.beginOfMonth(date1);
        Date endMonthDate=DateUtil.endOfMonth(date1);
        int day=DateUtil.dayOfMonth(endMonthDate);
        List<Integer> list=new ArrayList<>();
        for(int i=0;i<day;i++){
            Date beginDate=DateUtil.offsetDay(beginMonthDate,i);
            Date beginOfDay=DateUtil.beginOfDay(beginDate);
            String beginDateTime=DateUtil.format(beginOfDay,"yyyy-MM-dd HH:mm:ss");
            Date endOfDay=DateUtil.endOfDay(beginDate);
            String endDateTime=DateUtil.format(endOfDay,"yyyy-MM-dd HH:mm:ss");
            int count=configureConnectMapper.countConfigureLock(discern,production,line,classInfo,beginDateTime,endDateTime);
            if(count>0){
                list.add(1);
            }else{
                list.add(0);
            }
        }
        return new FebsResponse().data(list);
    }

    @PostMapping("/uploadImage")
    public FebsResponse uploadImage(MultipartFile file, HttpServletRequest request){
             String fileName=file.getOriginalFilename();
             String suffixName=fileName.substring(fileName.lastIndexOf("."));
             fileName=UUID.randomUUID()+suffixName;
             String filePath="D:\\pictures\\";
             try{
                 file.transferTo(new File(filePath+fileName));
                 return new FebsResponse().message("上传成功");
             }catch (Exception e){
                 e.printStackTrace();
                 return new FebsResponse().message("上传失败");
             }
    }
}
