<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.jjkpi.dao.SixGdDjxxMapper">
    <resultMap id="DJxxDto" type="cc.mrbird.febs.jjkpi.entity.DjxxDto">
        <result column="SCSL_QOP" jdbcType="DECIMAL" property="SCSL_QOP"/>
        <result column="CPLH_PFN" jdbcType="VARCHAR" property="CPLH_PFN"/>
        <result column="KHMC_CN" jdbcType="VARCHAR" property="KHMC_CN"/>
        <result column="KHLH_CMN" jdbcType="VARCHAR" property="KHLH_CMN"/>
        <result column="GLBH_WON" jdbcType="VARCHAR" property="GLBH_WON"/>
        <result column="RUKUDANHAO" jdbcType="VARCHAR" property="RUKUDANHAO"/>
        <result column="DC_SCDC" jdbcType="VARCHAR" property="DC_SCDC"/>
    </resultMap>

    <select id="queryForm" resultMap="DJxxDto">
        select distinct SCSL_QOP,CPLH_PFN,KHMC_CN,KHLH_CMN,GLBH_WON,DC_SCDC,RUKUDANHAO
        FROM [Android_App].[dbo].[Six_TD_DJXX]
        <where>
            <if test="line!=null and line!=''">
                and  line like concat('%','${line}','%')
            </if>
            <if test="classInfo!=null and classInfo!=''">
                and ProductClass<![CDATA[  =  ]]>#{classInfo}
            </if>
            <if test="production!=null and production!=''">
                and series<![CDATA[  =  ]]>#{production}
            </if>
            AND DATE_SCRQ >=CONVERT(date,getdate()-2)
        </where>
    </select>
    <select id="queryProducitonLine" resultType="java.lang.String">
        select ProductionLine
        from T_Configure_Equipment
        where LineLeaderNumber=#{cardId}
    </select>
    <select id="queryProductionLineInfo" resultType="cc.mrbird.febs.jjkpi.entity.LineInfo">
        select DISTINCT (section,series,line,CPLH_PFN)
        from
        Six_TD_DJXX
        where line=#{line}
    </select>
</mapper>
