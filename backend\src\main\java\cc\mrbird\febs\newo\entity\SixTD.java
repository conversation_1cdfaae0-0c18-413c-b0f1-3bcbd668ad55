package cc.mrbird.febs.newo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("Six_TD_DJXX")
public class SixTD {
    @TableField(value = "series")
     private String       series;
    @TableField(value = "line")
     private String       line;
    @TableField(value = "CPLH_PFN")
     private String     CPLH_PFN;
    @TableField(value = "ProductClass")
     private String       ProductClass;
    @TableField(value = "GLBH_WON")
     private String     GLBH_WON;
    @TableField(value = "KHMC_CN")
     private String     KHMC_CN;
    @TableField(value = "SCSL_QOP")
     private String    SCSL_QOP;
    @TableField(value = "RUKUDANHAO")
     private String     RUKUDANHAO;
    @TableField(value = "DATE_SCRQ")
     private String      DATE_SCRQ;
    @TableField(value = "KHLH_CMN")
    private String KHLH_CMN;
}
