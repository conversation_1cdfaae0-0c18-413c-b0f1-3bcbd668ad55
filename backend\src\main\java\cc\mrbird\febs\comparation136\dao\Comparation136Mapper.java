package cc.mrbird.febs.comparation136.dao;

import cc.mrbird.febs.comparation136.VO.QueryIPQCVO;
import cc.mrbird.febs.comparation136.VO.QueryIdComparation136VO;
import cc.mrbird.febs.comparation136.VO.QueryInformationVO;
import cc.mrbird.febs.comparation136.VO.TypePerson;
import cc.mrbird.febs.comparation136.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;

import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper
@DS("mssql-paperless")
public interface Comparation136Mapper {


    /**
     *通過工號查詢產品處和機能
     * @param insUserId
     * @return
     */
    /*QueryByWorkIdVO queryByWorkId(String insUserId);*/

    /**
     * 136對比表通過id查詢詳情數據
     * @param id
     * @return
     */
    QueryIdComparation136VO selectById(Long id);

    /**
     * 136對比表通過id查詢圖片信息
     * @param id
     * @return
     */
    List<ImageInformation> queryImageInformation(Long id);


    /**
     * 136對比表新增
     * @param saveComparationInformation
     */
    void saveInformation(SaveComparationInformation saveComparationInformation);

    /**
     * 查詢新增數據的id
     * @param saveComparationInformation
     * @return
     */
    Long QueryId(SaveComparationInformation saveComparationInformation);

    /**
     * 數據創建成功後，更新簽核狀態
     * @param id
     */
    void updateStatus(Long id);

    /**
     * 通過前端傳過來的id數據查詢該筆單據的簽核狀態
     * @param checkComparation136
     */
    Integer selectStatusById(CheckComparation136 checkComparation136);

    /**
     * 通過id來改變簽核狀態
     * @param id
     * @param status
     */
    void updateCheckStatus(Long id, Integer status);

    /**
     * 根據前端傳過來的數據查詢退單人工號，名字
     * @param id
     * @return
     */
    String selectTDRName(Long id);

    /**
     * 獲取退單人工號
     * @param id
     * @return
     */
    String selectTDRWorkId(Long id);
    /**
     * 向退單表中插入一條數據
     * @param tdInformation
     */
    void insertTDInformation(TDInformation tdInformation);


    /**
     * 獲取退單組長名字
     * @param id
     * @return
     */
    String selectTDZZName(Long id);

    /**
     * 獲取退單組長工號
     * @param id
     * @return
     */
    String selectTDZZWorkId(Long id);

    /**
     * 被駁回之後重新編輯數據
     * @param saveComparationInformation
     */
    void updateInformation(SaveComparationInformation saveComparationInformation);

    /**
     * 查询组长品保信息
     * @param LX_Type
     * @param section
     * @return
     */
    ArrayList<String> queryPersonInformation(String LX_Type, String section);

    /**
     * 查詢產品料號的信息
     * @param cplhPfn
     * @return
     */
    ArrayList<String> queryMaterialNumber(String cplhPfn);

    /**
     * 136對比表按日期查詢
     * @param bdDate
     * @return
     */
    ArrayList<String> queryDate(String bdDate);


    /**
     * 通過課別查詢品保名字
     * @param section
     * @return
     */
    List<QueryIPQCVO> queryIPQC(String section);

    /**
     * 通過課別查詢組長名字
     * @param section
     * @return
     */
    ArrayList<QueryIPQCVO> queryZZ(String section);

    /**
     * 136對比表按課別、班別、料號、日期、線體查詢
     * @param comparation136QueryPage
     * @return
     */
    List<QueryInformationVO> queryPageInformation(Comparation136QueryPage comparation136QueryPage);

    /**
     * 通過工號查詢課別
     * @param workId
     * @return
     */
    String querySection(String workId);

    /**
     * 通過課別查詢線體
     * @param section
     * @return
     */
    ArrayList<String> queryLine(String section);

    /**
     * 通过课别线体查询料号信息
     * @param queryMaterialNumber
     * @return
     */
    ArrayList<String> selectMaterialNumber (QueryMaterialNumber queryMaterialNumber);

    /**
     * 通过id跟新写入信息为PC
     * @param id
     */
    void updateWriter(Long id);

    /**
     * 通过工号查询组织
     * @param workId
     * @return
     */
    String querySBU(String workId);

    /**
     * 通过工号查询机能
     * @param workId
     * @return
     */
    String queryFunctions(String workId);

    /**
     * 通過id來查詢相關錢和人的工號
     * @param id
     * @return
     */
    List<QueryWork> queryWorkId(Long id);


    /**
     * 查詢駁回人信息
     * @param id
     * @return
     */
    List<TypePerson> queryType(Long id);





}
