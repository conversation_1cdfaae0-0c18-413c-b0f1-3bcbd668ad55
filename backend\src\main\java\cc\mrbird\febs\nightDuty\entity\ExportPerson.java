package cc.mrbird.febs.nightDuty.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "值夜人员表")
public class ExportPerson implements Serializable {

    @ExcelProperty("廠區")
    private String factory;

    @ExcelProperty("巡視區域")
    //A4-A5
    private String building;

    @ExcelProperty("巡視日期")
    //日期
    private String date;

    @ExcelProperty("工號")
    //工号
    private String workId;

    @ExcelProperty("巡視人員")
    //名字
    private String name;

}
