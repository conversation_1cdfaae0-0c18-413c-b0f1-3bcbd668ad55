package cc.mrbird.febs.backwork.common;



import cc.mrbird.febs.backwork.entity.SendPost;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;


import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
@Slf4j
public class FitHttpUtil {
     public static String sendPost(SendPost sendPost) {
             String url ="";
                //定义发送数据
                JSONObject param = new JSONObject();
                param.put("ls_to",sendPost.getLs_to());
                param.put("ls_from",sendPost.getLs_from());
                param.put("ls_cc",sendPost.getLs_cc());
                param.put("ls_subject",sendPost.getLs_subject());
                param.put("ls_body",sendPost.getLs_body());
                //定义接收数据
                JSONObject result = new JSONObject();
                HttpPost httpPost = new HttpPost(url);
                CloseableHttpClient client = HttpClients.createDefault();
                //请求参数转JSON
                StringEntity entity = new StringEntity(JSONObject.toJSONString(param),"UTF-8");
             //   entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
                try {
                        HttpResponse response = client.execute(httpPost);
                        if (response.getStatusLine().getStatusCode()==200){
                                result = JSONObject.parseObject(EntityUtils.toString(response.getEntity(),"UTF-8"));
                        }
                        System.out.println(result);
                }catch (IOException e){
                        e.printStackTrace();
                        result.put("error","连接错误!");
                }
                return result.toString();
        }


}
