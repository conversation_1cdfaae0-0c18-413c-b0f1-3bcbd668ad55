package cc.mrbird.febs.clearLine.dao;

import cc.mrbird.febs.clearLine.entity.*;
import cc.mrbird.febs.clearLine.entity.pojo.CheckDtoByClearLine;
import cc.mrbird.febs.clearLine.entity.pojo.PicURL;
import cc.mrbird.febs.clearLine.entity.pojo.SelectAllDataPojo;
import cc.mrbird.febs.clearLine.entity.pojo.SelectCondition;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.poi.ss.formula.functions.T;
import org.mapstruct.Mapper;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
@Mapper
@DS("mssql")
@Transactional(propagation = Propagation.REQUIRES_NEW)
public interface ClearLineDataMapper extends BaseMapper<T> {

    List<String> selectProductionByAdd(TClearLineData clearLineData);
    List<String> selectProductionLineByAdd(QueryLine queryLine);

    List<String> selectProduction(TClearLineData clearLineData);
    List<String> selectProductionLine(QueryLine queryLine);

    boolean addSheet(TDataClearLineCheckItemByLine tDataClearLineCheckItemByLine);

    boolean addSheetByPointData(TDataClearLine tDataClearLine);

    List<SelectAllDataPojo> selectAllDataPojo(SelectCondition selectCondition);
    //查询图片
    List<PicURL> selectClearLineByPicURL(String mainInfoId);

    List<String> selectQC(String pl);
    List<String> selectQCByIDS3(String pl);

    List<String> selectSection(String pl);

    List<TDataClearLineCheckByData> selectCheckData(CheckDtoByClearLine checkDto);

    int   updateQhStatus(TDataClearLineUpdate tDataClearLineUpdate);

    String selectShowByName(String name);

    int updateAllDateByMastData(TDataClearLine tDataClearLine);
    int updateAllDateBySalveData(TDataClearLineCheckItemByLine tDataClearLineCheckItemByLine);

    void goBackByQC(String id,String showId,String backMark);

    String selectQh1(String id);


    String selectLineLeaderNumber(String name);

    String selectGroupLeaderNumber(String name);
    String selectQhNumber(String name);

    List<String> selectGroupLeader(String pl,String discern);

    List<String> selectQCByIDS1(String pl);
}
