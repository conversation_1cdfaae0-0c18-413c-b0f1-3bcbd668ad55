<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.equipmentSelfInspection.dao.EquipmentInspectionMaintenanceMapper">

    <select id="selectDiscern" resultType="java.lang.String">
        SELECT DISTINCT  section
        FROM
            [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
        WHERE SBU=#{SBU} AND functions=#{functions}
    </select>
    <select id="selectProduction" resultType="java.lang.String">
        SELECT DISTINCT  series
        FROM
            [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
        WHERE SBU=#{SBU} AND functions=#{functions} AND section=#{discern}
    </select>
    <select id="selectProductionLine" resultType="java.lang.String">
        SELECT DISTINCT  line
        FROM
            [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
        WHERE SBU=#{SBU} AND functions=#{functions} AND section=#{discern} AND series=#{production}
    </select>
    <select id="selectSelfInspectionType" resultType="java.lang.String">
        SELECT
            DISTINCT           [DJLX_Type]
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_DJXM]
    </select>
    <select id="selectInspectionMainInfo"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionMainInfoDto">
        SELECT
        a.[id]              as id,
        a.[SBU]             as pl,
        a.[functions]       as enginery,
        a.[section] AS discern,
        a.[series]          as production,
        a.[line]            as productionLine,
        a.[EquipmentID]   as equipmentId,
        a.[EquipmentName]   as equipmentName,
        b.[DJLX_Type]     as inspectionType,
        datediff(MINUTE,b.[DJ_StartDate],b.[DJ_EndDate]) as duration,
        b.[DJ_Date]     as date,
        b.[DJ_StartDate]     as startDate,
        b.[DJ_EndDate]     as endDate,
        b.[STATUS]   as status,
        b.[BZ_Notes]     as notes,
        b.[DJR_WorkId]  as djrWorkId,
        b.[DJR_Name]  as djrName,
        b.[QDR_WorkId]  as qdrWorkId,
        b.[QDR_Name]  as qdrName
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
        as a
        INNER JOIN [APPGCL_Process].[dbo].[EquipmentDJ_MainInfo]
        as b
        on a.EquipmentID = b.EquipmentID
        <where>
            <if test="functions != null and functions != ''">
                AND functions = #{functions}
            </if>
            <if test="discern != null and discern != ''">
                AND section = #{discern}
            </if>
            <if test="production != null and production != ''">
                AND series = #{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND line = #{productionLine}
            </if>
            <if test="selfInspectionType != null and selfInspectionType != ''">
                AND DJLX_Type=#{selfInspectionType}
            </if>
            <if test="date != null and date != ''">
                AND  DJ_Date = #{date}
            </if>
            <if test="startDate != null and startDate != ''">
                AND  DateDIFF(day,b.[DJ_Date],#{startDate}) &lt;=0
            </if>
            <if test="endDate != null and endDate != ''">
                AND  DateDIFF(day,b.[DJ_Date],#{endDate}) >=0
            </if>
        </where>
        ORDER BY DJ_Date DESC
    </select>
    <select id="queryIsNotDJ"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionMainInfoDto">
        SELECT [SBU]             as pl,
        [functions]       as enginery,
        [section] AS discern,
        [series]          as production,
        [line]            as productionLine
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_NotDJ]
        <where>
        SBU = 'IDS' AND functions = #{functions} AND section = #{discern} AND series = #{production}
        AND line = #{productionLine} AND DJLX_Type=#{selfInspectionType} AND  DJ_Date = #{date}
        </where>
    </select>

<!--    <select id="selectInspectionProjects"-->
<!--            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionProjectDto">-->
<!--        SELECT-->
<!--            [SBU]             as pl,-->
<!--            [functions]       as enginery,-->
<!--            [section] AS discern,-->
<!--            [series]          as production,-->
<!--            [line]            as productionLine-->
<!--        FROM [APPGCL_Process].[dbo].[EquipmentDJ_DJXM_Data]-->
<!--        where DJ_Date=#{date} AND DJLX_Type=#{selfInspectionType} AND EquipmentID=#{equipmentID}-->
<!--    </select>-->
    <select id="selectInspectionProjectData"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.EquipmentInspectionProjectData">
        SELECT
            [DJ_Date]             as djDate,
            [DJLX_Type]       as djType,
            [EquipmentID] AS equipmentID,
            [DJZ_Value]          as djValueList,
            [DJ_Result]            as djResultList,
            [DJ_Beizhu]            as djNotes
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_DJXM_Data]
        where DJ_Date=#{date} AND DJLX_Type=#{selfInspectionType} AND EquipmentID=#{equipmentID}
    </select>
    <select id="selectEquipmentInspectionProjectsByEquipmentIDAndType"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionProjectDto">
        SELECT a.[id]             as projectId,
               a.[EquipmentID]       as equipmentId,
               a.[DJLX_Type] as selfInspectionType,
               a.[DJZT_Status]          as status,
               a.[DJBW_Position]            as selfInspectionPosition,
               a.[JZ_Basis]            as selfInspectionStandard,
               b.[SBU]             as pl,
               b.[functions]       as enginery,
               b.[section] AS discern,
               b.[series]          as production,
               b.[line]            as productionLine
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_DJXM]
            as a
            LEFT JOIN [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
            as b
        on a.EquipmentID = b.EquipmentID
        where a.[EquipmentID]=#{equipmentID} AND [DJLX_Type]=#{djType}
    </select>
    <select id="selectSBU" resultType="java.lang.String">
        SELECT DISTINCT  SBU
        FROM
            [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
    </select>

    <select id="selectFunctions" resultType="java.lang.String">
        SELECT DISTINCT  functions
        FROM
        [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
        WHERE SBU=#{SBU}
    </select>

    <select id="selectRYXXByUserID"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionProjectDto">
        SELECT
        [SBU]             as pl,
        [functions]       as enginery,
        [section] AS discern
        FROM
        [APPGCL_Process].[dbo].[EquipmentDJ_RYXX]
        WHERE SJ_UserID=#{userID}
    </select>

    <select id="queryDiscernBySBU" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
            [section] AS discern
        FROM
            [APPGCL_Process].[dbo].[EquipmentDJ_SbuMapSection]
        WHERE SBU=#{SBU}
    </select>

</mapper>