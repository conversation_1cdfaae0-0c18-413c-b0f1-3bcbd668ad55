package cc.mrbird.febs.lessDaysheet.entity.dto;

import cc.mrbird.febs.lessDaysheet.entity.BadStatement;
import cc.mrbird.febs.lessDaysheet.entity.BadStatementUUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BadStatementDto {
    private BadStatement badStatementList;
    private List<BadStatementUUID> badStatementUUID;
}
