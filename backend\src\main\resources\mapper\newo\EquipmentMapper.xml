<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.newo.dao.EquipmentMapper">
    <resultMap id="SixTd" type="cc.mrbird.febs.newo.entity.SixTD">
        <result column="SCSL_QOP" jdbcType="DECIMAL" property="SCSL_QOP"/>
        <result column="CPLH_PFN" jdbcType="VARCHAR" property="CPLH_PFN"/>
        <result column="KHMC_CN" jdbcType="VARCHAR" property="KHMC_CN"/>
        <result column="KHLH_CMN" jdbcType="VARCHAR" property="KHLH_CMN"/>
        <result column="GLBH_WON" jdbcType="VARCHAR" property="GLBH_WON"/>
        <result column="RUKUDANHAO" jdbcType="VARCHAR" property="RUKUDANHAO"/>
        <result column="DATE_SCRQ"  jdbcType="VARCHAR" property="DATE_SCRQ"/>
    </resultMap>
    <insert id="insertWXBY" parameterType="cc.mrbird.febs.newo.entity.WXBY">
        USE Schedule
        INSERT INTO T_Equipment_Maintenance(Production,ProductionLine,RecordDate,ClassInfo,EquipmentName,EquipmentId
                                           ,SCLH,StartTime,EndTime,LossTime,MaintenanceNature,ChangeStation,FaultType,FaultCause,CounterPlan,STATUS,QH1,QH2,QH3,QH4)
        VALUES(#{Production},#{ProductionLine},#{RecordDate},#{ClassInfo},#{EquipmentName},#{EquipmentId},
               #{SCLH},#{StartTime},#{EndTime},#{LossTime},#{MaintenanceNature},#{ChangeStation},#{FaultType},#{FaultCause},
               #{CounterPlan},#{STATUS},#{QH1},#{QH2},#{QH3},#{QH4})
    </insert>
    <!--根据工号查询课别-->
    <select id="findDiscern" resultType="cc.mrbird.febs.newo.entity.PlDiscern">
        USE Schedule
        select DISTINCT Discern,PL FROM T_Configure_Equipment WHERE LineLeaderNumber = #{cardId}
    </select>
<!--    根据课别查询系列-->
    <select id="findSeries" resultType="cc.mrbird.febs.newo.entity.Equipment">
             USE Schedule
            select DISTINCT Production FROM T_Configure_Equipment WHERE Discern = #{Discern} AND PL=#{pl}
 </select>
    <!--根据系列查询线体-->
    <select id="findProductionLine" resultType="cc.mrbird.febs.newo.entity.Equipment">
                USE Schedule
                select DISTINCT ProductionLine FROM T_Configure_Equipment WHERE Production = #{Production}
 </select>
 <!--根据系列线体查询工令和客户料号-->
    <select id="findGL" resultType="cc.mrbird.febs.newo.entity.GL">
          USE Schedule
          SELECT Enginery,Discern,Production,Production_TD,
          ProductionLine,SectionManagerName,SectionManagerNumber,GroupLeaderName,GroupLeaderNumber,LineLeaderName,LineLeaderNumber,ClassInfo,line,SCSL_QOP,
          CPLH_PFN,KHLH_CMN,GLBH_WON,DC_SCDC,RUKUDANHAO,DATE_SCRQ,DATE_TDRQ,KHMC_CN
          FROM V_Date_Statement WHERE Production =#{Production} AND ProductionLine = #{ProductionLine}
 </select>
 <!--根据系列线体查询前天今天明天后天的工令数据-->
    <select id="findTDGL" resultMap="SixTd">
           USE  Android_App
           SELECT  series,line,CPLH_PFN,ProductClass,GLBH_WON,
           KHMC_CN,SCSL_QOP,RUKUDANHAO,DATE_SCRQ,STATUS,KHLH_CMN
           FROM Six_TD_DJXX
           WHERE  series =#{Production} AND line =#{ProductionLine} AND ProductClass=#{ClassInfo} AND DATE_SCRQ >= CONVERT (date,getdate()-2)  AND STATUS = 3
 </select>
    <select id="findGroup" resultType="java.lang.String">
           USE Schedule
        SELECT DISTINCT GroupLeaderName FROM T_Configure_Equipment WHERE  Discern =#{Discern} AND PL=#{pl}
 </select>
    <select id="findProduction" resultType="java.lang.String">
        use Schedule
        select distinct Production
        from T_Configure_Equipment
        where
          Discern=#{Discern}

    </select>
    <select id="finProductionLineByjj" resultType="java.lang.String">
        use Schedule
        select distinct ProductionLine
        from T_Configure_Equipment
        where
               PL=#{pl} AND Discern=#{discern} AND    Production=#{production}

    </select>
    <select id="findTDDJ" resultType="java.lang.String">
        USE  Android_App
        SELECT  CPLH_PFN,ProductClass,GLBH_WON,
                KHMC_CN,SCSL_QOP,RUKUDANHAO,DATE_SCRQ,KHLH_CMN,DC_SCDC
        FROM Six_TD_DJXX
        WHERE  series =#{Production} AND line =#{ProductionLine} AND ProductClass=#{ClassInfo} AND DATE_SCRQ >= CONVERT (date,getdate()-2)  AND STATUS = 3
    </select>
    <select id="findProdutLeader" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT ProductLeader FROM T_Configure_Equipment WHERE Discern = #{Discern} AND PL=#{pl}
    </select>
    <select id="selectByDC" resultType="cc.mrbird.febs.newo.entity.SelectByDC">
        USE Schedule
        SELECT
            A.plan_output AS planOutput,A.real_output AS realOutput,A.product_quantity AS productQuantity,
            A.customer_name AS customerName ,A.customer_quantity AS customerQuantity,A.ticket,
            A.dc ,A.inbound_order_number AS inboundOrderNumber,A.Person AS person ,A.DateTime AS dateTime,B.ClassInfo AS classInfo,
            B.TDW AS tdw,B.ProcessingTalk AS processingTalk,B.ConnectTalk AS connectTalk,B.OtherTalk AS otherTalk,
            B.Production,B.ProductionLine
        FROM [Schedule].[dbo].[T_Configure_ClassKPI_lock]  AS A   LEFT JOIN    configure_connect_lock AS B ON A.id =B.ClassKPI_Id
        <where>
            <if test="dc !=null and dc !=''">
                AND  A.dc=#{dc}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND B.ClassInfo = #{classInfo}
            </if>
            <if test="dateTime !=null and dateTime !=''">
                AND A.DateTime >=CONVERT(datetime,#{dateTime})   AND A.DateTime &lt; DATEADD(day,1,#{dateTime})
            </if>
        </where>
    </select>
    <!--查询开线数-->
    <select id="countOpenLineByOne" resultType="int">
    select count(distinct line) AS num from [Android_App].[dbo].[Six_TD_DJXX] where section like '%A5一課%' and DATE_SCRQ =CONVERT(date,GETDATE())
    </select>
    <select id="countOpenLineByTwo" resultType="int">
    select count(distinct line) AS num2 from [Android_App].[dbo].[Six_TD_DJXX] where section IN( 'A5二課',' A4二課') and DATE_SCRQ =CONVERT(date,GETDATE())
    </select>

    <select id="countProductionDayByOne" resultType="int">
        SELECT count (DISTINCT ProductionLine) as num  FROM [IDS_BK_PaperLessSystem].[dbo].[T_Configure_ProductionDay_lock] WHERE Industry ='PL1' AND Discern ='裝配一課' AND  DOM =CONVERT(date,getdate()-1)
    </select>

    <select id="countProductionDayByTwo" resultType="int">
        SELECT count (DISTINCT ProductionLine) as num  FROM [IDS_BK_PaperLessSystem].[dbo].[T_Configure_ProductionDay_lock] WHERE Industry ='PL1' AND Discern ='裝配二課' AND  DOM =CONVERT(date,getdate()-1)
    </select>

    <select id="lineWorkEditByOne" resultType="int">
    select count (distinct ProductionLine) AS num FROM  [Schedule].[dbo].[configure_connect_lock] where Industry ='PL1' AND DateTime >=convert(date,getdate()-1) AND Discern ='裝配一課'
    </select>

    <select id="lineWorkEditByTwo" resultType="int">
    select count (distinct ProductionLine) AS num FROM  [Schedule].[dbo].[configure_connect_lock] where Industry ='PL1' AND DateTime >=convert(date,getdate()-1) AND Discern ='裝配二課'
    </select>

    <select id="findTDDJ11" resultType="cc.mrbird.febs.newo.entity.DjXXVo">
        USE  Android_App
        SELECT  CPLH_PFN AS cplh,ProductClass AS productClass,GLBH_WON AS glbh,
        KHMC_CN AS khmcCn,SCSL_QOP AS scsl,RUKUDANHAO AS rkdh,DATE_SCRQ AS scrq,KHLH_CMN AS khmcCmn,DC_SCDC AS scdc
        FROM Six_TD_DJXX
        WHERE  series =#{production} AND line =#{productionLine} AND ProductClass=#{classInfo} AND DATE_SCRQ >= CONVERT (date,getdate()-2)  AND STATUS = 3
    </select>

    <select id="findDiscernByCardId" resultType="java.lang.String">
        USE Schedule select DISTINCT Discern FROM T_Configure_Equipment WHERE LineLeaderNumber = #{cardId};
    </select>

    <select id="selectQC" resultType="cc.mrbird.febs.newo.entity.PlDiscern">
        USE Schedule
        SELECT pl ,discern from T_Data_Group WHERE CardId=#{cardId}
    </select>

    <select id="selectMap" resultType="cc.mrbird.febs.newo.entity.PlDiscern">
        USE Schedule
        SELECT pl FROM t_configure_backwork_audit WHERE workId=#{cardId}
    </select>

    <select id="selectPerms" resultType="java.lang.String">
        IF EXISTS (
            SELECT 1
            FROM [IDS_BK_PaperLessSystem].[dbo].[t_config_dataPerms]
            WHERE workId=#{cardId}
        )
            SELECT perms
            FROM [IDS_BK_PaperLessSystem].[dbo].[t_config_dataPerms]
            WHERE workId=#{cardId}
        ELSE
            SELECT '0' AS perms
    </select>

    <select id="selectAllDataByPl" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT PL  FROM T_Configure_Equipment WHERE PL iS NOT NULL AND Discern !='' AND Discern IS NOT NULL AND Discern !='NULL' AND Discern !='A5包裝房'
        ORDER BY PL ASC
    </select>

    <select id="selectAllDataByDiscern" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT Discern  FROM T_Configure_Equipment WHERE PL iS NOT NULL AND Discern !='' AND Discern IS NOT NULL AND Discern !='NULL' AND Discern !='A5包裝房'
        ORDER BY Discern ASC
    </select>

    <select id="selectRoleByCardId" resultType="cc.mrbird.febs.newo.entity.DataRoleByIDS3">
        USE Schedule
        SELECT DISTINCT pl, RoleName AS role   FROM  T_Data_Group WHERE CardId=#{cardId}
    </select>

    <select id="findDiscern2" resultType="cc.mrbird.febs.newo.entity.PlDiscern">
        USE Schedule
        select DISTINCT Discern,PL FROM T_Configure_Equipment WHERE LineLeaderNumber = #{cardId}
    </select>
</mapper>
