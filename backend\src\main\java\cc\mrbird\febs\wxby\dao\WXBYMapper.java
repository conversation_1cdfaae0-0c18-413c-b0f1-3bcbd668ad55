package cc.mrbird.febs.wxby.dao;


//设备维修保养mapper
import cc.mrbird.febs.wxby.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2022/10/22
*/
@Mapper
@DS("mssql")
public interface WXBYMapper extends BaseMapper<MaintenanceLog> {

    /**
     * 明确系统数据来源于哪里，目前数据来源只能是从123服务器中拿
     * */
    /**   实现逻辑构思:
     *  ①
     * 对于设备:
     * 维修/保养/内容  =》  异常更换原因
     * 对于线长:
     * 填写异常设备信息
     * 对于生技维修组:
     * 处理异常设备并写明原因和改善策略   改善策略是否由改善组填写====?
     * 对于生技维修组长：
     * 目前所体现的是只有签名
     * /

   ===============================================================================
*/

    /**前端线长发起签核*/
    /**查插入之前先查询询*/
    @DS("mssql")
    void insertwxby(MaintenanceLog maintenanceLog);
    //查询维修保养的系列线体机构名称
    List<String> queryProductRange(String pl,String discern);
    List<String> queryProductionLine(String ProductRange,String discern);
    List<GroupEq> queryEquipment(String ProductRange, String ProductionLine);



    List<ErrorCause> queryCause(String EquipmentId);

    //为推送做服务: QH1为线长发起,QH2为生技代签,QH3为生技组长签核,QH4为生技科长签核,STATUS 表示整个签核流程已经完成
    //根据EquipmentId 获取生技组别,然后根据组别获取推送的生技人,此时时刻推送完就代表了审核通过,那就一直推送给下一个人,只有当STATUS==1时,签核流程完成

  //服务①
    //根据EquipmentId获取生技组别人员信息
    List queryUser(String EquipmentId);
    //服务②
    //根据生技组别获取生技叫修人员推送给生技组长和生技课长信息

    List<String> queryUserFirst(String pl,String discern);

    //获取设备的维修/保养数据  BY 生技組長
    List<MaintenanceLog> queryEquipmentList(TStatus tStatus);
    //BY 發起人
    List<MaintenanceLog> queryEquipmentListByFqr(TStatus tStatus);
    //BY 責任生技
    List<MaintenanceLog> queryEquipmentListByZRSJ(TStatus tStatus);
    //BY WXSH
    List<MaintenanceLog> queryEquipmentListByWXSH(TStatus tStatus);
    //BY WXHZ
    List<MaintenanceLog> queryEquipmentListByWXHZ(TStatus tStatus);
    List<MaintenanceLog> queryEquipmentListByWXHZANDIDS2(TStatus tStatus);
    List<MaintenanceLog> queryEquipmentListByPGQRR(TStatus tStatus);
    List<MaintenanceLog> queryEquipmentListByPGQRRAndIDS2(TStatus tStatus);

    List<MaintenanceLog> queryEquipmentListBySCZZ(TStatus tStatus);


    //模糊搜索

    List<String> queryCausation(String Causation);
    //根据异常原因查找解决对策
    List<String> queryCountermeasures(String Causation);
    List<String> queryUserByList(String pl,String discern);

    /***
     * 判断状态值
     *
     * */
    List<MaintenanceLog> queryQHList();
    //QH2签核,可以修改数据
    void updateMaintenance(MaintenanceLog maintenanceLog);
    //根据工号查询身份  1 2 3 4 5 6 7
    List<TGroupDate> queryRole(String CardId);
    //查询责任生技带班
    List<String> queryZRSJDB(String pl,String discern);
    //查询生技组长
    List<String> querySJZZ(String pl,String discern);
    List<String> querySJZZByUpdate(String pl,String discern);
    //生技带班推送接口
    void updataTS(MaintenanceLog maintenanceLog);
    //生技组长推送接口
    void updataTSBySJzz(MaintenanceLog maintenanceLog);
    //品管确认人
    void updataTSByPgqrr(MaintenanceLog maintenanceLog);
    //查询品管确认人
    List<String> queryPGQRR(String pl);
    //组长推送回对应发起人
    List<String> queryFQR();
    //qh2接口
    List<MaintenanceLog> selectByqh2(Integer Id);
    //根據系列線體查詢線長名稱
    List<String> selectBySCXZ(String Production,String ProductionLine,String EquipmentId);
    //生技組長推送給相對應的發起人
    void updateByLineLeader(MaintenanceLog maintenanceLog);
    //查詢生產組長
    List<String> selectBYGroupLeader(String Production,String ProductionLine);
    //生產線長推送給生產組長
    void updateByGroupLeader(MaintenanceLog maintenanceLog);

    //品保簽核
    void updateByQC(MaintenanceLog maintenanceLog);

   void insertMaintenanceLog(@Param("list") List<MaintenanceLog> maintenanceLog);
    //插入123叫修异常数据
   List<String>  countMaintenance();
   //获取当天维修保养数据
    List<MaintenanceLog> selectAllByGetDate();

    void TSByZs(MaintenanceLog maintenanceLog);

    List<MaintenanceLog> selectDataByQC();

    int countByDataPerms(String name);
    List<MaintenanceLog> selectDataByPermsVo(TStatus tStatus);
    //根據插入id來查找，如果為空就插入
    Integer  queryInsertId(Integer id);

 }
