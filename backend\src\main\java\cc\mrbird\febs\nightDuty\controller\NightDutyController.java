package cc.mrbird.febs.nightDuty.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.nightDuty.dao.NightDutyMapper;
import cc.mrbird.febs.nightDuty.entity.ExportPerson;
import cc.mrbird.febs.nightDuty.entity.ImportPerson;
import cc.mrbird.febs.nightDuty.entity.NightDutyPerson;
import cc.mrbird.febs.nightDuty.service.NightDutyService;
import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

@RestController
@RequestMapping("/nightDuty")
@Api(tags = "值夜接口")
@Slf4j
public class NightDutyController {

    @Autowired
    private NightDutyMapper nightDutyMapper;

    @Autowired
    private NightDutyService nightDutyService;

    @PostMapping("/importFile")
    @ApiOperation("excel导入人员信息")
    public FebsResponse importPerson(@RequestParam("file") MultipartFile file) throws IOException, InvalidFormatException {
        log.info("上传值夜人员信息：{}", file);
        List<ImportPerson> list = new ArrayList<>();
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


        Workbook xssfWorkbook  = WorkbookFactory.create(file.getInputStream());
        int SheetNum = xssfWorkbook.getNumberOfSheets();
        for (int i = 0; i < SheetNum; i++) {
            Sheet xssfSheet = xssfWorkbook.getSheetAt(i);
            int row = xssfSheet.getLastRowNum();
            for (int j = 1; j <= row; j++) {
                if(j>row){
                    break;
                }
                String SBU = String.valueOf(xssfSheet.getRow(j).getCell(0));
                String factory = String.valueOf(xssfSheet.getRow(j).getCell(1));
                String building = String.valueOf(xssfSheet.getRow(j).getCell(2));

                String date = null;
                if(xssfSheet.getRow(j).getCell(3).getCellType() == Cell.CELL_TYPE_NUMERIC) {
                    if(DateUtil.isCellDateFormatted(xssfSheet.getRow(j).getCell(3))) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                         date = sdf.format(xssfSheet.getRow(j).getCell(3).getDateCellValue());
                    }
                } else {
                    date = String.valueOf(xssfSheet.getRow(j).getCell(3));
                }

                String workId = String.valueOf(xssfSheet.getRow(j).getCell(4));
                String name =  String.valueOf(xssfSheet.getRow(j).getCell(5));
                String time = localDateTime.format(dateTimeFormatter1);
                String functions = String.valueOf(xssfSheet.getRow(j).getCell(6));
                String section =  String.valueOf(xssfSheet.getRow(j).getCell(7));

                ImportPerson importPerson = new ImportPerson(SBU, factory,building, date, workId, name, time, functions, section);

                    nightDutyMapper.insertChectPerson(importPerson);
                    list.add(importPerson);
                    int number1 = nightDutyMapper.queryAccount(importPerson);
                    if(number1 <1) {
                        //创建APP端的账号信息
                        nightDutyMapper.createAccount(importPerson);
                    }
                }
            }
        return new FebsResponse().code("200").message("文件导入成功");
    }

    @PostMapping("/deletePerson")
    @ApiOperation("批量删除人员信息")
    public FebsResponse deletePerson(@RequestParam("ids") List<Long> ids) {
        log.info("批量删除人员信息:{}",ids);
        nightDutyService.deletePerson(ids);
        return new FebsResponse().code("200").message("删除成功");
    }

    @PostMapping("/updatePerson")
    @ApiOperation("修改人员信息")
    public FebsResponse updatePerson(@RequestBody NightDutyPerson nightDutyPerson){
        log.info("修改人员信息:{}",nightDutyPerson);
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String time = localDateTime.format(dateTimeFormatter);
        nightDutyPerson.setTime(time);
        nightDutyMapper.updatePerson(nightDutyPerson);
        return new FebsResponse().code("200").message("修改人员信息成功");
    }

    @PostMapping("/selectPerson")
    @ApiOperation(value = "查询值夜人员信息",notes = "传厂区、巡视区域、年月")
    public FebsResponse selectPerson(@RequestBody NightDutyPerson nightDutyPerson){
        log.info("查询值夜人员信息：{}",nightDutyPerson);
        List<NightDutyPerson> list = nightDutyMapper.selectPerson(nightDutyPerson);
        for (int i = 0; i < list.size(); i++) {
            NightDutyPerson nightDutyPerson1 = list.get(i);
           int number1 = nightDutyMapper.selectStauts(nightDutyPerson1);
            if (number1 < 1){
                nightDutyPerson1.setStatus("0");
            } else {
                nightDutyPerson1.setStatus("1");
            }
            list.set(i,nightDutyPerson1);
        }
        return new FebsResponse().code("200").data(list);
    }

    @PostMapping("/insertPerson")
    @ApiOperation("新增值夜人员")
    public FebsResponse insertPerson(@RequestBody ImportPerson importPerson){
        log.info("新增值夜人员：{}",importPerson);
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        importPerson.setTime(localDateTime.format(dateTimeFormatter));
        nightDutyMapper.insertPerson(importPerson);
        int number1 = nightDutyMapper.queryAccount(importPerson);
        if(number1 < 1) {
            //创建APP端的账号信息
            String SBU = "IDS";
            importPerson.setSBU(SBU);
            nightDutyMapper.createAccount(importPerson);
        }
        return new FebsResponse().code("200").message("新增值夜人员成功");
    }

    @GetMapping("/queryFactory")
    @ApiOperation("查詢廠區")
    public FebsResponse queryFactory(){
        log.info("查詢廠區");
        List<String> list = nightDutyMapper.queryFactory();
        LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
        ArrayList<String > newlist = new ArrayList<>(hashSet);
        return new FebsResponse().code("200").data(newlist);
    }

    @GetMapping("/queryBuilding")
    @ApiOperation("查詢樓棟")
    public FebsResponse queryBuilding(){
        List<String> list = nightDutyMapper.queryBuilding();
        LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
        ArrayList<String > newlist = new ArrayList<>(hashSet);
        return new FebsResponse().code("200").data(newlist);
    }

    @GetMapping("/queryFunctions")
    @ApiOperation("查询机能")
    public FebsResponse queryFunctions(){
        List<String> list = nightDutyMapper.queryFunctions();
        LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
        ArrayList<String> newlist = new ArrayList<>(hashSet);
        return new FebsResponse().code("200").data(newlist);
    }

    @GetMapping("/querySection")
    @ApiOperation("查询课别")
    public FebsResponse querySection(){
        List<String> list = nightDutyMapper.querySection();
        LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
        ArrayList<String> newlist = new ArrayList<>(hashSet);
        return new FebsResponse().code("200").data(newlist);
    }

/*    @GetMapping("queryInfo")
    @ApiOperation("通过id查询信息")
    public FebsResponse queryInfo(@RequestParam("id") long id){
        log.info("通过id查询信息:{}",id);
        NightDutyPersonVO nightDutyPersonVO = new NightDutyPersonVO();
        nightDutyPersonVO = nightDutyMapper.queryInfo(id);
        return new FebsResponse().code("200").data(nightDutyPersonVO);
    }*/

    @PostMapping("/exportTemplate")
    @ApiOperation("模版導出")
    @CrossOrigin(origins = "*")
    public FebsResponse exportTemplate(HttpServletResponse response){
        String filePath ="D:\\Fit\\muban\\123123.xls";
        try {
            URLDecoder.decode(filePath, "UTF-8");
            File file = new File(filePath);
            String fileName = file.getName();
            String ext = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            FileInputStream fis = new FileInputStream(file);
            InputStream is = new BufferedInputStream(fis);
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            is.close();
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode(fileName, "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            response.addHeader("content-Length", "" + file.length());
            response.setHeader("Access-Control-Allow-Origin", "*");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return  new FebsResponse().code("200").message("下载成功");
    }

    @PostMapping("/exportPerson")
    @ApiOperation("值夜人员导出")
    public FebsResponse exportPerson(HttpServletResponse response) throws Exception{
        String name = "月值夜人员表";
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String time = localDateTime.format(dateTimeFormatter);
        List<ExportPerson> list = nightDutyMapper.exportPerson(time);
        if(list==null)
            return new FebsResponse().code("500").message("导出失败请检查sql语句");
        String filename= time + name;
        ServletOutputStream servletOutputStream = servletOutputStream(filename, response);
        EasyExcel.write(servletOutputStream,ExportPerson.class).sheet("值夜人员表").doWrite(list);
        servletOutputStream.flush();
        return  new FebsResponse().code("200").message("下载成功");
    }


    @PostMapping("/exportDutyPersonTest")
    @ApiOperation("人员导出测试")
    public FebsResponse exportDutyPersonTest(HttpServletResponse response,String date) throws Exception {

        List<ExportPerson> list = nightDutyMapper.exportPerson(date);
        if(list==null)
            return new FebsResponse().code("500").message("导出失败请联系管理员");
        String filename= date;
        ServletOutputStream servletOutputStream = servletOutputStream(filename, response);
        EasyExcel.write(servletOutputStream,ExportPerson.class).sheet("值夜人员表").doWrite(list);
        servletOutputStream.flush();
       return  new FebsResponse().code("200").message("下载成功");
    }

    public static ServletOutputStream servletOutputStream(String filename, HttpServletResponse response) throws Exception {
        try {
            filename = URLEncoder.encode(filename, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment; filename=" + filename + ".xls");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");
            return response.getOutputStream();
        } catch (IOException e) {
            throw new Exception("导出excel表格失败!", e);
        }
    }
}
