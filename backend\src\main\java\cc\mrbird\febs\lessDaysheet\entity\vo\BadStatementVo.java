package cc.mrbird.febs.lessDaysheet.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BadStatementVo {
        private String id;
        private String discern;
        private String classInfo;
        private String checkStatus;
        private String production;
        private String productionLine;
        private String workStation;
        private String partNumber;
        private String recordDate;
        private String badItem;
        private String mo;
        private String remark;
        private String inputNumber1;
        private String inputNumber2;
        private String inputNumber3;
        private String inputNumber4;
        private String inputNumber5;
        private String lossRate1;
        private String lossRate2;
        private String lossRate3;
        private String lossRate4;
        private String lossRate5;
        private String yield1;
        private String yield2;
        private String yield3;
        private String yield4;
        private String yield5;
        private String productionLineCheckName;
        private String qcName;
        private String badNum1;
        private String badNum2;
        private String badNum3;
        private String badNum4;
        private String badNum5;
}
