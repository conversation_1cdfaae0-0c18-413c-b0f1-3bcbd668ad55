package cc.mrbird.febs.common.aspect;

import cc.mrbird.febs.common.annotation.RdProjectLog;
import cc.mrbird.febs.common.utils.FebsUtil;
import cc.mrbird.febs.rdProject.dao.RdProjectUserExtraMapper;
import cc.mrbird.febs.rdProject.entity.RdProjectHistory;
import cc.mrbird.febs.rdProject.entity.RdProjectUserExtra;
import cc.mrbird.febs.rdProject.service.RdProjectHistoryService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.concurrent.CompletableFuture;


/**
 * @Author: 李囯斌
 * @Date: 2025/7/21
 * @Time: 13:15
 */
@Aspect
@Component
public class RdProjectLogAspect {
    // 依赖注入Service（用于保存日志）
    @Autowired
    private RdProjectHistoryService rdProjectHistoryService;
    @Autowired
    private RdProjectUserExtraMapper rdProjectUserExtraMapper;

    // 获取当前登录用户
    private RdProjectUserExtra getCurrentUser() {
        String workerId = FebsUtil.getCurrentUser().getUsername();
        if(!StringUtils.isEmpty(workerId)){
            RdProjectUserExtra userExtra = rdProjectUserExtraMapper.selectRdProjectUserExtraByWorkerId(workerId);
            return userExtra;
        }
        return null;
    }

    @Pointcut("@annotation(cc.mrbird.febs.common.annotation.RdProjectLog)")
    public void operationLogPointcut() {}

    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RdProjectLog logAnnotation = method.getAnnotation(RdProjectLog.class);

        // 1. 收集日志信息
        RdProjectHistory log = new RdProjectHistory();
        log.setContent(logAnnotation.value());
        RdProjectUserExtra currentUser = getCurrentUser();
        if(currentUser==null)throw new NullPointerException("暫無該用戶信息！");
        log.setUserName(currentUser.getUserName());
        log.setTime(new Date());
        log.setWorkerId(currentUser.getWorkerId());
        log.setSiteArea(currentUser.getSiteArea());
        log.setSbu(currentUser.getSbu());
        log.setFunctions(currentUser.getFunctions());
        log.setSection(currentUser.getSection());


        // 2. 执行目标方法
        Object result;
        try {
            result = joinPoint.proceed();
            // 可添加操作状态字段标记成功
        } catch (Exception e) {
            // 记录异常信息（可选）
            log.setContent(log.getContent()+"操作失败: " + e.getMessage());
            throw e;
        }

        // 3. 异步保存日志（不阻塞主流程）
        CompletableFuture.runAsync(() -> rdProjectHistoryService.saveLog(log));

        return result;
    }
}
