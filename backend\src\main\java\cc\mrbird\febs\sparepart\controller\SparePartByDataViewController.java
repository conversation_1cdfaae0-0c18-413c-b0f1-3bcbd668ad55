package cc.mrbird.febs.sparepart.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.sparepart.dao.SparePartByDataMapper;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureYieldOperation;
import cc.mrbird.febs.sparepart.entity.qo.SparePartAndEquipmentQo;
import cc.mrbird.febs.sparepart.entity.qo.UpdateSparePartQo;
import cc.mrbird.febs.sparepart.service.IEquipmentDataService;
import cc.mrbird.febs.wxby.dao.SelectMapper;
import cc.mrbird.febs.wxby.dao.WXBYMapper;
import cc.mrbird.febs.wxby.entity.MaintenanceLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@Api(tags = "備品管控-寿命显示、编辑")
@ResponseBody
@Slf4j
@RequestMapping("/SparePartByDataView")
public class SparePartByDataViewController {
    private final SparePartByDataMapper sparePartByDataMapper;
    private final IEquipmentDataService iEquipmentDataService;
    private final SelectMapper selectMapper;
    private final WXBYMapper wxbyMapper;
    public SparePartByDataViewController(SparePartByDataMapper sparePartByDataMapper, IEquipmentDataService iEquipmentDataService, SelectMapper selectMapper, WXBYMapper wxbyMapper) {
        this.sparePartByDataMapper = sparePartByDataMapper;
        this.iEquipmentDataService = iEquipmentDataService;
        this.selectMapper = selectMapper;
        this.wxbyMapper = wxbyMapper;
    }


    //獲取IDS1
    @PostMapping("/queryPl")
    @ApiOperation(value = "獲取pl 例: IDS1 IDS2 IDS3")
    public FebsResponse queryPl() {
        List<String> list = sparePartByDataMapper.queryPl();
        return new FebsResponse().code("200").data(list);
    }

    //根據 IDS 獲取課別
    @GetMapping("/queryDiscern")
    @ApiOperation(value = "根據IDS 獲取課別  ",notes = "例： 裝配一課 裝配二課")
    public FebsResponse queryDiscern(@RequestParam("pl") String pl) {
        List<String> list = sparePartByDataMapper.queryDiscern(pl);
        return new FebsResponse().code("200").data(list);
    }

    //根據 IDS 課別 獲取 系列
    @PostMapping("/queryProduction")
    @ApiOperation(value = "根据事業處、课别获取系列",notes = "參數只需要傳 pl、 discern")
    public FebsResponse queryProduction(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentQo){
        List<String> list =sparePartByDataMapper.queryProductionByEdit(sparePartAndEquipmentQo);
        return  new FebsResponse().code("200").data(list);
    }

    //根據 IDS 課別 系列 獲取 線體
    @PostMapping("/queryProductionLine")    @ApiOperation(value = "根據 pl 課別、系列獲取線體",notes = "參數傳：pl,discern,production")
    public FebsResponse queryProductionLine(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentVo) {
        List<String> list = sparePartByDataMapper.queryProductionLineByEdit(sparePartAndEquipmentVo);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation(value = "獲取備品數據展示")
    @PostMapping("/querySparePartDataByView")
    public FebsResponse querySparePartDataByView(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentQo){
        List<TConfigureEquipmentAndYield> list =iEquipmentDataService.querySparePartDataByView(sparePartAndEquipmentQo);
        return  new FebsResponse().code("200").data(list);
    }


    @ApiOperation(value = "更換備件")
    @PostMapping("/updateBySparePart")
    public FebsResponse updateBySparePart(@RequestBody UpdateSparePartQo updateSparePartQo){
            int c =iEquipmentDataService.updateSparePart(updateSparePartQo);
           // TConfigureEquipmentAndYield tc=iEquipmentDataService.selectTConfigureEquipmentAndYield(updateSparePartQo.getId());
            //MaintenanceLog log=setMaintenanceLogDto(tc,updateSparePartQo);
           // wxbyMapper.insertwxby(log);
            if (c==2){
                return new FebsResponse().code("200").message("修改成功");
            }else {
                return new FebsResponse().code("500").message("修改失敗,傳的參數為:"+updateSparePartQo);
            }
    }

    @ApiOperation(value = "查询修改记录")
    @GetMapping("/queryYieldOperation")
    public  FebsResponse queryYieldOperation(@RequestParam("id") Integer id ){
        List<TConfigureYieldOperation> list = iEquipmentDataService.queryYieldOperation(id);
        return  new FebsResponse().code("200").data(list);

    }

    //构造设备维修记录表DTO类方法
    private MaintenanceLog setMaintenanceLogDto(TConfigureEquipmentAndYield tc,UpdateSparePartQo uq){
        DateTimeFormatter dateTimeFormat =DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        MaintenanceLog ml = new MaintenanceLog();
        ml.setPl(tc.getPl());
        ml.setDiscern(tc.getDiscern());
        ml.setProduction(tc.getProduction());
        ml.setProductionLine(tc.getProductionLine());
        LocalDateTime dateTime= LocalDateTime.now();
        String recordTime= dateTime.format(dateTimeFormat);
        ml.setRecordDate(recordTime);
        ml.setClassInfo("白班");
        ml.setEquipmentName(tc.getEquipmentName());
        LocalDateTime startTime=LocalDateTime.now();
        String start =startTime.format(dateTimeFormatter);
        ml.setStartTime(start);
        ml.setEndTime(tc.getEquipmentUpdateTime());
        ml.setMaintenanceNature("故障維修");
        ml.setChangeStation("更換工件");
        ml.setFaultType(uq.getOperationalReason());
        ml.setFaultCause(uq.getOperationalReason());
        ml.setCounterPlan(uq.getOperationType());
        ml.setStatus("false");
        ml.setQh1("true");
        ml.setFqr(uq.getOperationUser());
        ml.setZrsj(uq.getOperationUser());
        ml.setQh2("true");
        //查找Leader
        String wxsh=sparePartByDataMapper.queryProductionLeader(tc.getProduction(),tc.getProductionLine());
        ml.setWxsh(wxsh);
        ml.setDataType("備品維護");
        return ml;
    }




}