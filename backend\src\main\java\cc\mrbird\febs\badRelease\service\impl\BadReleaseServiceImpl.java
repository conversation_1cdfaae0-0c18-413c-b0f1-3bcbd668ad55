package cc.mrbird.febs.badRelease.service.impl;

import cc.mrbird.febs.badRelease.dao.BadReleaseMapper;
import cc.mrbird.febs.badRelease.entity.dto.AdditionalInfoDto;
import cc.mrbird.febs.badRelease.entity.dto.BadReleaseDetailDto;
import cc.mrbird.febs.badRelease.entity.qo.ConditionUseingQueryQo;
import cc.mrbird.febs.badRelease.entity.vo.BadReleaseDetailVo;
import cc.mrbird.febs.badRelease.entity.vo.BadReleaseInfoVo;
import cc.mrbird.febs.badRelease.service.BadReleaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class BadReleaseServiceImpl implements BadReleaseService {

    @Autowired
    private BadReleaseMapper badReleaseMapper;


    /**
     * 通過工號查詢產品處
     * @param workId
     * @return
     */
    public String querySBU(String workId) {

        if(workId.isEmpty()){
            return "工號不能為空";
        }

        String SBU = badReleaseMapper.querySBU(workId);
        if(SBU.equals("IDS")){
            SBU = "IDS3";
        }
        return SBU;
    }


    /**
     * 通過工號查詢機能
     * @param workId
     * @return
     */
    public String queryFunction(String workId) {

        if(workId.isEmpty()){
            return "工號不能為空";
        }

        String section = badReleaseMapper.queryFunction(workId);
        return section;
    }

    /**
     * 查詢課別
     * @return
     */
    public List<String> querySection() {
        List<String> list =  badReleaseMapper.querySecntion();
        LinkedHashSet<String> list1 = new LinkedHashSet<>(list);
        ArrayList<String> section = new ArrayList<>(list1);
        return section;
    }

    /**
     * 查詢線體
     * @return
     */
    public List<String> queryLine() {
        List<String> list =  badReleaseMapper.queryLine();
        LinkedHashSet<String> list1 = new LinkedHashSet<>(list);
        ArrayList<String> lines = new ArrayList<>(list1);
        return lines;
    }

    /**
     * 查詢料號
     * @return
     */
    public List<String> queryMaterialNumber() {
        List<String> list =  badReleaseMapper.queryMaterialNumber();
        LinkedHashSet<String> list1 = new LinkedHashSet<>(list);
        ArrayList<String> materialNumbers = new ArrayList<>(list1);
        return materialNumbers;
    }

    /**
     * 查詢不良釋放日期
     * @return
     */
    public List<String> queryBadReleaseDate() {
        List<String> list =  badReleaseMapper.queryBadReleaseDate();
        LinkedHashSet<String> list1 = new LinkedHashSet<>(list);
        ArrayList<String> badReleaseDate = new ArrayList<>(list1);
        return badReleaseDate;
    }

    /**
     * 條件查詢
     * 條件為：課別、線體、料號、不良釋放日期
     * @param conditionUseingQuery
     * @return
     */
    public List<BadReleaseInfoVo> queryCondition(ConditionUseingQueryQo conditionUseingQuery) {
        List<BadReleaseInfoVo> badReleaseInfos = badReleaseMapper.queryCondition(conditionUseingQuery);
        return badReleaseInfos;
    }

    /**
     * 詳情展示
     * id查詢
     * @param id
     * @return
     */
    public BadReleaseDetailVo queryById(Integer id) {

        BadReleaseDetailVo badReleaseDetailVos = new BadReleaseDetailVo();
        List<AdditionalInfoDto> finResult = new ArrayList<>();
        BadReleaseDetailDto badReleaseDetailDto = badReleaseMapper.queryById(id);

        List<Integer> result = new ArrayList<>();
        //尋找字符串中包含NG的部分
        String input = badReleaseDetailDto.getResult();
        input = input.substring(1,input.length()-1);
        String pattern = ",\"([0-9]+)\":\"NG\"";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(input);
        List<String> ngNumbers = new ArrayList<>();
        while(m.find()){
            ngNumbers.add(m.group(1));
        }
        for(String number : ngNumbers){
            result.add(Integer.parseInt(number));
        }

        String materialNumber = badReleaseDetailDto.getMaterialNumber();
        List<AdditionalInfoDto> additionalInfoDtos = badReleaseMapper.queryByMaterialNumber(materialNumber);
        for (int i = 0; i < additionalInfoDtos.size(); i++) {
            AdditionalInfoDto additionalInfoDto = additionalInfoDtos.get(i);
            additionalInfoDto.setTestResult("OK");

            for (int j = 0; j < result.size(); j++) {
                if(additionalInfoDto.getId().equals(result.get(j))){
                    additionalInfoDto.setTestResult("NG");
                }
            }

            finResult.add(additionalInfoDto);
        }

        badReleaseDetailVos.setSBU(badReleaseDetailDto.getSBU());
        badReleaseDetailVos.setFunctions(badReleaseDetailDto.getFunctions());
        badReleaseDetailVos.setSection(badReleaseDetailDto.getSection());
        badReleaseDetailVos.setLine(badReleaseDetailDto.getLine());
        badReleaseDetailVos.setMaterialNumber(badReleaseDetailDto.getMaterialNumber());
        badReleaseDetailVos.setBadReleaseDate(badReleaseDetailDto.getBadReleaseDate());
        badReleaseDetailVos.setName(badReleaseDetailDto.getName());
        badReleaseDetailVos.setNgStatus(badReleaseDetailDto.getNgStatus());
        badReleaseDetailVos.setNgCause(badReleaseDetailDto.getNgCause());
        badReleaseDetailVos.setAIPicture(badReleaseDetailDto.getAIPicture());
        badReleaseDetailVos.setCCDPicture(badReleaseDetailDto.getCCDPicture());
        badReleaseDetailVos.setStatus(badReleaseDetailDto.getStatus());
        badReleaseDetailVos.setResult(finResult);

        return badReleaseDetailVos;
    }
}
