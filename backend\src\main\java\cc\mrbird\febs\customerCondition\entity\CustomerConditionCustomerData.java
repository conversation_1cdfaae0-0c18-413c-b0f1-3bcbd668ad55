package cc.mrbird.febs.customerCondition.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶信息實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionCustomerData {

    @ApiModelProperty(value = "客戶ID", position = 0)
    private Integer customerId;

    @ApiModelProperty(value = "客戶名字", position = 1)
    private String customerName;
}
