package cc.mrbird.febs.rdProject.dao;

import cc.mrbird.febs.rdProject.entity.RdProjectHistory;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/7/21
 * @Time: 13:13
 */
@DS("primary")
public interface RdProjectHistoryMapper {

    void saveHistory(RdProjectHistory rdProjectHistory);

    List<RdProjectHistory> selectHistory(@Param("condition") RdProjectHistory rdProjectHistory);
}
