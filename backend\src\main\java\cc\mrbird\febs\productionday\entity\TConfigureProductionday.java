package cc.mrbird.febs.productionday.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("T_Configure_ProductionDay")
public class TConfigureProductionday implements Serializable {

    private static final long serialVersionUID = 1L;

        @TableField("Id")
        @ExcelIgnore
    private Long Id;

    /**
     * 事业处
     */
    @ExcelProperty(value = "事业处",index = 0)
        @TableField("Industry")
    private String industry;

    /**
     * 机能
     */
    @ExcelProperty(value = "机能",index = 1)
        @TableField("Enginery")
    private String enginery;

    /**
     * 课别
     */
    @ExcelProperty(value = "课别",index = 2)
        @TableField("Discern")
    private String discern;

    /**
     * 系列
     */
    @ExcelProperty(value = "系列",index = 3)
        @TableField("Production")
    private String production;

    /**
     * 线体
     */
    @ExcelProperty(value = "线体",index = 4)
        @TableField("ProductionLine")
    private String productionLine;

    /**
     * 班别
     */
    @ExcelProperty(value = "班别",index = 5)
        @TableField("ClassInfo")
    private String classInfo;

    /**
     * 产品组长
     */
    @ExcelProperty(value = "产品组长",index = 6)
        @TableField("PTL")
    private String ptl;

    /**
     * 轮班组长
     */
    @ExcelProperty(value = "轮班组长",index = 7)
        @TableField("ShiftLeader")
    private String shiftLeader;

    /**
     * 人员班别
     */
    @ExcelProperty(value = "人员班别",index = 8)
        @TableField("PersonnelShift")
    private String personnelShift;

    /**
     * 生产日期
     */
    @ExcelProperty(value = "生产日期",index = 9)
        @TableField("DOM")
    private String dom;

    /**
     * 工令
     */
    @ExcelProperty(value = "工令",index = 10)
        @TableField("WorkOrder")
    private String workOrder;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称",index = 11)
        @TableField("CustomerName")
    private String customerName;

    /**
     * 料号
     */
    @ExcelProperty(value = "料号",index = 12)
        @TableField("PartNumber")
    private String partNumber;

    /**
     * 产品特性
     */
    @ExcelProperty(value = "产品特性",index = 13)
        @TableField("ProductTX")
    private String productTX;

    @ExcelProperty(value = "ScheduleDT",index = 14)
        @TableField("ScheduleDT")
    private String scheduleDT;

    /**
     * 总投入时间
     */
    @ExcelProperty(value = "总投入时间",index = 15)
        @TableField("TotalTime")
    private String totalTime;

    /**
     * 总负荷时间     总负荷时间  =  计划停机时间-总投入时间
     */
    @ExcelProperty(value = "总负荷时间",index = 16)
        @TableField("TotalLoadTime")
    private String totalLoadTime;

    /**
     * 当机损失时间
     */
    @ExcelProperty(value = "当机损失时间",index = 17)
        @TableField("DowntimeLostTime")
    private String downtimeLostTime;

    /**
     * 标配人力
     */
    @ExcelProperty(value = "标配人力",index = 18)
        @TableField("StandardManpower")
    private String standardManpower;

    /**
     * 实际人力
     */
    @ExcelProperty(value = "实际人力",index = 19)
        @TableField("ActualManpower")
    private String actualManpower;

    /**
     * 目标报废率
     */
    @ExcelProperty(value = "目标报废率",index = 20)
        @TableField("TSR")
    private String tsr;

    /**
     * 实际报废率
     */
    @ExcelProperty(value = "实际报废率",index = 21)
        @TableField("ACR")
    private String acr;

    /**
     * CT
     */
    @ExcelProperty(value = "CT",index = 22)
        @TableField("CT")
    private String ct;

    /**
     * 理论产量     理论产量=(总负荷时间-损失时间)*60/CT/1000
     */
    @ExcelProperty(value = "理论产量",index = 23)
        @TableField("TOutput")
    private String tOutput;

    /**
     * 计划量
     */
    @ExcelProperty(value = "计划量",index = 24)
        @TableField("PQ")
    private BigDecimal pq;

    /**
     * 实际量
     */
    @ExcelProperty(value = "实际量",index = 25)
        @TableField("AQ")
    private BigDecimal aq;

    /**
     * 入库量
     */
    @ExcelProperty(value = "入库量",index = 26)
        @TableField("WV")
    private String wv;

    /**
     * 差异值
     */
    @ExcelProperty(value = "差异值",index = 27)
        @TableField("DV")
    private BigDecimal dv;

    /**
     * 达成状况
     */
    @ExcelProperty(value = "达成状况",index = 28)
        @TableField("AchievementStatus")
    private String achievementStatus;

    /**
     * 欠产原因
     */
    @ExcelProperty(value = "欠产原因",index = 29)
        @TableField("RFLF")
    private String rflf;

    /**
     * 改善策略
     */
    @ExcelProperty(value = "改善策略",index = 30)
        @TableField("IPST")
    private String ipst;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注",index = 31)
        @TableField("Remarks")
    private String remarks;

    /**
     * 时间稼动率
     */
    @ExcelProperty(value = "时间稼动率",index = 32)
        @TableField("TimeRate")
    private String timeRate;

    /**
     * 性能稼动率
     */
    @ExcelProperty(value = "性能稼动率",index = 33)
        @TableField("PerformanceRate")
    private String performanceRate;

    /**
     * 良品率
     */
    @ExcelProperty(value = "良品率",index = 34)
        @TableField("YieldRate")
    private String yieldRate;

    @ExcelProperty(value = "OEE1",index = 35)
        @TableField("OEE1")
    private String oee1;

    @ExcelProperty(value = "OEE",index = 36)
        @TableField("OEE")
    private String oee;

    @ExcelProperty(value = "OPE1",index = 37)
        @TableField("OPE1")
    private String ope1;

    @ExcelProperty(value = "UPH",index = 38)
        @TableField("UPH")
    private String uph;

    @ExcelProperty(value = "UPPH",index = 39)
        @TableField("UPPH")
    private String upph;

    @ExcelProperty(value = "FillingDate",index = 40)
        @TableField("FillingDate") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date fillingDate;

    @ExcelProperty(value = "cardId",index =41)
        @TableField("cardId")
        private String cardId;

    @ExcelProperty(value = "Scsl",index = 42)
    @TableField("Scsl")
    private String scsl;
    /***
     *实际产量
     */
    @TableField("ListAq")
    private String ListAq;

}
