<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.sparepart.dao.SparePartByDataMapper">
    <select id="queryBySharePartAndEquipment"
            resultType="cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndSparePart">
        SELECT id
             , pl
             , enginery
             , discern
            ,production
             , productionLine
             , equipmentName
             , equipmentId
             , sparePartName
             ,lifeLimit
             , sparePartId
        FROM [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndSparePart]
        <where>
            <if test="pl != null and pl != ''">
                AND pl = #{pl}
            </if>
            <if test="discern != null and discern != ''">
                AND discern = #{discern}
            </if>
            <if test="production != null and production != ''">
                AND production=#{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND productionLine = #{productionLine}
            </if>
            AND isDelete = '0'
        </where>
    </select>

    <select id="queryByPersonAndEquipment"
            resultType="cc.mrbird.febs.sparepart.entity.dto.TConfigurePersonAndEquipment">
        SELECT id
             , pl
             , enginery
             , discern
             , sparePartName
             , sparePartCode
             , personInChargeNameBy01
             , personInChargeWorkIdBy01
             , personInChargeEmailBy01
             , personInChargeNameBy02
             , personInChargeWorkIdBy02
             , personInChargeEmailBy02
             , personInChargeNameByMax
             , personInChargeWorkIdByMax
             , personInChargeEmailByMax
        FROM [IDS_BK_PL1_02].[dbo].[t_configure_personAndEquipment]
        <where>
            <if test="pl != null and pl != ''">
                AND pl = #{pl}
            </if>
            <if test="discern != null and discern != ''">
                AND discern = #{discern}
            </if>
            <if test="sparePartCode != null and sparePartCode != ''">
                AND sparePartCode = #{sparePartCode}
            </if>
            AND isDelete = '0'
        </where>
    </select>

    <select id="queryPl" resultType="java.lang.String">
        SELECT DISTINCT pl
        FROM t_configure_equipmentAndYield
    </select>

    <select id="queryDiscern" resultType="java.lang.String">
        SELECT DISTINCT discern
        FROM t_configure_equipmentAndYield
    </select>

    <select id="queryProductionLine" resultType="java.lang.String">
        SELECT DISTINCT productionLine
        FROM t_configure_equipmentAndSparePart
        WHERE pl = #{pl}
          AND discern = #{discern} AND production=#{production} AND isDelete='0'
    </select>

    <select id="querySparePArtCode" resultType="java.lang.String">
        SELECT DISTINCT sparePartCode
        FROM t_configure_personAndEquipment
        WHERE pl = #{pl}
          AND discern = #{discern} AND isDelete='0'
    </select>

    <update id="upDataBySparePartAndEquipment">
        UPDATE t_configure_equipmentAndSparePart
        set pl            =CASE WHEN #{pl} IS NOT NULL then #{pl} else pl END,
            enginery      =CASE WHEN #{enginery} IS NOT NULL then #{enginery} else enginery END,
            discern=CASE WHEN #{discern} IS NOT NULL then #{discern} else discern END,
            productionLine=CASE WHEN #{productionLine} IS NOT NULL then #{productionLine} else productionLine END,
            equipmentName=CASE WHEN #{equipmentName} IS NOT NULL then #{equipmentName} else equipmentName END,
            equipmentId   =CASE WHEN #{equipmentId} IS NOT NULL then #{equipmentId} else equipmentId END,
            sparePartName=CASE WHEN #{sparePartName} IS NOT NULL then #{sparePartName} else sparePartName END,
            lifeLimit=CASE WHEN #{lifeLimit} IS NOT NULL then #{lifeLimit} else lifeLimit END,
            sparePartId=CASE WHEN #{sparePartId} IS NOT NULL then #{sparePartId} else sparePartId END
        WHERE id = #{id}
    </update>

    <update id="upDataByPersonAndEquipment">
        UPDATE t_configure_personAndEquipment
        set pl                       =CASE WHEN #{pl} IS NOT NULL then #{pl} else pl END,
            enginery                 =CASE WHEN #{enginery} IS NOT NULL then #{enginery} else enginery END,
            discern=CASE WHEN #{discern} IS NOT NULL then #{discern} else discern END,
            sparePartName=CASE WHEN #{sparePartName} IS NOT NULL then #{sparePartName} else sparePartName END,
            sparePartCode=CASE WHEN #{sparePartCode} IS NOT NULL then #{sparePartCode} else sparePartCode END,
            personInChargeNameBy01=CASE
                                       WHEN #{personInChargeNameBy01} IS NOT NULL then #{personInChargeNameBy01}
                                       else personInChargeNameBy01 END,
            personInChargeWorkIdBy01=CASE
                                         WHEN #{personInChargeWorkIdBy01} IS NOT NULL then #{personInChargeWorkIdBy01}
                                         else personInChargeWorkIdBy01 END,
            personInChargeEmailBy01  =CASE
                                          WHEN #{personInChargeEmailBy01} IS NOT NULL then #{personInChargeEmailBy01}
                                          else personInChargeEmailBy01 END,
            personInChargeNameBy02=CASE
                                       WHEN #{personInChargeNameBy02} IS NOT NULL then #{personInChargeNameBy02}
                                       else personInChargeNameBy02 END,
        personInChargeWorkIdBy02=CASE
        WHEN #{personInChargeWorkIdBy02} IS NOT NULL then #{personInChargeWorkIdBy02}
        else personInChargeWorkIdBy01 END,
            personInChargeEmailBy02=CASE
                                        WHEN #{personInChargeEmailBy02} IS NOT NULL then #{personInChargeEmailBy02}
                                        else personInChargeEmailBy02 END,
            personInChargeNameByMax  =CASE
                                          WHEN #{personInChargeNameByMax} IS NOT NULL then #{personInChargeNameByMax}
                                          else personInChargeNameByMax END,
            personInChargeWorkIdByMax=CASE
                                          WHEN #{personInChargeWorkIdByMax} IS NOT NULL
                                              then #{personInChargeWorkIdByMax}
                                          else personInChargeWorkIdByMax END,
            personInChargeEmailByMax=CASE
                                         WHEN #{personInChargeEmailByMax} IS NOT NULL then #{personInChargeEmailByMax}
                                         else personInChargeEmailByMax END
        WHERE id = #{id}
    </update>

    <insert id="insertByParePartAndEquipment">
        INSERT INTO t_configure_equipmentAndSparePart(pl, enginery, discern, productionLine, equipmentName, equipmentId,
                                                      sparePartName, sparePartId, isDelete)
        VALUES ( #{pl}, #{enginery}, #{discern}, #{productionLine}, #{equipmentName}, #{equipmentId}, #{sparePartName}
               , #{sparePartId}, 0)
    </insert>

    <insert id="insertByPersonAndEquipment">
        INSERT INTO t_configure_personAndEquipment(pl, enginery, discern, sparePartName, sparePartCode,
                                                   personInChargeNameBy01, personInChargeWorkIdBy01,
                                                   personInChargeEmailBy01, personInChargeNameBy02,
                                                   personInChargeWorkIdBy02, personInChargeEmailBy02,
                                                   personInChargeNameByMax, personInChargeWorkIdByMax,
                                                   personInChargeEmailByMax, isDelete)
        VALUES (#{pl}, #{enginery}, #{discern}, #{sparePartName}, #{sparePartCode},
                #{personInChargeNameBy01}, #{personInChargeWorkIdBy01}, #{personInChargeEmailBy01},
                #{personInChargeNameBy02}, #{personInChargeWorkIdBy02}, #{personInChargeEmailBy02},
                #{personInChargeNameByMax}, #{personInChargeWorkIdByMax}, #{personInChargeEmailByMax}, 0)
    </insert>

    <select id="existsWithSameFields" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_configure_equipmentAndSparePart
        WHERE pl = #{pl}
          AND enginery = #{enginery}
          AND discern = #{discern}
          AND productionLine = #{productionLine}
          AND equipmentName = #{equipmentName}
          AND equipmentId = #{equipmentId}
          AND sparePartName = #{sparePartName}
          AND sparePartId = #{sparePartId}
          AND isDelete = '0'
    </select>

    <select id="existsWithSameFieldsByPersonAndEquipment" resultType="int">
        SELECT COUNT(*)
        FROM t_configure_personAndEquipment
        WHERE pl = #{pl}
          AND enginery = #{enginery}
          AND discern = #{discern}
          AND sparePartName = #{sparePartName}
          AND sparePartCode = #{sparePartCode}
          AND personInChargeNameBy01 = #{personInChargeNameBy01}
          AND personInChargeEmailBy01 = #{personInChargeEmailBy01}
          AND personInChargeWorkIdBy01 = #{personInChargeWorkIdBy01}
          AND personInChargeNameBy02 = #{personInChargeNameBy02}
          AND personInChargeEmailBy02 = #{personInChargeEmailBy02}
          AND personInChargeWorkIdBy02 = #{personInChargeWorkIdBy02}
          AND personInChargeNameByMax = #{personInChargeNameByMax}
          AND personInChargeEmailByMax = #{personInChargeEmailByMax}
          AND personInChargeWorkIdByMax = #{personInChargeWorkIdByMax}
          AND isDelete = 0
    </select>

    <select id="queryProduction" resultType="java.lang.String">
        SELECT DISTINCT production FROM t_configure_equipmentAndSparePart WHERE pl=#{pl} AND discern=#{discern}
    </select>

    <select id="queryProductionByEdit" resultType="java.lang.String">
        SELECT DISTINCT production FROM [t_configure_equipmentAndYield] WHERE pl=#{pl} AND discern=#{discern}
    </select>

    <select id="queryProductionLineByEdit" resultType="java.lang.String">
        SELECT DISTINCT productionLine
        FROM [t_configure_equipmentAndYield]
        WHERE pl = #{pl}
        AND discern = #{discern} AND production=#{production}
    </select>

    <select id="queryProductionLeader" resultType="java.lang.String">
        SELECT productionLeader
        FROM
            T_Configure_PersonAndProduction
        WHERE production=#{param1} AND productionLine=#{param2}
    </select>
</mapper>