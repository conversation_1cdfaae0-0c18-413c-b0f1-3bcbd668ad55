package cc.mrbird.febs.newo.dao;

import cc.mrbird.febs.newo.entity.*;
import cc.mrbird.febs.newo.entity.qo.GetProductionLine;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
/**
*
* <AUTHOR>
* @date 2022/10/20
*/
@Mapper
@DS(value = "mssql")
public interface EquipmentMapper extends BaseMapper<Equipment> {
    List<PlDiscern> findDiscern (String cardId);
    PlDiscern findDiscern2(String cardId);
    List<Equipment> findSeries(String Discern,String pl);
    List findProductionLine(String Production);
    List findGL(String Production,String ProductionLine);
    List findTDGL(String Production,String ProductionLine,String ClassInfo);
    /**
     * 根据课别查找大组长，轮班组长
     * */
    List<String> findGroup(String Discern,String pl);
    List<String> findProdutLeader(String Discern,String pl);
/**
 * 线长交接表
 * 根据课别查询系列
 * */
    List<String> findProduction(String Discern);
/**
 * 线长交接表
 * 根据系列查询线体
 * */
    List<String> finProductionLineByjj(GetProductionLine getProductionLine);
/** *
 * 线长交接表
 * 根据班别,系列,线体查询投单数据
 * */
    List<String> findTDDJ(String ClassInfo,String Production,String ProductionLine);
   /**
    * 一天可以重复提交多次,但是只保留最新一笔的数据。
    * 在一个班次内提交的次数有且只能有一笔
    * 删除逻辑实现类
    * ①获取当前传入数据的系列线体和班别,判断当前记录数是否在数据库中存在，如果不存在则插入
    * ②如果存在，则删除这条记录，直接插入该条记录
    * ③时间范围为:   白班:  9:00-20:00    晚班:   21:00-9:00
    * ④
    * */
    boolean insertWXBY(WXBY wxby);
  /**
  *
  * <AUTHOR>
  *
  *
  *   线长交接表 =>新需求:
  *   根据D/C 查询对应的工令信息
   *
   *
  */
    List<SelectByDC> selectByDC(SelectDcDto selectDcDto);
    //统计开线数
    int countOpenLineByOne();

    int countOpenLineByTwo();
    //統計生產日報表填寫數
    int countProductionDayByOne();

    int countProductionDayByTwo();
    //統計線長交接表填寫數
    int lineWorkEditByOne();
    int lineWorkEditByTwo();

    List<DjXXVo> findTDDJ11(SelectDjDto selectDjDto);

    List<String> findDiscernByCardId (String cardId);
    List<PlDiscern> selectQC(String cardId);
    List<PlDiscern>    selectMap(String cardId);
    String selectPerms(String cardId);
    List<String> selectAllDataByPl();
    List<String> selectAllDataByDiscern();

    DataRoleByIDS3 selectRoleByCardId(String cardId);
}
