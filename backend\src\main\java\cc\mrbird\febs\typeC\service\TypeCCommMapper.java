package cc.mrbird.febs.typeC.service;

import cc.mrbird.febs.typeC.entity.qo.TableColumnName;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

public interface TypeCCommMapper {
    List<Map<String, Object>> getTableData1(JdbcTemplate jdbcTemplate, TableColumnName tableColumnName);

    List<Map<String, Object>> getTableData2(JdbcTemplate jdbcTemplate, TableColumnName tableColumnName);
    //A产品默认查1000条数据

    List<Map<String,Object>> getTableDataByA1000(JdbcTemplate jdbcTemplate,TableColumnName tableColumnName);
    //非A产品默认查1000条数据
    List<Map<String,Object>> getTableDataByNotA1000(JdbcTemplate jdbcTemplate,TableColumnName tableColumnName);

    List<Map<String,Object>> getTableDataByTest(JdbcTemplate jdbcTemplate,TableColumnName tableColumnName);


}
