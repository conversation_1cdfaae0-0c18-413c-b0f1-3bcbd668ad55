package cc.mrbird.febs.rdProject.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的任務信息（樹形）數據訪問接口
 */
@DS("primary")
public interface RdProjectTaskMainInfoMapper {

    // 插入任務主要信息
    int insertRdProjectTaskMainInfo(RdProjectTaskMainInfo rdProjectTaskMainInfo);

    // 根據任務ID刪除任務主要信息
    int deleteRdProjectTaskMainInfoById(@Param("taskId") Long taskId);

    // 根據父任務ID刪除子任務
    int deleteRdProjectTaskMainInfoByParentId(@Param("parentId") Long parentId);

    // 更新任務主要信息
    int updateRdProjectTaskMainInfo(RdProjectTaskMainInfo rdProjectTaskMainInfo);

    // 根據任務ID查詢任務主要信息
    RdProjectTaskMainInfo selectRdProjectTaskMainInfoById(@Param("taskId") Long taskId);

    // 根據父任務ID查詢子任務列表
    List<RdProjectTaskMainInfo> selectRdProjectTaskMainInfoByParentId(@Param("parentId") Long parentId);

    // 根據創建人工號查詢任務列表
    List<RdProjectTaskMainInfo> selectRdProjectTaskMainInfoByCreatorWorkerId(@Param("creatorWorkerId") String creatorWorkerId);

    // 根據狀態查詢任務列表
    List<RdProjectTaskMainInfo> selectRdProjectTaskMainInfoByStatus(@Param("status") Integer status);

    // 根據樹路徑查詢任務
    RdProjectTaskMainInfo selectRdProjectTaskMainInfoByTreePath(@Param("treePath") String treePath);

    // 查詢所有任務主要信息
    List<RdProjectTaskMainInfo> selectAllRdProjectTaskMainInfo();
}
