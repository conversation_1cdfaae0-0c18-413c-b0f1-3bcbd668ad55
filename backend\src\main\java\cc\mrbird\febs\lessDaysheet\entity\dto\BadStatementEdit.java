package cc.mrbird.febs.lessDaysheet.entity.dto;

import cc.mrbird.febs.lessDaysheet.entity.BadStatementUUID;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BadStatementEdit {
    private static long serialVersionUID = 1L;
    @TableId(value = "id",type = IdType.AUTO)
    @JsonIgnore
    private Integer id;
    @ApiModelProperty(value = "",position = 21)
    @TableField("uuid")
    private String uuid;
    @ApiModelProperty(value = "課別",position = 22)
    private String discern;
    @ApiModelProperty(value = "班别",position = 23)
    private String classInfo;
    @ApiModelProperty(value = "日期",position = 25)
    private String recordDate;
    @TableField("production")
    @ApiModelProperty(value = "產品系列",position = 1)
    private String production;
    @TableField("productionLine")
    @ApiModelProperty(value = "線體",position = 2)
    private String productionLine;
    @TableField("partNumber")
    @ApiModelProperty(value = "料號",position = 3)
    private String partNumber;
    @TableField("workStation")
    @ApiModelProperty(value = "工站",position = 4)
    private String workStation;
    @TableField("inputNumber1")
    @ApiModelProperty(value = "第一節課投入數",position = 5)
    private String inputNumber1;
    @TableField("inputNumber2")
    @ApiModelProperty(value = "第二節課投入數",position = 6)
    private String inputNumber2;
    @TableField("inputNumber3")
    @ApiModelProperty(value = "第三節課投入數",position = 7)
    private String inputNumber3;
    @TableField("inputNumber4")
    @ApiModelProperty(value = "第四節課投入數",position = 8)
    private String inputNumber4;
    @TableField("inputNumber5")
    @ApiModelProperty(value = "第五節課投入數",position = 9)
    private String inputNumber5;
    @TableField("lossRate1")
    @ApiModelProperty(value = "第一節課報廢率",position = 10)
    private String lossRate1;
    @TableField("lossRate2")
    @ApiModelProperty(value = "第二節課報廢率",position = 11)
    private String lossRate2;
    @TableField("lossRate3")
    @ApiModelProperty(value = "第三節課報廢率",position = 12)
    private String lossRate3;
    @TableField("lossRate4")
    @ApiModelProperty(value = "第四節課報廢率",position = 13)
    private String lossRate4;
    @TableField("lossRate5")
    @ApiModelProperty(value = "第五節課報廢率",position = 14)
    private String lossRate5;
    @TableField("yield1")
    @ApiModelProperty(value = "第一節課良率",position = 15)
    private String yield1;
    @TableField("yield2")
    @ApiModelProperty(value = "第二節課良率",position = 16)
    private String yield2;
    @TableField("yield3")
    @ApiModelProperty(value = "第三節課良率",position = 17)
    private String yield3;
    @TableField("yield4")
    @ApiModelProperty(value = "第四節課良率",position = 18)
    private String yield4;
    @TableField("yield5")
    @ApiModelProperty(value = "第五節課良率",position = 19)
    private String yield5;
    private List<BadStatementUUID> badStatementUUID;
}
