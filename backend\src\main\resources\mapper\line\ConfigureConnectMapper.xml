<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.line.dao.ConfigureConnectMapper">

    <insert id="insertConfigureConnect" parameterType="cc.mrbird.febs.line.entity.ConfigureConnect">
        use Schedule
        insert into configure_connect(Factory,Industry,Enginery,DateTime,Discern,Production,
        ProductionLine,PartNumber,Person,ClassInfo,name,
        AttenDance,Appearance,Energy,QC,Discipline,
        Arrange,Decree,Spirit,Quality,newText1,newText2,newText3,Abnormal,Particulars,AbnormalTalk,Processing,TDW,ProcessingDetail,ProcessingTalk,
        ConnectTalk,OtherTalk,ClassKPI_Id)
        values(#{factory},#{industry},#{enginery},#{dateTime},#{discern},#{production},
        #{productionLine},#{partNumber},#{person},#{classInfo},#{name},
        #{attenDance},#{appearance},#{energy},#{qc},#{discipline},
        #{arrange},#{decree},#{spirit},#{quality},#{newText1},#{newText2},#{newText3},#{abnormal},#{particulars},#{abnormalTalk},#{processing},#{tdw},#{processingDetail},#{processingTalk},
        #{connectTalk},#{otherTalk},#{classkpiId})
    </insert>
    <insert id="insertKpi" parameterType="cc.mrbird.febs.line.entity.TConfigureClasskpi">
        use Schedule
        insert into T_Configure_ClassKPI(id,plan_output,real_output,product_quantity,customer_name,customer_quantity,ticket,dc,inbound_order_number,Person,DateTime)VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id},cast(#{item.planOutput} as decimal(18,3)),cast(#{item.realOutput} as decimal(18,3)),#{item.productQuantity},#{item.customerName},#{item.customerQuantity},#{item.ticket},#{item.dc},#{item.inboundOrderNumber},#{item.person},#{item.dateTime})
        </foreach>
    </insert>
    <insert id="insertKpiLock" parameterType="cc.mrbird.febs.line.entity.TConfigureClasskpi">
        use Schedule
        insert into T_Configure_ClassKPI_lock(id,plan_output,real_output,product_quantity,customer_name,customer_quantity,ticket,dc,inbound_order_number,Person,DateTime)VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id},cast(#{item.planOutput} as decimal(18,3)),cast(#{item.realOutput} as decimal(18,3)),#{item.productQuantity},#{item.customerName},#{item.customerQuantity},#{item.ticket},#{item.dc},#{item.inboundOrderNumber},#{item.person},#{item.dateTime})
        </foreach>
    </insert>
    <insert id="insertConfigureConnectLock" >
         use Schedule
        insert into configure_connect_lock(Factory,Industry,Enginery,DateTime,Discern,Production,
        ProductionLine,PartNumber,Person,ClassInfo,name,
        AttenDance,Appearance,Energy,QC,Discipline,
        Arrange,Decree,Spirit,Quality,newText1,newText2,newText3,Abnormal,Particulars,AbnormalTalk,Processing,TDW,ProcessingDetail,ProcessingTalk,
        ConnectTalk,OtherTalk,ClassKPI_Id)
        values(#{factory},#{industry},#{enginery},#{dateTime},#{discern},#{production},
        #{productionLine},#{partNumber},#{person},#{classInfo},#{name},
        #{attenDance},#{appearance},#{energy},#{qc},#{discipline},
        #{arrange},#{decree},#{spirit},#{quality},#{newText1},#{newText2},#{newText3},#{abnormal},#{particulars},#{abnormalTalk},#{processing},#{tdw},#{processingDetail},#{processingTalk},
        #{connectTalk},#{otherTalk},#{classkpiId})
    </insert>
    <update id="updateConfigure" parameterType="cc.mrbird.febs.line.entity.ConfigureConnect">
        use Schedule
        update configure_connect
        <set>
            Factory=#{factory},Industry=#{industry},Enginery=#{energy},DateTime=#{dateTime},Discern=#{discern},Production=#{production},
            ProductionLine=#{productionLine},PartNumber=#{partNumber},Person=#{person},ClassInfo=#{classInfo},name=#{name},
            AttenDance=#{attenDance},Appearance=#{appearance},Energy=#{energy},QC=#{qc},Discipline=#{discipline},
            Arrange=#{arrange},Decree=#{decree},Spirit=#{spirit},Quality=#{quality},Abnormal=#{abnormal},Particulars=#{particulars}
            ,AbnormalTalk=#{abnormalTalk},Processing=#{processing},TDW=#{tdw},ProcessingDetail=#{processingDetail},ProcessingTalk=#{processingTalk},
            ConnectTalk=#{connectTalk},OtherTalk=#{otherTalk},ClassKPI_Id=#{classkpiId}
        </set>
        where
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{productionLine}
        and
        ClassInfo=#{classInfo}
    </update>
    <update id="updateKpi" parameterType="java.util.List">
        use Schedule
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update T_Configure_ClassKPI
            <set>
                id=#{id}
            </set>
        </foreach>
    </update>
    <update id="updateConfigureLock">
        use Schedule
        update configure_connect_lock
        <set>
            Factory=#{factory},Industry=#{industry},Enginery=#{energy},DateTime=#{dateTime},Discern=#{discern},Production=#{production},
            ProductionLine=#{productionLine},PartNumber=#{partNumber},Person=#{person},ClassInfo=#{classInfo},name=#{name},
            AttenDance=#{attenDance},Appearance=#{appearance},Energy=#{energy},QC=#{qc},Discipline=#{discipline},
            Arrange=#{arrange},Decree=#{decree},Spirit=#{spirit},Quality=#{quality},Abnormal=#{abnormal},Particulars=#{particularsa}
            ,AbnormalTalk=#{abnormalTalk},Processing=#{processing},TDW=#{tdw},ProcessingDetail=#{processingDetail},ProcessingTalk=#{processingTalk},
            ConnectTalk=#{connectTalk},OtherTalk=#{otherTalk}
        </set>
    </update>
    <delete id="delKpi">
        use Schedule
        delete from T_Configure_ClassKPI
        where id=#{kpi}
    </delete>
    <delete id="delKpiLock">
        use Schedule
        delete from T_Configure_ClassKPI_lock
        where id=#{kpi}
    </delete>
    <select id="countConfigure" resultType="java.lang.Integer" parameterType="cc.mrbird.febs.line.entity.ConfigureConnect">
        use Schedule
        select count (1)
        from configure_connect
        where
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{productionLine}
        and
        ClassInfo=#{classInfo}
    </select>
    <select id="queryKpi" resultType="java.lang.String">
        use Schedule
        select ClassKPI_Id
        from configure_connect
        where
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{productionLine}
        and
        ClassInfo=#{classInfo}
    </select>
    <select id="countConfigureLock" resultType="java.lang.Integer">
        use Schedule
        select count (1)
        from configure_connect_lock
        <where>
            <if test="beginDate!=null and beginDate!=''">
                and DateTime<![CDATA[  >=  ]]>#{beginDate}
            </if>
            <if test="endDate!=null and endDate!=''">
                and DateTime<![CDATA[  <=  ]]>#{endDate}
            </if>
        </where>
        and
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{productionLine}
        and
        ClassInfo=#{classInfo}
    </select>
    <select id="queryKpiLock" resultType="java.lang.String">
           use Schedule
        select ClassKPI_Id
        from configure_connect_lock
        where
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{productonLine}
        and
        ClassInfo=#{classInfo}
    </select>
    <select id="queryConfigureConnect" resultType="cc.mrbird.febs.line.entity.ConfigureConnect">
        use Schedule
        select Factory,Industry,Enginery,DateTime,Discern,Production,
        ProductionLine,PartNumber,Person,ClassInfo,name,
        AttenDance,Appearance,Energy,QC,Discipline,
        Arrange,Decree,Spirit,Quality,newText1,newText2,newText3,Abnormal,Particulars,AbnormalTalk,Processing,TDW,ProcessingDetail,ProcessingTalk,
        ConnectTalk,OtherTalk,ClassKPI_Id
        from configure_connect
         where
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{line}
        and
        ClassInfo=#{classInfo}
    </select>
    <select id="queryKpiList" resultType="cc.mrbird.febs.line.entity.TConfigureClasskpi">
        use Schedule
        select id,plan_output,real_output,product_quantity,customer_name,customer_quantity,ticket,dc,inbound_order_number,Person,DateTime
        from T_Configure_ClassKPI
        where id=#{kpiId}
    </select>
    <select id="queryConfigureConnectLock" resultType="cc.mrbird.febs.line.entity.ConfigureConnect">
        use Schedule
        select Factory,Industry,Enginery,DateTime,Discern,Production,
        ProductionLine,PartNumber,Person,ClassInfo,name,
        AttenDance,Appearance,Energy,QC,Discipline,
        Arrange,Decree,Spirit,Quality,newText1,newText2,newText3,Abnormal,Particulars,AbnormalTalk,Processing,TDW,ProcessingDetail,ProcessingTalk,
        ConnectTalk,OtherTalk,ClassKPI_Id,PicUrl
        ,PicName
        from configure_connect_lock
        <where>
            <if test="beginDate1!=null and beginDate1!=''">
                and DateTime<![CDATA[  >=  ]]>#{beginDate1}
            </if>
            <if test="endDate1!=null and endDate1!=''">
                and DateTime<![CDATA[  <=  ]]>#{endDate1}
            </if>
        </where>
         and
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{line}
        and
        ClassInfo=#{classInfo}
    </select>
    <select id="queryKpiListLock" resultType="cc.mrbird.febs.line.entity.TConfigureClasskpi">
          use Schedule
        select id,plan_output,real_output,product_quantity,customer_name,customer_quantity,ticket,dc,inbound_order_number,Person,DateTime
        from T_Configure_ClassKPI_lock
        where id=#{kpiId}
    </select>
</mapper>
