package cc.mrbird.febs.productionday.service;

import cc.mrbird.febs.productionday.entity.CapacityTrend;
import cc.mrbird.febs.productionday.entity.TConfigureProductionday;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ITConfigureProductiondayService extends IService<TConfigureProductionday> {

    CapacityTrend queryCapacity(String discern,String date, String name);
}
