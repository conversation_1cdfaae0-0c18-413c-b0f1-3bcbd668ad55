package cc.mrbird.febs.line.dao;

import cc.mrbird.febs.jjkpi.entity.DjxxDto;
import cc.mrbird.febs.line.entity.ConfigureConnect;
import cc.mrbird.febs.line.entity.TConfigureClasskpi;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@DS("mssql")
public interface ConfigureConnectMapper extends BaseMapper<ConfigureConnect> {

    void insertConfigureConnect(ConfigureConnect configureConnect);

    void insertKpi(List<TConfigureClasskpi> list);

    int countConfigure(ConfigureConnect configureConnect);

    void updateConfigure(ConfigureConnect configureConnect);

    void updateKpi(List<TConfigureClasskpi> classkpiList);

    String queryKpi(String discern, String production, String productionLine, String classInfo);

    void delKpi(String kpi);

    int countConfigureLock(String discern, String production, String productionLine, String classInfo, String beginDate, String endDate);

    void updateConfigureLock(ConfigureConnect configureConnect);

    String queryKpiLock(String discern, String production, String productionLine, String classInfo);

    void delKpiLock(String kpi);

    void insertKpiLock(List<TConfigureClasskpi> classkpiList);

    void insertConfigureConnectLock(ConfigureConnect configureConnect);

    ConfigureConnect queryConfigureConnect(String discern, String production, String line, String classInfo);

    List<TConfigureClasskpi> queryKpiList(String kpiId);

    ConfigureConnect queryConfigureConnectLock(String discern, String production, String line, String classInfo, String beginDate1,String endDate1);

    List<TConfigureClasskpi> queryKpiListLock(String kpiId);
}
