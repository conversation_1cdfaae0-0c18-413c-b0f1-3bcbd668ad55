<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.comparation136.dao.Comparation136Mapper">
    <!--136對比表新增-->
    <insert id="saveInformation">
        insert into [APPGCL_Process].[dbo].[136BD_MainInfo]
        (SBU,functions,section,line,CPLH_PFN,BD_Date,ProductClass,BDXM_01,BDXM_02,BDXM_03,BDXM_04,BDXM_05,BDXM_06,BDXM_07,BDXM_08,BDXM_09,BDXM_10,
         SF_NG,NG_Cause,JLR_Name,IPQC_QHRWorkId,IPQC_QHRName,ZZ_QHRWorkId,ZZ_QHRName,Ins_Date,Ins_UserId,Ins_Name,STATUS,Write_Type)
            values (#{SBU},#{functions},#{section},#{line},#{CPLH_PFN},#{BD_Date},#{ProductClass},#{BDXM_01},#{BDXM_02},#{BDXM_03},#{BDXM_04},#{BDXM_05},
        #{BDXM_06},#{BDXM_07},#{BDXM_08},#{BDXM_09},#{BDXM_10},#{SF_NG},#{NG_Cause},#{JLR_Name},#{IPQC_QHRWorkId},#{IPQC_QHRName},#{ZZ_QHRWorkId},#{ZZ_QHRName},#{Ins_Date},#{Ins_UserId},#{Ins_Name},1,'PC')
    </insert>

    <!--向退單表中插入一條數據-->
    <insert id="insertTDInformation">
        insert into [APPGCL_Process].[dbo].[136BD_TUIDAN] (MainInfoID,TD_DATA,TD_TDYY,TD_WorkId,TD_Name,TD_type)
        values (#{MainInfoID},#{TD_DATA},#{TD_TDYY},#{TD_WorkId},#{TD_Name},#{TD_type})
    </insert>




    <!--新增信息之後更新通過id更新簽核狀態-->
    <update id="updateStatus">
        update [APPGCL_Process].[dbo].[136BD_MainInfo]
        set STATUS = '1' where id = #{id}
    </update>

    <!--在簽核過成功改變簽核狀態-->
    <update id="updateCheckStatus">
        update [APPGCL_Process].[dbo].[136BD_MainInfo]
        <set>
            <if test="status != null">STATUS = #{status},</if>
        </set>
        <where>
            <if test="id != null"> id = #{id}</if>
        </where>
    </update>

    <!--被駁回之後重新編輯數據-->
    <update id="updateInformation">
        update [APPGCL_Process].[dbo].[136BD_MainInfo]
        <set>
            <if test="SBU != null"> SBU =#{SBU},</if>
            <if test="functions != null">  functions =#{functions},</if>
            <if test="section != null"> section =#{section},</if>
            <if test="line != null"> line =#{line},</if>
            <if test="CPLH_PFN != null"> CPLH_PFN =#{CPLH_PFN},</if>
            <if test="BD_Date != null"> BD_Date =#{BD_Date},</if>
            <if test="ProductClass != null">  ProductClass =#{ProductClass},</if>
            <if test="BDXM_01 != null">  BDXM_01 =#{BDXM_01},</if>
            <if test="BDXM_02 != null">  BDXM_02 =#{BDXM_02},</if>
            <if test="BDXM_03 != null">  BDXM_03 =#{BDXM_03},</if>
            <if test="BDXM_04 != null">  BDXM_04 =#{BDXM_04},</if>
            <if test="BDXM_05 != null">  BDXM_05 =#{BDXM_05},</if>
            <if test="BDXM_06 != null">  BDXM_06 =#{BDXM_06},</if>
            <if test="BDXM_07 != null">  BDXM_07 =#{BDXM_07},</if>
            <if test="BDXM_08 != null">  BDXM_08 =#{BDXM_08},</if>
            <if test="BDXM_09 != null">  BDXM_09 =#{BDXM_09},</if>
            <if test="BDXM_10 != null">  BDXM_10 =#{BDXM_10},</if>
            <if test="SF_NG != null">  SF_NG =#{SF_NG},</if>
            <if test="NG_Cause != null">  NG_Cause =#{NG_Cause},</if>
            <if test="JLR_Name != null">  JLR_Name =#{JLR_Name},</if>
            <if test="IPQC_QHRWorkId != null">  IPQC_QHRWorkId =#{IPQC_QHRWorkId},</if>
            <if test="IPQC_QHRName != null">  IPQC_QHRName =#{IPQC_QHRName},</if>
            <if test="ZZ_QHRWorkId != null">  ZZ_QHRWorkId =#{ZZ_QHRWorkId},</if>
            <if test="ZZ_QHRName != null">  ZZ_QHRName =#{ZZ_QHRName},</if>
            <if test="Ins_Date != null">  Ins_Date =#{Ins_Date},</if>
            <if test="Ins_UserId != null">  Ins_UserId =#{Ins_UserId},</if>
            <if test="Ins_Name != null">  Ins_Name =#{Ins_Name},</if>
        </set>
        <where>id = #{id}</where>
    </update>

    <!--通过id更新写入信息为PC-->
    <update id="updateWriter">
        update [APPGCL_Process].[dbo].[136BD_MainInfo]
        <set>
            Write_Type = 'PC'
        </set>
        <where>
            <if test="id != null">id = #{id}</if>
        </where>
    </update>


    <!--通過工號查詢產品處和機能-->
   <!-- <select id="queryByWorkId" resultType="cc.mrbird.febs.comparation136.VO.QueryByWorkIdVO">
        select SBU,functions from [APPGCL_Process].[dbo].[136BD_MainInfo] where Ins_UserId = #{Ins_UserId}
    </select>-->

    <!--136對比表通過id查詢詳情數據-->
    <select id="selectById" resultType="cc.mrbird.febs.comparation136.VO.QueryIdComparation136VO">
        select ma.section,ma.line,ma.CPLH_PFN CPLHPFN,ma.BD_Date BDDate,ma.ProductClass,ma.BDXM_01 BDXM01,ma.BDXM_02 BDXM02,ma.BDXM_03 BDXM03,ma.BDXM_04 BDXM04,ma.BDXM_05 BDXM05,ma.BDXM_06 BDXM06,ma.BDXM_07 BDXM07,ma.BDXM_08 BDXM08,ma.BDXM_09 BDXM09,ma.BDXM_10 BDXM10,
               ma.BDXM_11 BDXM11,ma.BDXM_12 BDXM12,
               ma.SF_NG SFNG,ma.NG_Cause NGCause,ma.JLR_Name JLRName,ma.IPQC_QHRName IPQCQHRName,ma.ZZ_QHRName ZZ_QHRName from [APPGCL_Process].[dbo].[136BD_MainInfo] ma
        <where>
            <if test=" id != null ">
               and ma.id = #{id}
            </if>
        </where>
    </select>

    <!--136對比表通過id查詢圖片信息-->
    <select id="queryImageInformation" resultType="cc.mrbird.febs.comparation136.entity.ImageInformation">
        select concat(PicUrl,PicName) as adress,PicOrder,PicUrl,PicName from [APPGCL_Process].[dbo].[136BD_PicUrl]
        <where>
            <if test="MainInfoID != null">
                and MainInfoID = #{MainInfoID}
            </if>
        </where>
    </select>


    <!--查詢新增數據的id-->
    <select id="QueryId" resultType="java.lang.Long">
        select ma.id from [APPGCL_Process].[dbo].[136BD_MainInfo] ma
        <where>
            <if test="Ins_Date != null">
            and ma.Ins_Date = #{Ins_Date}
            </if>
            <if test="JLR_Name != null">
                and ma.JLR_Name = #{JLR_Name}
            </if>
            <if test="IPQC_QHRName != null">
                and ma.BD_Date = #{BD_Date}
            </if>
            <if test="Ins_UserId != null">
                and ma.CPLH_PFN = #{CPLH_PFN}
            </if>
        </where>
    </select>

    <!--通過前端傳過來的id數據查詢該筆單據的簽核狀態-->
    <select id="selectStatusById" resultType="java.lang.Integer">
            select STATUS from [APPGCL_Process].[dbo].[136BD_MainInfo]
            <where>
                <if test="id != null">and id = #{id}</if>
            </where>
    </select>

    <!--根據前端傳過來的數據查詢退單人的名字和工號-->
    <select id="selectTDRName" resultType="java.lang.String">
        select IPQC_QHRName from [APPGCL_Process].[dbo].[136BD_MainInfo]
        <where>
            <if test="id != null">and id = #{id}</if>
        </where>
    </select>

    <select id="selectTDRWorkId" resultType="java.lang.String">
        select IPQC_QHRWorkId from [APPGCL_Process].[dbo].[136BD_MainInfo]
        <where>
            <if test="id != null">and id = #{id}</if>
        </where>
    </select>


    <!--獲取退單組長名字-->
    <select id="selectTDZZName" resultType="java.lang.String">
        select ZZ_QHRName from [APPGCL_Process].[dbo].[136BD_MainInfo]
        <where>
            <if test="id != null">and id = #{id}</if>
        </where>
    </select>

    <!--獲取退單組長工號-->
    <select id="selectTDZZWorkId" resultType="java.lang.String">
        select ZZ_QHRWorkId from [APPGCL_Process].[dbo].[136BD_MainInfo]
        <where>
            <if test="id != null">and id = #{id}</if>
        </where>
    </select>

    <!--通過簽核類型回傳給前端組長、IPQC名字-->
    <select id="queryPersonInformation" resultType="java.lang.String">
        select QHR_Name QHRName, QHR_WorkId QHRWorkId from [APPGCL_Process].[dbo].[136BD_QHRInfo]
        <where>
            <if test="LX_Type != null">and LX_Type = #{LX_Type}</if>
            <if test="LX_Type != null">and section = #{section}</if>
        </where>
    </select>

    <!--查詢產品料號的信息-->
    <select id="queryMaterialNumber" resultType="java.lang.String">
        select CPLH_PFN  from [APPGCL_Process].[dbo].[136BD_CPLH]
        <where>
            <if test="CPLH_PFN != null">CPLH_PFN like concat ('%',#{CPLH_PFN},'%')</if>
        </where>
    </select>



    <!--136對比表按日期查詢-->
    <select id="queryDate" resultType="java.lang.String">
        select BD_Date from [APPGCL_Process].[dbo].[136BD_MainInfo]
        <where>
            <if test="BD_Date != null">BD_Date like concat ('%',#{BD_Date},'%')</if>
        </where>
    </select>

    <!--通過課別查詢品保名字-->
    <select id="queryIPQC" resultType="cc.mrbird.febs.comparation136.VO.QueryIPQCVO">
        select QHR_Name QHRName,QHR_WorkId QHRWorkId from [APPGCL_Process].[dbo].[136BD_QHRInfo]
        <where>
            <if test="section != null">section = #{section} </if>
            and LX_Type = 'IPQC'
        </where>
    </select>

    <!--通過課別查詢組長名字-->
    <select id="queryZZ" resultType="cc.mrbird.febs.comparation136.VO.QueryIPQCVO">
        select QHR_Name QHRName,QHR_WorkId QHRWorkId from [APPGCL_Process].[dbo].[136BD_QHRInfo]
        <where>
        <if test="section != null">section = #{section} </if>
        and LX_Type = '組長'
        </where>
    </select>

    <!--136對比表按課別、班別、料號、日期、線體查詢-->
    <select id="queryPageInformation" resultType="cc.mrbird.febs.comparation136.VO.QueryInformationVO">
        select id,SBU,functions,line,CPLH_PFN CPLHPFN,BD_Date BDDate,ProductClass,SF_NG SFNG,NG_Cause NGCause,STATUS,JLR_Name JLRName,IPQC_QHRName IPQCQHRName,ZZ_QHRName ZZQHRName,Ins_Date InsDate,section from [APPGCL_Process].[dbo].[136BD_MainInfo]
        <where>
            <if test="section != null and section != ''">
                 section = #{section}
            </if>
            <if test="line != null and line != ''">
                AND line = #{line}
            </if>
            <if test="BD_Date != null and BD_Date != ''">
               AND BD_Date = #{BD_Date}
            </if>
            <if test="CPLH_PFN != null and CPLH_PFN != ''">
                AND CPLH_PFN like concat ('%',#{CPLH_PFN},'%')
            </if>
            <if test="ProductClass != null and ProductClass != ''">
               AND ProductClass = #{ProductClass}
            </if>
        </where>
        order by Ins_Date Desc
    </select>

    <!--通過工號查詢課別-->
    <select id="querySection" resultType="java.lang.String">
        select section from [APPGCL_Process].[dbo].[136BD_ZZGX]
        <where>
            <if test="workId != null"> workId = #{workId}</if>
        </where>
    </select>

    <!--根据课别查出线体信息-->
    <select id="queryLine" resultType="java.lang.String">
        select line from [APPGCL_Process].[dbo].[136BD_CPLH]
        <where>
            <if test="section != null">section = #{section}</if>
        </where>
    </select>

    <!--通过课别线体查询料号信息-->
    <select id="selectMaterialNumber" resultType="java.lang.String">
        select CPLH_PFN as CP from [APPGCL_Process].[dbo].[136BD_CPLH]
        <where>
            <if test="section != null"> section = #{section}</if>
            <if test=" line !=null"> and line = #{line}</if>
        </where>
    </select>

    <!--通过工号查询组织-->
    <select id="querySBU" resultType="java.lang.String">
        select SBU from [APPGCL_Process].[dbo].[136BD_ZZGX]
        <where>
            <if test="workId != null"> workId = #{workId}</if>
        </where>
    </select>

    <!--通过工号查询机能-->
    <select id="queryFunctions" resultType="java.lang.String">
        select functions from [APPGCL_Process].[dbo].[136BD_ZZGX]
        <where>
            <if test="workId != null"> workId = #{workId}</if>
        </where>
    </select>

    <!--獲取相關錢和人的工號-->
    <select id="queryWorkId" resultType="cc.mrbird.febs.comparation136.entity.QueryWork">
        select IPQC_QHRWorkId IPQCQHRWorkId,ZZ_QHRWorkId ZZQHRWorkId from [APPGCL_Process].[dbo].[136BD_MainInfo]
        <where>
            <if test="id != null"> id = #{id}</if>
        </where>
    </select>

    <!--查詢駁回人信息-->
    <select id="queryType" resultType="cc.mrbird.febs.comparation136.VO.TypePerson">
        select TD_WorkId WorkId,TD_Name name,TD_type Type,TD_TDYY description from [APPGCL_Process].[dbo].[136BD_TUIDAN]
        <where>
            <if test="MainInfoID != null">
                MainInfoID = #{id}
            </if>
        </where>
    </select>




</mapper>
