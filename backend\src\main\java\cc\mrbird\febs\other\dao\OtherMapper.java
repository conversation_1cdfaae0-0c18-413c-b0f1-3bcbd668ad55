package cc.mrbird.febs.other.dao;

import cc.mrbird.febs.other.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
@DS("two")
public interface OtherMapper {
    List<TConfigureCause> selectAll(SelectDto selectDto);

    List<String> getDiscern();

    List<String> getProduction(String groupName);

    List<String> getProductionLine(ProductionLineDto productionLineDto);

    List<String> getEquipmentName(EquipmentDto equipmentDto);

    boolean updateById(UpdateDto updateDto);
}
