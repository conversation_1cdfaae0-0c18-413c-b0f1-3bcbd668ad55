package cc.mrbird.febs.hmsquality.dao;

import cc.mrbird.febs.hmsquality.entity.HmsPicture;
import cc.mrbird.febs.hmsquality.entity.dto.HMSQualityDto;
import cc.mrbird.febs.hmsquality.param.HMSQualityQueryParam;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:10
 */
@DS("one")
public interface HMSQualityMapper {
    List<HMSQualityDto> selectHMSQualityData(HMSQualityQueryParam hmsQualityQueryParam);

    List<HmsPicture> selectHmsPics(@Param("pzID") Integer pzID);

    //批量查询
    List<HmsPicture> selectHmsPicsByIds(@Param("ids")List<Integer> ids);

    List<String> selectProduction();

    List<String> selectProductionLine(String production);

    List<String> selectDC(String production, String productionLine);

    List<String> selectPFN(String production, String productionLine, String dateCode);

    List<String> selectUser(String production, String productionLine, String dateCode, String pfn);
}
