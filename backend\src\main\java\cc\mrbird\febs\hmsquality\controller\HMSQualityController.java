package cc.mrbird.febs.hmsquality.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionMainInfoDto;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentInspectionQueryParam;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentQueryParam;
import cc.mrbird.febs.equipmentSelfInspection.service.EquipmentSelfInspectionService;
import cc.mrbird.febs.hmsquality.entity.dto.HMSQualityDto;
import cc.mrbird.febs.hmsquality.param.HMSQualityQueryParam;
import cc.mrbird.febs.hmsquality.service.HMSQualityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:08
 */
@Api(tags = "紅墨水品質")
@RequestMapping("/hmsQuality")
@Slf4j
@RestController
@ResponseBody
public class HMSQualityController {
    private final HMSQualityService hmsQualityService;

    @Autowired
    public HMSQualityController(HMSQualityService hmsQualityService) {
        this.hmsQualityService = hmsQualityService;
    }

    //查詢系列
    @GetMapping("/queryProduction")
    @ApiOperation("/獲取系列")
    public FebsResponse queryProduction(){
        List<String> list;
        try {
            list = hmsQualityService.queryProduction();
        }
        catch (Exception e){
            return new FebsResponse().code("500").message("錯誤");
        }
        return  new FebsResponse().code("200").data(list);
    }

    //根據系列查詢線體
    @ApiImplicitParams({
            @ApiImplicitParam(name = "production",value="系列,4189|LGA1700",example = "4189")
    })
    @GetMapping("/queryProductionLine")
    @ApiOperation("獲取線體")
    public FebsResponse queryProductionLine(
                                            @RequestParam(value = "production",required = false)String production){
        List<String> list ;
        try {
            //數據庫中的SBU均為IDS，暫時先將其固定爲IDS
            list = hmsQualityService.queryProductionLine(production);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+production);
        }
        return new FebsResponse().code("200").data(list);
    }

    //根據系列、線體查詢D/C
    @ApiImplicitParams({
            @ApiImplicitParam(name = "production",value="系列,4189|LGA1700",example = "4189"),
            @ApiImplicitParam(name = "productionLine",value="綫體,P3/P4|V3",example = "P3/P4")
    })
    @GetMapping("/queryDateCode")
    @ApiOperation("查詢D/C")
    public FebsResponse queryDateCode(@RequestParam(value = "production",required = false)String production,
                                            @RequestParam(value = "productionLine",required = false)String productionLine){
        List<String> list ;
        try {
            list = hmsQualityService.queryDateCode(production,productionLine);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+production+","+productionLine);
        }
        return new FebsResponse().code("200").data(list);
    }

    //根據系列、線體、D/C查詢料號
    @ApiImplicitParams({
            @ApiImplicitParam(name = "production",value="系列,4189|LGA1700",example = "4189"),
            @ApiImplicitParam(name = "productionLine",value="綫體,P3/P4|V3",example = "P3/P4"),
            @ApiImplicitParam(name = "dateCode",value="日期代碼,4B137|4AX",example = "4B137")
    })
    @GetMapping("/queryPFN")
    @ApiOperation("查詢料號")
    public FebsResponse queryPFN(@RequestParam(value = "production",required = false)String production,
                                      @RequestParam(value = "productionLine",required = false)String productionLine,
                                      @RequestParam(value = "dateCode",required = false)String dateCode){
        List<String> list ;
        try {
            list = hmsQualityService.queryPFN(production,productionLine,dateCode);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+production+","+productionLine+","+dateCode);
        }
        return new FebsResponse().code("200").data(list);
    }

    //根據系列、線體、D/C、料號查詢記錄人員
    @ApiImplicitParams({
            @ApiImplicitParam(name = "production",value="系列,4189|LGA1700",example = "4189"),
            @ApiImplicitParam(name = "productionLine",value="綫體,P3/P4|V3",example = "P3/P4"),
            @ApiImplicitParam(name = "dateCode",value="日期代碼,4B137|4AX",example = "4B137"),
            @ApiImplicitParam(name = "pfn",value="料號,PE41897-01NK5-1H|PE17007-11AA0-1H",example = "PE41897-01NK5-1H")
    })
    @GetMapping("/queryUser")
    @ApiOperation("查詢記錄人員")
    public FebsResponse queryUser(@RequestParam(value = "production",required = false)String production,
                                      @RequestParam(value = "productionLine",required = false)String productionLine,
                                      @RequestParam(value = "dateCode",required = false)String dateCode,
                                      @RequestParam(value = "pfn",required = false)String pfn){
        List<String> list ;
        try {
            list = hmsQualityService.queryUser(production,productionLine,dateCode,pfn);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+production+","+productionLine+","+dateCode);
        }
        return new FebsResponse().code("200").data(list);
    }

    //查詢紅墨水品質記錄
    @ApiOperation(value = "查詢紅墨水品質信息",httpMethod = "POST")
    @PostMapping("/queryHMSQualityData")
    public FebsResponse queryHMSQualityData(@RequestBody HMSQualityQueryParam hmsQualityQueryParam){
        List<HMSQualityDto> list;
        try {
            list = hmsQualityService.queryHMSQualityDataWithPic(hmsQualityQueryParam);
        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+hmsQualityQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    //紅墨水品質記錄數據信息導出
    @ApiOperation("紅墨水品質記錄信息導出")
    @PostMapping("/getHMSQualityDataFile")
    public void getHMSQualityDataFile(@RequestBody HMSQualityQueryParam hmsQualityQueryParam, HttpServletResponse response){
        try{
            //String discern="";
            //把最新的数据写进Excel里,然后再下载
//            EquipmentQueryParam equipmentInspectionDto  = new EquipmentQueryParam();
//            List<EquipmentInspectionDto> list =equipmentSelfInspectionService.selectExampleData();

            List<HMSQualityDto> list = hmsQualityService.queryHMSQualityDataWithPic(hmsQualityQueryParam);
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            XSSFSheet sheet = xssfWorkbook.createSheet("sheet1");
            // 创建一个绘图 patriarch 对象
            XSSFDrawing patriarch = sheet.createDrawingPatriarch();
            //設置表頭
            XSSFRow headerRow  = sheet.createRow(0);
            headerRow .createCell(0).setCellValue("ID");
            headerRow .createCell(1).setCellValue("日期");
            headerRow .createCell(2).setCellValue("D/C");
            headerRow .createCell(3).setCellValue("系列");
            headerRow .createCell(4).setCellValue("綫體");
            headerRow .createCell(5).setCellValue("圖片總數");
            headerRow .createCell(6).setCellValue("圖片");
            headerRow .createCell(7).setCellValue("記錄人員");
            headerRow .createCell(8).setCellValue("上傳日期");
            int rowNum=1;
            for (HMSQualityDto param :list){
                XSSFRow row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(param.getId());
                row.createCell(1).setCellValue(param.getDate());
                row.createCell(2).setCellValue(param.getDateCode());
                row.createCell(3).setCellValue(param.getProduction());
                row.createCell(4).setCellValue(param.getProductionLine());
                row.createCell(5).setCellValue(param.getSum()+"張");
//                row.createCell(6).setCellValue(param.getHmsPictureList());
                List<String> hmsPictureList = param.getHmsPictureList();
                // 合并单元格并插入照片
                int cellIndex = 6;
                row.setHeight((short) (2200));
                sheet.setColumnWidth(cellIndex, 5000 * hmsPictureList.size());
                for (int i = 0; i < hmsPictureList.size(); i++) {
                    BufferedImage bufferedImage = null;
                    int hang = rowNum-1;
                    int lie = cellIndex;
                    //设置单元个宽高，单元格宽高限制了图片的宽高

                    XSSFClientAnchor anchor = null;
//                    File file = new File(hmsPictureList.get(i));
                    String picURL = hmsPictureList.get(i);

                    URL url = new URL(picURL);
                    bufferedImage = ImageIO.read(url.openStream());
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    ImageIO.write(bufferedImage, "jpg", byteArrayOutputStream);
                    /* dx1:图片左边界距离单元格左边框像素值,
                     * dy1:图片上边界距离单元格上边框像素值,
                     * dx2:图片右边界距离单元格右边框像素值（负数）,
                     * dy2:图片下边界距离单元格下边框像素值（负数）,
                     * col1:列下标（0开始），
                     * row1:行下标（0开始），
                     * col2:列下标（1开始），
                     * row2:行下标（1开始）。*/
                    anchor = new XSSFClientAnchor(1500000 * i, 200000, -((1500000 * hmsPictureList.size()) - (1500000 * (i + 1))), -100000, (short) lie, hang, (short) lie + 1, hang + 1);
                    anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                    patriarch.createPicture(anchor, xssfWorkbook.addPicture(byteArrayOutputStream.toByteArray(), XSSFWorkbook.PICTURE_TYPE_JPEG));
                }

                row.createCell(7).setCellValue(param.getUserID()+"("+param.getUserName()+")");
                row.createCell(8).setCellValue(param.getInsDate());

            }
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode("20225sadas.xlsx", "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            /*  response.addHeader("content-Length", "");*/
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/octet-stream");
            response.setHeader("Custom-Status", "200");
            try(OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())){
                xssfWorkbook.write(outputStream);
                outputStream.flush();
            }
            xssfWorkbook.close();
        }catch (Exception e){
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

    }
}
