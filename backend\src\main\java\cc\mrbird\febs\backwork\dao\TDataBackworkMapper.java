package cc.mrbird.febs.backwork.dao;
import cc.mrbird.febs.backwork.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【t_data_backwork】的数据库操作Mapper
* @createDate 2023-02-13 15:59:23
* @Entity cc.mrbird.febs.backwork.entity.TDataBackwork
*/
@Mapper
@DS("mssql")
public interface  TDataBackworkMapper extends BaseMapper<TDataBackwork> {
    //查詢接口(倒敘)
    List<TDataBackwork> selectByproductionLike(String uuid);

    //查詢所有料號
    List<String> selectAllPartNumber();
    //按日期、料號查詢
    List<TDataBackwork> queryByPartNumber(TDataBackWorkDto backWorkDto);

    //新增表單接口

    int insertSelective(TDataBackwork tDataBackwork);

    String selectByFileName(String id);
    //查詢重工單位
    List<String> selectUnit();

    //根据userName 查询role
    List<UserRoleDto>       selectRoleByWorkId(String workId);
    //根据工号查询 role
    Integer queryRoleByWorkId(String workId);
    //查詢品保工程師
    List<String> findQc(String pl);
    List<String> findUnitName(String unit);
    //    課長簽核
    boolean QC1(CheckDto checkDto);
    //品保敲定重工責任單位
     void QC2(CheckDto checkDto);
     void QCNotSP(CheckDto checkDto);
    //重工責任單位簽核
    boolean QC3(CheckDto checkDto);
    //電鍍內部審核
    boolean QC4(CheckDto checkDto);
    //品保最終審核
    boolean QC5(CheckDto checkDto);
    //根據名字查詢  郵箱，role，
    List<UserRoleDto> findEmailANdRole(String name);
    //更改流程編號
    void updateStNo(StNo stNo);
    //查詢流程編號
    String findStNo(String uuid);
    //判定重工單位是不是電鍍
    int  countUnit(String uuid);
    String findEmail(String name);

    String findLeaderName(String uuid);
    String findQh1(String uuid);
    String findQh2(String uuid);
    String findQh3(String uuid);
    String findQh4(String uuid);
    String findQh5(String uuid);

    List<UserRoleDto> selectState(String uuid);

    String selectWorkIdByNewAdd(String name);

    String selectWorkIdByQh1(String name);

    String selectQh2Name(String uud);

    Integer QH1ByPL2AndPL3(CheckDto checkDto);
    Integer QH2ByPL2AndPL3(CheckDto checkDto);
    Integer QH3ByPL2AndPL3(CheckDto checkDto);
    Integer QH4ByPL2AndPL3(CheckDto checkDto);
    Integer QH4ByPL2AndPL3ToKs(CheckDto checkDto);
    Integer QH5ByPL2AndPL3(CheckDto checkDto);
    Integer QH6ByPL2AndPL3(CheckDto checkDto);


    String findQh6(String uuid);

    List<String> findDiscernLeader(RoleAndPl roleAndPl);


    List<String> selectNoName(NoName noName);

    //根据UUID查PL
    String selectPLByUUID(String uuid);
    //根据UUID查stNo
    Integer selectStNoByUUID(String uuid);
    //不管谁点的退签   都退回给填写的用户
    boolean  updateByGoBackCheck(GoBackCheck goBackCheck);
    String selectWorkIdByName(String name);

    int delByUUID(String uuid);

    String selectQHByQh1(String uuid);


    String selectQHByQh3(String uuid);
    String selectQHByZRQC(String uuid);

    int QHByPL1SameUnit(BackWorkSameUnit backWorkSameUnit);

    String selectLineLeaderByUUID(String uuid);

    String selectPartnumber(String uuid);

}




