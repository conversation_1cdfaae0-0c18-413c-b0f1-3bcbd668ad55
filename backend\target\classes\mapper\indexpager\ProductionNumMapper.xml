<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.indexpage.dao.ProductionNumMapper">
    <select id="selectProductionNum" resultType="cc.mrbird.febs.indexpage.entity.TDataByCountProductionNum">
        <!--预测量-->
        USE Schedule
        SELECT [PL],
               [discern],
               SUM([productionNum]) AS [productionNum]
        FROM [Schedule].[dbo].[V_Data_CountProductionNum]
        WHERE PL = #{pl}
          AND discern = #{discern}
        GROUP BY PL, discern
        ORDER BY PL, discern
    </select>

    <insert id="insertByTDataContext">
        USE Schedule
        INSERT INTO t_data_context(subject, context, creatTime, updateTime, workId, username, state)
        values (#{subject}, #{context}, #{creatTime}, #{updateTime}, #{workId}, #{username}, #{state})
    </insert>

    <select id="selectContext" resultType="cc.mrbird.febs.indexpage.entity.TDataContext">
        USE Schedule
        SELECT *
        FROM t_data_context
        ORDER BY state ASC
    </select>

    <select id="countByLine" resultType="java.lang.String">
        USE Schedule
        SELECT COUNT(PartNumber) AS num
        FROM [Schedule].[dbo].[configure_connect_lock]
        WHERE DateTime >= CONVERT(date, GETDATE())
          AND PartNumber = #{workId}
    </select>

    <select id="countByDay" resultType="java.lang.String">
        USE [IDS_BK_PaperLessSystem]
        SELECT COUNT(cardId) AS num
        FROM [T_Configure_ProductionDay_lock]
        WHERE DOM >= CONVERT(date, getdate())
          AND cardId = #{workId}
    </select>

    <select id="countByBackwork" resultType="int">
        USE Schedule
        select count(showId) AS num
        from t_data_backwork
        where showId = #{workId}
    </select>

    <select id="countBackNum" resultType="int">
        USE Schedule
        select count(showId) AS num
        from t_data_backwork
        where showId = #{workId}
          AND isBack = '1'
    </select>

    <select id="getPartNumber" resultType="cc.mrbird.febs.indexpage.entity.PartNumberByIndex">
        USE Android_App
        SELECT line AS productionLine, CPLH_PFN AS partNumber, SCSL_QOP AS number
        FROM Six_TD_DJXX
        WHERE SQR_WorkId = #{workId}
          AND DATE_SCRQ >= #{date} AND DATE_SCRQ &lt;#{date1}
    </select>

    <select id="getProductivityData" resultType="cc.mrbird.febs.indexpage.entity.SumProductivityData">
        USE Schedule
        SELECT CONVERT(varchar(10), DateTime, 120)        AS days,
               SUM(ISNULL(CAST(real_output AS float), 0)) AS num
        FROM T_Configure_ClassKPI_lock
        WHERE id IN (SELECT ClassKPI_Id
                     FROM configure_connect_lock
        WHERE DateTime &lt;= CONVERT(date, getdate())  <!--DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 0)-->
        AND DateTime >= CONVERT(date, getdate() - 7)<!--DATEADD(WEEK, DATEDIFF(WEEK, 0, GETDATE()), 7)-->
        AND Discern = #{param2}
        AND Industry = #{param1})
        GROUP BY CONVERT(varchar(10), DateTime, 120)
    </select>

    <update id="updateContext">
        USE Schedule
        update t_data_context
        set context=#{context},
            subject=#{subject},
            updateTime=#{updateTime},
            username=#{username,jdbcType=VARCHAR},
            state=#{state,jdbcType=VARCHAR}
        where id = #{id}
    </update>

    <select id="selectVisitData" resultType="cc.mrbird.febs.indexpage.entity.VisitPageData">
        USE Schedule
        select pageName, id, workId, visitTime, visitType, visitUrl
        FROM t_data_hisVisit
        WHERE workId = #{workId}
        ORDER BY visitTime DESC
    </select>

    <insert id="insertVisitData">
        USE Schedule
        insert into t_data_hisVisit(workId, pageName, visitTime, visitType, visitUrl)
        VALUES (#{workId}, #{pageName}, #{visitTime}, #{visitType}, #{visitUrl})
    </insert>

    <select id="selectSheetNameData" resultType="cc.mrbird.febs.indexpage.entity.PageData">
        USE Schedule
        select '5'                                               AS target,
               COUNT(productionLine)                             AS reality,
               productionLine,
               CASE
                   WHEN COUNT(productionLine) = 0 then '0'
                   when COUNT(productionLine) = 1 then '20'
                   when COUNT(productionLine) = 2 then '40'
                   when COUNT(productionLine) = 3 then '60'
                   when COUNT(productionLine) = 4 then '80'
                   when COUNT(productionLine) = 5 then '100' else 0 end AS conclude
        FROM t_data_comparison
        <where>
            <if test="industry != null and industry != ''">
                AND industry = #{industry,jdbcType=VARCHAR}
            </if>
            <if test="discern != null and discern != ''">
                AND enginery = #{discern,jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != ''">
                AND spotChecker = #{name,jdbcType=VARCHAR}
            </if>
            <if test="recordDate != null">
                AND recordDate = #{recordDate,jdbcType=TIMESTAMP}
            </if>
        </where>
        GROUP BY productionLine
    </select>

    <select id="selectDataByDay" resultType="cc.mrbird.febs.indexpage.entity.PageData">
        USE IDS_BK_PaperLessSystem
        SELECT '1' AS target, COUNT(ProductionLine) AS reality, ProductionLine AS productionLine,
        CASE
        WHEN COUNT(ProductionLine) = 0 then '0'
        when COUNT(ProductionLine) = 1 then '100'
         end AS conclude
        FROM T_Configure_ProductionDay_lock
        <where>
            <if test="recordDate != null">
                AND DOM = #{recordDate}
            </if>
            <if test="workId != null and workId != ''">
                AND cardId = #{workId}
            </if>
        </where>
        GROUP BY ProductionLine
    </select>

    <select id="selectDataByLine" resultType="cc.mrbird.febs.indexpage.entity.PageData">
        USE Schedule
        SELECT '1' AS target, COUNT(ProductionLine) AS reality, ProductionLine AS productionLine,
        CASE
        WHEN COUNT(ProductionLine) = 0 then '0'
        when COUNT(ProductionLine) = 1 then '100'
        end AS conclude
        FROM configure_connect_lock
        <!--WHERE Person = #{name}
        AND DateTime = #{recordDate}-->
        <where>
            <if test="name != null and name != ''">
                AND Person = #{name}
            </if>
            <if test="recordDate != null">
                AND DateTime = #{recordDate}
            </if>
            <if test="industry != null and industry != ''">
                AND Industry = #{industry}
            </if>
            <if test="discern != null and discern != ''">
                AND Discern = #{discern}
            </if>
        </where>
        GROUP BY ProductionLine
    </select>

    <select id="selectDataByPlan" resultType="cc.mrbird.febs.indexpage.entity.PageData">
        USE Android_App
        SELECT COUNT(line) AS reality, line AS productionLine
        FROM Six_TD_DJXX
        WHERE DATE_SCRQ >= #{recordDate}
          AND DATE_SCRQ &lt; #{endDate}
          AND SQR_WorkId = #{workId}
          AND STATUS = '3'
        GROUP BY line
    </select>

    <select id="countBy136" resultType="int">
        USE Schedule
        select count(productionLine)
        from t_data_comparison
        where spotChecker=#{name}

    </select>

    <select id="countByDayNew" resultType="int">
        USE IDS_BK_PaperLessSystem
        SELECT COUNT(ProductionLine)
        FROM T_Configure_ProductionDay_lock
        WHERE cardId=#{workId}
    </select>

    <select id="countByLineNew" resultType="int">
        USE Schedule
        SELECT COUNT(ProductionLine) FROM configure_connect_lock WHERE PartNumber=#{workId}
    </select>
</mapper>