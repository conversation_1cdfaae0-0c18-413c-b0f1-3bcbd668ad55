package cc.mrbird.febs.clearLine.entity;

import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataClearLine {
    @Ignore
    private String id;
    private String pl;
    private String discern;
    private String production;
    private String productionLine;
    private String recordDate;
    private String startTime;
    private String endTime;
    private String clearLineTime;
    private String oldOrder;
    private String newOrder;
    private String oldPartNumber;
    private String newPartNumber;
    private String oldState;
    private String newState;
    private String remark;
    private String linkId;
    private String qh1;
    private String qh2;
    private String qh3;
    private String stNo;
    private String qhStatus;
    private String showId;
}
