<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.productionday.dao.TConfigureProductiondayMapper">
    <resultMap id="productionLineInfo" type="cc.mrbird.febs.productionday.entity.ProductDayInfo">
        <result column="Factory" jdbcType="VARCHAR" property="factory"/>
        <result column="Industry" jdbcType="VARCHAR" property="industry"/>
        <result column="Enginery" jdbcType="VARCHAR" property="enginery"/>
        <result column="Discern" jdbcType="VARCHAR" property="discern"/>
        <result column="Series" jdbcType="VARCHAR" property="series"/>
        <result column="ProductionLine" jdbcType="VARCHAR" property="productionLine"/>
        <result column="LineLeaderNumber" jdbcType="VARCHAR" property="lineLeaderNumber"/>
        <result column="ClassInfo" jdbcType="VARCHAR" property="classInfo"/>
        <result column="CPLH_PFN" jdbcType="VARCHAR" property="cplh_pfn"/>
        <result column="DATE_SCRQ" jdbcType="VARCHAR" property="date_scrq"/>
    </resultMap>
    <resultMap id="capacityTrend" type="cc.mrbird.febs.productionday.entity.CapacityTrendDto">
        <result column="PQ" jdbcType="INTEGER" property="PQ"/>
        <result column="AQ" jdbcType="INTEGER" property="AQ"/>
        <result column="DV" jdbcType="INTEGER" property="DV"/>
        <result column="achievementRate" jdbcType="VARCHAR" property="achievementRate"/>
    </resultMap>
<!--暂存插入-->
    <insert id="insertConfig" parameterType="cc.mrbird.febs.productionday.entity.TConfigureProductionday">
         USE IDS_BK_PaperLessSystem
        insert into T_Configure_ProductionDay(Industry,Enginery,Discern,Production,
        ProductionLine,ClassInfo,PTL,ShiftLeader,PersonnelShift,DOM,WorkOrder,CustomerName,PartNumber,ProductTX,ScheduleDT,TotalTime,TotalLoadTime,DowntimeLostTime,StandardManpower,ActualManpower,TSR,ACR,CT,TOutput,PQ,AQ,WV,DV,AchievementStatus,RFLF,IPST,Remarks,TimeRate,PerformanceRate,YieldRate,OEE1,OEE,OPE1,UPH,UPPH,FillingDate,cardId,Scsl,ListAq)
        values(#{industry},#{enginery},#{discern},#{production},
        #{productionLine},#{classInfo},#{ptl},
        #{shiftLeader},#{personnelShift},#{dom},#{workOrder},#{customerName},
        #{partNumber},#{productTX},#{scheduleDT},#{totalTime},#{totalLoadTime},#{downtimeLostTime},#{standardManpower},#{actualManpower},#{tsr},#{acr},#{ct},
        #{tOutput},#{pq},#{aq},#{wv},#{dv},#{achievementStatus},#{rflf},#{ipst},#{remarks},#{timeRate},#{performanceRate},#{yieldRate},#{oee1},#{oee},#{ope1},#{uph},#{upph},#{fillingDate},#{cardId},#{scsl},#{listAq})
    </insert>
<!--    锁定插入-->
    <insert id="lockConfig" parameterType="cc.mrbird.febs.productionday.entity.TConfigureProductionday">
        USE IDS_BK_PaperLessSystem
        insert into T_Configure_ProductionDay_lock(Industry,Enginery,Discern,Production,
        ProductionLine,ClassInfo,PTL,ShiftLeader,PersonnelShift,DOM,WorkOrder,CustomerName,PartNumber,ProductTX,ScheduleDT,TotalTime,TotalLoadTime,DowntimeLostTime,StandardManpower,ActualManpower,TSR,ACR,CT,TOutput,PQ,AQ,WV,DV,AchievementStatus,RFLF,IPST,Remarks,TimeRate,PerformanceRate,YieldRate,OEE1,OEE,OPE1,UPH,UPPH,FillingDate,cardId,Scsl,ListAq)
        values(#{industry},#{enginery},#{discern},#{production},
        #{productionLine},#{classInfo},#{ptl},
        #{shiftLeader},#{personnelShift},#{dom},#{workOrder},#{customerName},
        #{partNumber},#{productTX},#{scheduleDT},#{totalTime},#{totalLoadTime},#{downtimeLostTime},#{standardManpower},#{actualManpower},#{tsr},#{acr},#{ct},
        #{tOutput},#{pq},#{aq},#{wv},#{dv},#{achievementStatus},#{rflf},#{ipst},#{remarks},#{timeRate},#{performanceRate},#{yieldRate},#{oee1},#{oee},#{ope1},#{uph},#{upph},#{fillingDate},#{cardId},#{scsl},#{listAq})
    </insert >
    <insert id="insertDBCL_OndutyYield" parameterType="cc.mrbird.febs.productionday.entity.TConfigureProductionday">
        use APPGCL_Process
        insert into DBCL_OndutyYield(functions,section,series,line,DATE_SCRQ,ProductClass,CPZZ_Name,LBZZ_Name,GLBH_Text,CPLH_Text,KHMC_Text,CXTX_characteristic,ztr_time,jhtj_time,zfh_time,djss_time,MB_InputManpower,SJ_InputManpower,MB_ScrapLv,SJ_ScrapLv,CT_cycle,LLCL_yield,JH_yield,SJ_yield,RK_yield,CY_yield,State_yield,TimeRate,PerformanceRate,YieldRate,OEE,OEE1,UPH,UPPH,QCYY_Reason,CLDC_Countermeasure,BeiZhu_Remarks)
        values(#{enginery},#{discern},#{production},#{productionLine},#{dom},#{classInfo},#{ptl},#{shiftLeader},#{workOrder},#{partNumber},#{customerName},#{productTX},#{totalTime},#{scheduleDT},#{totalLoadTime},#{downtimeLostTime},#{standardManpower},#{actualManpower},#{tsr},#{acr},#{ct},#{tOutput},#{pq},#{aq},#{wv},#{dv},#{achievementStatus},#{timeRate},#{performanceRate},#{yieldRate},#{oee},#{oee1},#{uph},#{upph},#{rflf},#{ipst},#{remarks})
    </insert>
    <update id="updateTConfigureProductionDay" parameterType="cc.mrbird.febs.productionday.entity.TConfigureProductionday">
        USE IDS_BK_PaperLessSystem
        update T_Configure_ProductionDay
        <set>
            PTL=#{ptl},ShiftLeader=#{shiftLeader},PersonnelShift=#{personnelShift},DOM=#{dom},WorkOrder=#{workOrder},
            CustomerName=#{customerName},PartNumber=#{partNumber},ProductTX=#{productTX},ScheduleDT=#{scheduleDT},TotalTime=#{totalTime},
            TotalLoadTime=#{totalLoadTime},DowntimeLostTime=#{downtimeLostTime},StandardManpower=#{standardManpower},ActualManpower=#{actualManpower},
            TSR=#{tsr},ACR=#{acr},CT=#{ct},TOutput=#{tOutput},PQ=#{pq},AQ=#{aq},WV=#{wv},DV=#{dv},AchievementStatus=#{achievementStatus},RFLF=#{rflf},IPST=#{ipst},
            Remarks=#{remarks},TimeRate=#{timeRate},PerformanceRate=#{performanceRate},YieldRate=#{yieldRate},OEE1=#{oee1},OEE=#{oee},OPE1=#{ope1},UPH=#{uph},UPPH=#{upph},FillingDate=#{fillingDate},Scsl=#{scsl},ListAq=#{listAq}
        </set>
        where
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{productionLine}
        and
        ClassInfo=#{classInfo}
    </update>


    <select id="queryLinesInfo" resultMap="productionLineInfo">
 use Schedule
SELECT DISTINCT
	A.Factory,
	A.Industry,
	A.Enginery,
	A.Discern,
	A.Series,
	A.ProductionLine,
	B.LineLeaderNumber,
	B.ClassInfo --,B.LineLeaderName,
	,
	C.CPLH_PFN,
	C.DATE_SCRQ
FROM
	T_Configure_EquipmentKPI AS A
	INNER JOIN ( SELECT ProductionLine, LineLeaderNumber, ClassInfo FROM T_Configure_ProductionLineKPI ) AS B ON A.ProductionLine = B.ProductionLine
	INNER JOIN ( SELECT CPLH_PFN, [section], DATE_SCRQ, STATUS, ProductClass, XZ_WorkId FROM [Schedule].[dbo].[V_Date_JJKPI] ) AS C ON B.LineLeaderNumber =#{cardId}
    </select>
    <select id="queryProductionDay" resultType="cc.mrbird.febs.productionday.entity.TConfigureProductionday">
        USE IDS_BK_PaperLessSystem
        select Industry,Enginery,Discern,Production,
        ProductionLine,ClassInfo,PTL,ShiftLeader,PersonnelShift,DOM,WorkOrder,CustomerName,PartNumber,ProductTX,ScheduleDT,TotalTime,TotalLoadTime,DowntimeLostTime,StandardManpower,ActualManpower,TSR,ACR,CT,TOutput,PQ,AQ,WV,DV,AchievementStatus,RFLF,IPST,Remarks,TimeRate,PerformanceRate,YieldRate,OEE1,OEE,OPE1,UPH,UPPH,Scsl,ListAq
        from T_Configure_ProductionDay
        where
        Discern=#{discern}
        and Production=#{production}
        and ProductionLine=#{line}
        and ClassInfo=#{classInfo}
    </select>
    <select id="queryCapacityTrend" resultMap="capacityTrend">
        USE IDS_BK_PaperLessSystem
         SELECT
        sum(PQ)as pq,
        sum(AQ)as aq,
        sum(AQ - PQ)as dv,
        cast((CONVERT(decimal(18,2),sum(AQ)*1.0/sum(PQ))*100) as varchar(50))+'%'as'achievementRate'
        FROM
        T_Configure_ProductionDay_lock
        <where>
            <if test="beginOfDay!=null and beginOfDay!=''">
                and FillingDate<![CDATA[  >=  ]]>#{beginOfDay}
            </if>
            <if test="endOfDay!=null and endOfDay!=''">
                and FillingDate<![CDATA[  <=  ]]>#{endOfDay}
            </if>
        </where>
        and
       Discern=#{discern}
    </select>
    <select id="queryGroupLeader" resultType="java.lang.String">
        use
        Schedule
        select distinct ProductLeader
        from T_Configure_Equipment
        where Discern=#{discern} and Production=#{production}
    </select>
    <select id="queryWorkOrder" resultType="java.lang.String">
        use Schedule
        select distinct  GLBH_WON
        from V_Date_Statement
        where line=#{line}
        and ClassInfo=#{classInfo}
        and Production=#{production}
    </select>
    <select id="queryCustomerName" resultType="java.lang.String">
             use Schedule
        select distinct  KHMC_CN
        from V_Date_Statement
        where KHLH_CMN in
        <foreach collection="khlh" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="queryCplh" resultType="java.lang.String">
             use Schedule
        select distinct  CPLH_PFN
        from V_Date_Statement
        where line=#{line}
    </select>
    <select id="queryProduction" resultType="java.lang.String">
        USE IDS_BK_PaperLessSystem
        select  DISTINCT Production
        from T_Configure_ProductionDay_lock
        where Discern=#{discern}
    </select>
    <select id="queryDayCount" resultType="cc.mrbird.febs.productionday.entity.DayCountDto">
        USE IDS_BK_PaperLessSystem
        SELECT CONVERT
        ( VARCHAR ( 100 ), t.FillingDate, 102 ) [date],
        COUNT ( 1 ) AS count
        FROM
        T_Configure_ProductionDay_lock t
        WHERE
        t.Production= #{production}
        <if test="beginDate!=null and beginDate!=''">
             <![CDATA[and t.FillingDate>=#{beginDate}]]>
        </if>
        <if test="endDate!=null and endDate!=''">
            <![CDATA[and t.FillingDate<=#{endDate}]]>
        </if>
        GROUP BY
        CONVERT ( VARCHAR ( 100 ), t.FillingDate, 102 )
    </select>
    <select id="queryKhlh" resultType="java.lang.String" >
        use Schedule
        select distinct  KHLH_CMN
        from V_Date_Statement
        where GLBH_WON in
        <choose>
            <when test="list.size>0">
                <foreach item="item" index="index"  collection="list" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                ('')
            </otherwise>
        </choose>

    </select>
    <select id="queryScslQops" resultMap="customer">
        use Schedule
        select KHMC_CN,SCSL_QOP
        from V_Date_Statement
        where LineLeaderNumber=#{cardId}
        and Discern=#{discern}
        and Production=#{production}
        and ProductionLine=#{line}
    </select>
    <select id="queryTConfigureProductionDayByTime"
            resultType="cc.mrbird.febs.productionday.entity.TConfigureProductionday">
        USE IDS_BK_PaperLessSystem
        select Industry,Enginery,Discern,Production,
        ProductionLine,ClassInfo,PTL,ShiftLeader,PersonnelShift,DOM,WorkOrder,CustomerName,PartNumber,ProductTX,ScheduleDT,TotalTime,TotalLoadTime,DowntimeLostTime,StandardManpower,ActualManpower,TSR,ACR,CT,TOutput,PQ,AQ,WV,DV,AchievementStatus,RFLF,IPST,Remarks,TimeRate,PerformanceRate,YieldRate,OEE1,OEE,OPE1,UPH,UPPH,Scsl,ListAq
        from T_Configure_ProductionDay_lock
        <where>
            <if test="beginDate1!=null and beginDate1!=''">
                and FillingDate<![CDATA[  >=  ]]>#{beginDate1}
            </if>
            <if test="endDate1!=null and endDate1!=''">
                and FillingDate<![CDATA[  <=  ]]>#{endDate1}
            </if>
            <if test="discern!=null and discern!=''">
                and Discern<![CDATA[  =  ]]>#{discern}
            </if>
            <if test="production!=null and production!=''">
                and Production<![CDATA[  =  ]]>#{production}
            </if>
            <if test="productionline!=null and productionline!=''">
                and ProductionLine<![CDATA[  =  ]]>#{productionline}
            </if>
            <if test="classInfo!=null and classInfo!=''">
                and ClassInfo<![CDATA[  =  ]]>#{classInfo}
            </if>
        </where>
    </select>
    <select id="queryRoleLeader" resultType="java.lang.String">
        USE Schedule
        select distinct GroupLeaderName
        from T_Configure_Equipment
        where Discern=#{discern} and Production=#{production}
    </select>
    <select id="queryCount" resultType="java.lang.Integer">
        USE IDS_BK_PaperLessSystem
        select count(1)
        from T_Configure_ProductionDay
        where
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{line}
        and
        ClassInfo=#{classInfo}
    </select>
    <select id="queryCountLock" resultType="java.lang.Integer">
        USE IDS_BK_PaperLessSystem
        select count(1)
        from T_Configure_ProductionDay_lock
        <where>
            <if test="beginDate!=null and beginDate!=''">
                and FillingDate<![CDATA[  >=  ]]>CONVERT(datetime,#{beginDate})
            </if>
            <if test="endDate!=null and endDate!=''">
                and FillingDate<![CDATA[  <=  ]]>CONVERT(datetime,#{endDate})
            </if>
        </where>
        and
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{line}
        and
        ClassInfo=#{classInfo}
    </select>


    <select id="queryCountLockByTime" resultType="java.lang.Integer">
        USE IDS_BK_PaperLessSystem
        select count(1)
        from T_Configure_ProductionDay_lock
        <where>
            <if test="beginDateTime!=null and beginDateTime!=''">
                and FillingDate<![CDATA[  >=  ]]>#{beginDateTime}
            </if>
            <if test="endDateTime!=null and endDateTime!=''">
                and FillingDate<![CDATA[  <=  ]]>#{endDateTime}
            </if>
        </where>

        and
        Discern=#{discern}
        and
        Production=#{production}
        and
        ProductionLine=#{line}
        and
        ClassInfo=#{classInfo}

    </select>
    <resultMap id="customer" type="cc.mrbird.febs.productionday.entity.CustomerScslQop">
        <result column="KHMC_CN" jdbcType="VARCHAR" property="customerName"/>
        <result column="SCSL_QOP" jdbcType="VARCHAR" property="scslQop"/>
    </resultMap>
</mapper>
