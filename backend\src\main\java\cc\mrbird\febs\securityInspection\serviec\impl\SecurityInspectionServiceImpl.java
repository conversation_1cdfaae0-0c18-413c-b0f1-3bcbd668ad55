package cc.mrbird.febs.securityInspection.serviec.impl;

import cc.mrbird.febs.securityInspection.entity.*;
import cc.mrbird.febs.securityInspection.dao.SecurityInspectionMapper;
import cc.mrbird.febs.securityInspection.serviec.SecurityInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SecurityInspectionServiceImpl implements SecurityInspectionService {

    @Autowired
    private SecurityInspectionMapper securityInspectionMapper;

    /**
     * 查询巡检时间
     * @return
     */
    @Override
    public List<String> queryDate() {
        List<String> list = securityInspectionMapper.queryDate();
        return list;
    }

    /**
     * 查询厂区
     * @return
     */
    @Override
    public List<String> queryFactory() {
        List<String> list = securityInspectionMapper.queryFactory();
        return list;
    }

    /**
     * 查询楼栋
     * @return
     */
    @Override
    public List<String> queryBuilding() {
        List<String> list = securityInspectionMapper.queryBuilding();
        return list;
    }

    /**
     * 查询楼层
     * @return
     */
    @Override
    public List<String> queryFloor() {
        List<String> list = securityInspectionMapper.queryFloor();
        return list;
    }

    /**
     * 查询主要信息
     * @return
     */
    @Override
    public List<SecurityInspectionExportExcelAllPoint> queryInfo(QueryCondition queryCondition) {
        List<SecurityInspectionExportExcelAllPoint> list = securityInspectionMapper.queryInfo(queryCondition);
        return list;
    }


    @Override
    public List<SecurityInspectionExportExcelFloor> queryInfoByFloor(QueryCondition queryCondition) {
        List<SecurityInspectionExportExcelFloor> list = securityInspectionMapper.queryInfoByFloor(queryCondition);
        return list;
    }

    public List<InspectionStationReport> groupByLocation(List<InspectionStationRaw> rawList) {
        return rawList.stream()
                .collect(Collectors.groupingBy(
                        item -> Arrays.asList(item.getBuilding(), item.getFloor(), item.getStation()), // 按点位分组
                        Collectors.mapping(
                                this::toInspectorInfo,
                                Collectors.toList()
                        )
                ))
                .entrySet().stream()
                .map(entry -> {
                    List<String> key = entry.getKey();
                    return new InspectionStationReport(
                            key.get(0), // building
                            key.get(1), // floor
                            key.get(2), // station
                            entry.getValue() // List<InspectorInfo>
                    );
                })
                .collect(Collectors.toList());
    }

    private InspectorInfo toInspectorInfo(InspectionStationRaw raw) {
        return new InspectorInfo(
                raw.getName(),
                raw.getTask(),
                raw.getNumber(),
                raw.getNgnumber()
        );
    }

}
