package cc.mrbird.febs.newo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "T_Configure_Equipment")
public class Equipment {
    @TableField(value = "Enginery")
    private String Enginery;
    @TableField(value = "PL")
    private String PL;
    @TableField(value = "Discern")
    private String Discern;
    @TableField(value = "Production")
    private String Production;
    @TableField(value = "ProductionLine")
    private String ProductionLine;
    @TableField(value = "SectionManagerName")
    private String SectionManagerName;
    @TableField(value = "SectionManagerNumber")
    private String SectionManagerNumber;
    @TableField(value = "ProductLeader")
    private String ProductLeader;
    @TableField(value = "ProductLeaderNumber")
    private String ProductLeaderNumber;
    @TableField(value = "GroupLeaderName")
    private String GroupLeaderName;
    @TableField(value = "GroupLeaderNumber")
    private String GroupLeaderNumber;
    @TableField(value = "LineLeaderName")
    private String LineLeaderName;
    @TableField(value = "LineLeaderNumber")
    private String LineLeaderNumber;
    @TableField(value = "ClassInfo")
    private String ClassInfo;
}
