package cc.mrbird.febs.rdProject.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的任務附件表實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectFile {

    @ApiModelProperty(value = "文件ID", position = 0)
    private Long fileId;

    @ApiModelProperty(value = "原始文件名", position = 1)
    private String fileName;

    @ApiModelProperty(value = "服务器存储路径", position = 2)
    private String filePath;

    @ApiModelProperty(value = "关联的任务ID", position = 3)
    private Long taskId;
}
