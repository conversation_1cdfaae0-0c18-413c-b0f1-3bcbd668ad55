package cc.mrbird.febs.rdProject.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import cc.mrbird.febs.rdProject.entity.RdProjectUserExtra;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理人員擴展信息表數據訪問接口
 */
@DS("primary")
public interface RdProjectUserExtraMapper {

    // 插入用戶擴展信息
    int insertRdProjectUserExtra(RdProjectUserExtra rdProjectUserExtra);

    // 根據工號刪除用戶擴展信息
    int deleteRdProjectUserExtraByWorkerId(@Param("workerId") String workerId);

    // 根據用戶ID刪除用戶擴展信息
    int deleteRdProjectUserExtraByUserId(@Param("userId") Long userId);

    // 更新用戶擴展信息
    int updateRdProjectUserExtra(RdProjectUserExtra rdProjectUserExtra);

    // 根據工號查詢用戶擴展信息
    RdProjectUserExtra selectRdProjectUserExtraByWorkerId(@Param("workerId") String workerId);

    // 根據用戶ID查詢用戶擴展信息
    RdProjectUserExtra selectRdProjectUserExtraByUserId(@Param("userId") Long userId);

    // 根據用戶名查詢用戶擴展信息列表
    List<RdProjectUserExtra> selectRdProjectUserExtraByUserName(@Param("userName") String userName);

    // 根據團隊ID查詢用戶擴展信息列表
    List<RdProjectUserExtra> selectRdProjectUserExtraByBelongTeamId(@Param("belongTeamId") Long belongTeamId);

    // 根據廠區查詢用戶擴展信息列表
    List<RdProjectUserExtra> selectRdProjectUserExtraBySiteArea(@Param("siteArea") String siteArea);

    // 根據產品処查詢用戶擴展信息列表
    List<RdProjectUserExtra> selectRdProjectUserExtraBySbu(@Param("sbu") String sbu);

    // 根據機能查詢用戶擴展信息列表
    List<RdProjectUserExtra> selectRdProjectUserExtraByFunctions(@Param("functions") String functions);

    // 根據課別查詢用戶擴展信息列表
    List<RdProjectUserExtra> selectRdProjectUserExtraBySection(@Param("section") String section);

    // 查詢所有用戶擴展信息
    List<RdProjectUserExtra> selectAllRdProjectUserExtra();
}
