<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectUserExtraMapper">

    <!-- 結果映射 -->
    <resultMap id="RdProjectUserExtraResultMap" type="cc.mrbird.febs.rdProject.entity.RdProjectUserExtra">
        <id column="worker_id" property="workerId"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="site_area" property="siteArea"/>
        <result column="sbu" property="sbu"/>
        <result column="functions" property="functions"/>
        <result column="section" property="section"/>
        <result column="belong_team_id" property="belongTeamId"/>
    </resultMap>

    <!-- 插入用戶擴展信息 -->
    <insert id="insertRdProjectUserExtra" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectUserExtra">
        INSERT INTO rd_project_user_extra (user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id)
        VALUES (#{userId}, #{workerId}, #{userName}, #{siteArea}, #{sbu}, #{functions}, #{section}, #{belongTeamId})
    </insert>

    <!-- 根據工號刪除用戶擴展信息 -->
    <delete id="deleteRdProjectUserExtraByWorkerId">
        DELETE FROM rd_project_user_extra WHERE worker_id = #{workerId}
    </delete>

    <!-- 根據用戶ID刪除用戶擴展信息 -->
    <delete id="deleteRdProjectUserExtraByUserId">
        DELETE FROM rd_project_user_extra WHERE user_id = #{userId}
    </delete>

    <!-- 更新用戶擴展信息 -->
    <update id="updateRdProjectUserExtra" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectUserExtra">
        UPDATE rd_project_user_extra
        SET user_id = #{userId},
            user_name = #{userName},
            site_area = #{siteArea},
            sbu = #{sbu},
            functions = #{functions},
            section = #{section},
            belong_team_id = #{belongTeamId}
        WHERE worker_id = #{workerId}
    </update>

    <!-- 根據工號查詢用戶擴展信息 -->
    <select id="selectRdProjectUserExtraByWorkerId" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE worker_id = #{workerId}
    </select>

    <!-- 根據用戶ID查詢用戶擴展信息 -->
    <select id="selectRdProjectUserExtraByUserId" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE user_id = #{userId}
    </select>

    <!-- 根據用戶名查詢用戶擴展信息列表 -->
    <select id="selectRdProjectUserExtraByUserName" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE user_name LIKE CONCAT('%', #{userName}, '%')
    </select>

    <!-- 根據團隊ID查詢用戶擴展信息列表 -->
    <select id="selectRdProjectUserExtraByBelongTeamId" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE belong_team_id = #{belongTeamId}
    </select>

    <!-- 根據廠區查詢用戶擴展信息列表 -->
    <select id="selectRdProjectUserExtraBySiteArea" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE site_area = #{siteArea}
    </select>

    <!-- 根據產品処查詢用戶擴展信息列表 -->
    <select id="selectRdProjectUserExtraBySbu" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE sbu = #{sbu}
    </select>

    <!-- 根據機能查詢用戶擴展信息列表 -->
    <select id="selectRdProjectUserExtraByFunctions" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE functions = #{functions}
    </select>

    <!-- 根據課別查詢用戶擴展信息列表 -->
    <select id="selectRdProjectUserExtraBySection" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
        WHERE section = #{section}
    </select>

    <!-- 查詢所有用戶擴展信息 -->
    <select id="selectAllRdProjectUserExtra" resultMap="RdProjectUserExtraResultMap">
        SELECT user_id, worker_id, user_name, site_area, sbu, functions, section, belong_team_id
        FROM rd_project_user_extra
    </select>

</mapper>
