<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.other.dao.OtherMapper">

<select id="selectAll" resultType="cc.mrbird.febs.other.entity.TConfigureCause">
SELECT * FROM IDS_BK_PL1_01.dbo.T_Data_ErrorCausation
WHERE Equipment_id IN
(SELECT Equipment_id FROM IDS_BK_PL1_01.dbo.T_Data_Group_Eq WHERE GroupName=#{groupName}
AND ProductRange=#{production} AND ProductionLine=#{productionLine} AND Equipment_Name=#{equipmentName})
</select>

<select id="getDiscern" resultType="java.lang.String">
    SELECT  DISTINCT  GroupName AS groupName
    FROM [IDS_BK_PL1_01].[dbo].[T_Data_Group_Eq] WHERE GroupName IS  NOT NULL
</select>

<select id="getProduction" resultType="java.lang.String">
    SELECT  DISTINCT  ProductRange AS production
    FROM [IDS_BK_PL1_01].[dbo].[T_Data_Group_Eq] WHERE GroupName =#{groupName}
    </select>

<select id="getProductionLine" resultType="java.lang.String">
    SELECT  DISTINCT  ProductionLine AS prodcutionLine
    FROM [IDS_BK_PL1_01].[dbo].[T_Data_Group_Eq] WHERE GroupName=#{groupName} AND ProductRange=#{production}
    </select>

<select id="getEquipmentName" resultType="java.lang.String">
    SELECT DISTINCT Equipment_Name AS equipmentName FROM   [IDS_BK_PL1_01].[dbo].[T_Data_Group_Eq] WHERE GroupName=#{groupName} AND ProductRange=#{production} AND ProductionLine=#{productionLine}
    </select>

<update id="updateById">
    UPDATE  [IDS_BK_PL1_01].dbo.T_Data_ErrorCausation  set StandardMaintenanceDuration=#{standardMaintenanceDuration}
    WHERE id =#{id}
    </update>
</mapper>