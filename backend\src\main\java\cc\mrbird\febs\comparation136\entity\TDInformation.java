package cc.mrbird.febs.comparation136.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TDInformation implements Serializable {

    //主表id
    private Long MainInfoID;

    //退單日期
    private String TD_DATA;

    //退單原因
    private String TD_TDYY;

    //退單工號
    private String TD_WorkId;

    //退單名字
    private String TD_Name;

    //退單類型
    private String TD_type;
}
