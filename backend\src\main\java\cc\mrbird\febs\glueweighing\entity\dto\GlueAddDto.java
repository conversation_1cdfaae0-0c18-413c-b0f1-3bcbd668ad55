package cc.mrbird.febs.glueweighing.entity.dto;

import cc.mrbird.febs.glueweighing.entity.Weight;
import cc.mrbird.febs.glueweighing.entity.WeightAnbnormal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GlueAddDto {
    private Weight weight;
    private List<WeightAnbnormal> weightAnbnormals;
}
