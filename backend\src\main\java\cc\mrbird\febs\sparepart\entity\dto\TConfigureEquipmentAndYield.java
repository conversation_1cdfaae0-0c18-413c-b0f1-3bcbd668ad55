package cc.mrbird.febs.sparepart.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TConfigureEquipmentAndYield {
    @ApiModelProperty(value = "表唯一id",position = 0)
    private Integer id;
    @ApiModelProperty(value = "事業處",position = 1)
    private String pl;
    @ApiModelProperty(value = "機能",position = 2)
    private String enginery;
    @ApiModelProperty(value = "課別",position = 3)
    private String discern;
    @ApiModelProperty(value = "系列",position = 4)
    private String production;
    @ApiModelProperty(value = "線體",position = 5)
    private String productionLine;
    @ApiModelProperty(value = "設備名稱",position = 6)
    private String equipmentName;
    @ApiModelProperty(value = "備品名稱",position = 7)
    private String sparePartName;
    @ApiModelProperty(value = "壽命上限",position = 8)
    private String lifeLimit;
    @ApiModelProperty(value = "實際產量",position = 9)
    private String actualOutput;
    @ApiModelProperty(value = "操作原因",position = 10)
    private String operationalReason;
    @ApiModelProperty(value = "操作類型",position = 11)
    private String        operationType;
    @ApiModelProperty(value = "更換備件時間",position = 12)
    private String        equipmentUpdateTime;
    @ApiModelProperty(value = "監控狀態",position = 13)
    private String monitorState;
    @ApiModelProperty(value = "執行操作",position = 14)
    private  String performActions;
    @ApiModelProperty(value = "維護記錄",position = 15)
    private String maintenanceRecord;
    @ApiModelProperty(value = "是否推送",position = 16)
    private String isSendEmail;
    private String eqid;
}
