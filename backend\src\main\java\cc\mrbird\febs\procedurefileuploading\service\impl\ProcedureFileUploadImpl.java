package cc.mrbird.febs.procedurefileuploading.service.impl;

import cc.mrbird.febs.backwork.service.Email;
import cc.mrbird.febs.procedurefileuploading.dao.ProcedureFileUploadMapper;
import cc.mrbird.febs.procedurefileuploading.entity.dto.TDataProcedureFileUpload;
import cc.mrbird.febs.procedurefileuploading.entity.qo.GetProcedureFileUploadDataQo;
import cc.mrbird.febs.procedurefileuploading.service.ProcedureFileUploadService;
import cc.mrbird.febs.procedurefileuploading.util.ArtCodeByEmail;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class ProcedureFileUploadImpl implements ProcedureFileUploadService {
    private final ProcedureFileUploadMapper procedureFileUploadMapper;
    private final Email email;

    @Autowired
    public ProcedureFileUploadImpl(ProcedureFileUploadMapper procedureFileUploadMapper, Email email) {
        this.procedureFileUploadMapper = procedureFileUploadMapper;
        this.email = email;
    }

    @Override
    public String uploadFileByProcedure(List<MultipartFile> files) throws IOException {
        if (files==null){
            throw new IOException("files is null");
        }
        File directory = null;
        if (files.size() > 1) {
            String directoryPath = "D://Fit/biaozhunchengshi/multiple_files";
            directory = new File(directoryPath);
            if (!directory.exists()) {
                directory.mkdirs();
            }
        }
        String fullFilePath =null;
        for (MultipartFile file : files) {
            String fileName = file.getOriginalFilename();
            if (StrUtil.isBlank(fileName)){
                continue;
            }
            //上传后的URL全路径
            if (files.size() == 1) {
                fullFilePath = "D://Fit/biaozhunchengshi/" + fileName;
            } else {
                fullFilePath = directory.getPath() + "/" + fileName;
            }
            FileUtil.writeFromStream(file.getInputStream(), fullFilePath);
        }
        return fullFilePath;
    }

    @Override
    public int insertFileUploadData(TDataProcedureFileUpload tDataProcedureFileUpload) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fileVersion = "V" +sdf.format(new Date())+String.format("%02d", 01);
        tDataProcedureFileUpload.setFileVersionCode(fileVersion);
        tDataProcedureFileUpload.setShowId("1");
        tDataProcedureFileUpload.setUpdateNum(1);
        return procedureFileUploadMapper.insertFileData(tDataProcedureFileUpload);
    }

    @Override
    public List<String> getPl() {
        List<String> list=   procedureFileUploadMapper.getPL();
        return list;
    }

    @Override
    public List<String> getDiscern(String pl,String enginery) {
        List<String> list =procedureFileUploadMapper.getDiscern( pl,enginery);
        return list;
    }

    @Override
    public List<String> getProduction(String pl, String discern) {
        List<String> list =procedureFileUploadMapper.getProduction(pl,discern);
        return list;
    }

    @Override
    public List<String> getProductionLine(String pl, String discern, String production) {
        List<String> list =procedureFileUploadMapper.getProductionLine(pl,discern,production);
        return list;
    }

    @Override
    public List<TDataProcedureFileUpload> getProcedureFileUploadData(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<TDataProcedureFileUpload> list = procedureFileUploadMapper.getProcedureFileUploadData(getProcedureFileUploadDataQo);
        return list;
    }

    @Override
    public List<String> getEquipmentName(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
       List<String> list =procedureFileUploadMapper.getProcedureFileUploadEquipmentName(getProcedureFileUploadDataQo);
        return list;
    }

    @Override
    public List<String> getProductionByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
       List<String> list =procedureFileUploadMapper.getProductionByUploadFile(getProcedureFileUploadDataQo);
        return list;
    }

    @Override
    public List<String> getProductionLineByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list =procedureFileUploadMapper.getProductionLineByUploadFile(getProcedureFileUploadDataQo);
        return list;
    }

    @Override
    public List<String> getEquipmentNameByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list =procedureFileUploadMapper.getEquipmentNameByUploadFile(getProcedureFileUploadDataQo);
        return list;
    }

    @Override
    public List<String> getEquipmentCodeByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list =procedureFileUploadMapper.getEquipmentCodeByUploadFile(getProcedureFileUploadDataQo);
        return list;
    }

    @Override
    public String updateFileUpload(TDataProcedureFileUpload tDataProcedureFileUpload) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Integer updateNum =procedureFileUploadMapper.queryUpdateNum(tDataProcedureFileUpload.getId());
        tDataProcedureFileUpload.setUpdateNum(updateNum);
        String fileVersion = "V" +sdf.format(new Date())+String.format("%02d",tDataProcedureFileUpload.getUpdateNum());
        String existingCode = fileVersion != null ? fileVersion.substring(9) : "01";
        int intCode = Integer.parseInt(existingCode) + 1;
        String newCode = intCode < 10 ? "0" + intCode : String.valueOf(intCode);
        fileVersion="V"+ sdf.format(new Date())+newCode;
        tDataProcedureFileUpload.setFileVersionCode(fileVersion);
        tDataProcedureFileUpload.setUpdateNum(tDataProcedureFileUpload.getUpdateNum()+1);
        tDataProcedureFileUpload.setShowId("1");
        //先插入新的文件,然后再修改对应的ShowID,方便前端根据SHOWID 来显示更新按钮
        int num= procedureFileUploadMapper.insertFileDataByUpdate(tDataProcedureFileUpload);
        int uNum=procedureFileUploadMapper.updateShowId(tDataProcedureFileUpload.getId());
        sendEmail(tDataProcedureFileUpload);
        return "200";
    }
    /**
    * <AUTHOR>
    * @date: 2024-07-24
    * @Time: 上午 09:59
     * 异步线程发送邮件  防止接口访问超时  POST请求 请求两次  异步线程执行是一致性的 即 接口访问失败  邮件不会推送
    */
    @Async
    public void sendEmail(TDataProcedureFileUpload tDataProcedureFileUpload) throws IOException {
                ArtCodeByEmail artCodeByEmail = new ArtCodeByEmail();
                String name =tDataProcedureFileUpload.getUploadUser();
                String sendEmail=procedureFileUploadMapper.sendEmail(name);
                String secondName=procedureFileUploadMapper.selectSecondName(name);
                String cc="<EMAIL>";
                //String sendEmail="<EMAIL>";
                if ("0".equals(sendEmail)){
                }else {
                    String content =artCodeByEmail.getCode(
                            tDataProcedureFileUpload.getProduction(),
                            tDataProcedureFileUpload.getProductionLine(),
                            secondName,
                            tDataProcedureFileUpload.getFileVersionCode(),
                            tDataProcedureFileUpload.getUploadUser(),
                            tDataProcedureFileUpload.getRemark()
                    );
                    String subject = "標準程式版次變更提示";
                    subject = URLEncoder.encode(subject, "utf-8");
                    email.selectEmail(sendEmail,subject,content,cc);
                }

    }
    //根据工号获取机能

    @Override
    public String queryEngineryByWorkId(String workId) {
        String enginery = procedureFileUploadMapper.queryEngineryByWorkId(workId);
       enginery= enginery.substring(0,2);
        //System.out.println("截取後的字符串為:"+enginery);
        return enginery;
    }









}
