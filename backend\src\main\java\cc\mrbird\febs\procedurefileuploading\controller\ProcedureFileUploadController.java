package cc.mrbird.febs.procedurefileuploading.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.procedurefileuploading.entity.dto.TDataProcedureFileUpload;
import cc.mrbird.febs.procedurefileuploading.entity.qo.GetProcedureFileUploadDataQo;
import cc.mrbird.febs.procedurefileuploading.entity.vo.FileVo;
import cc.mrbird.febs.procedurefileuploading.service.ProcedureFileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Api("標準程式文件上傳")
@RestController
@ResponseBody
@RequestMapping("/procedureFileUpload")
@Slf4j
public class ProcedureFileUploadController {
    private final ProcedureFileUploadService procedureFileUploadService;


    private static final String UTF8 = "UTF-8";

    @Autowired
    public ProcedureFileUploadController(ProcedureFileUploadService procedureFileUploadService) {
        this.procedureFileUploadService = procedureFileUploadService;
    }

    //查询PL
    @ApiOperation("获取PL-查询")
    @PostMapping("/getPl")
    public FebsResponse getPl() {
        List<String> list = procedureFileUploadService.getPl();
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("根据pl获取課別-查询")
    @GetMapping("/getDiscern")
    public FebsResponse getDiscern(@RequestParam("pl") String pl,@RequestParam("enginery") String enginery) {
        List<String> list = procedureFileUploadService.getDiscern( pl,enginery);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("根據pl和課別獲取系列-查询")
    @GetMapping("/getProduction")
    public FebsResponse getProduction(@RequestParam("pl") String pl, @RequestParam("discern") String discern) {
        List<String> list = procedureFileUploadService.getProduction(pl, discern);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("根據Pl、課別、系列獲取線體-查询")
    @GetMapping("/getProductionLine")
    public FebsResponse getProductionLine(@RequestParam("pl") String pl, @RequestParam("discern") String discern, @RequestParam("production") String production) {
        List<String> list = procedureFileUploadService.getProductionLine(pl, discern, production);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("设备下拉框数据，equipmentName参数不需要传")
    @PostMapping("/getEquipmentName")
    public FebsResponse getEquipmentName(@RequestBody GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list = procedureFileUploadService.getEquipmentName(getProcedureFileUploadDataQo);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("根據pl、課別、系列、線體查詢對應的數據-查询")
    @PostMapping("/getProcedureFileUploadData")
    public FebsResponse getProcedureFileUploadData(@RequestBody GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<TDataProcedureFileUpload> list = procedureFileUploadService.getProcedureFileUploadData(getProcedureFileUploadDataQo);
        return new FebsResponse().code("200").data(list);
    }

    //文件上傳相關
    @ApiOperation("文件上傳-獲取系列，參數傳PL和課別")
    @PostMapping("/getProductionByUploadFile")
    public FebsResponse getProductionByUploadFile(@RequestBody GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list = procedureFileUploadService.getProductionByUploadFile(getProcedureFileUploadDataQo);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("文件上傳-獲取線體，參數傳PL、課別、系列")
    @PostMapping("/getProductionLineByUploadFile")
    public FebsResponse getProductionLineByUploadFile(@RequestBody GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list = procedureFileUploadService.getProductionLineByUploadFile(getProcedureFileUploadDataQo);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("文件上傳-设备名称，參數傳PL、課別、系列、線體")
    @PostMapping("/getEquipmentNameByUploadFile")
    public FebsResponse getEquipmentNameByUploadFile(@RequestBody GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list = procedureFileUploadService.getEquipmentNameByUploadFile(getProcedureFileUploadDataQo);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation("文件上傳-獲取設備ID，參數傳 pl、discern、production、productionLine、equipmentName")
    @PostMapping("/getEquipmentCodeByUploadFile")
    public FebsResponse getEquipmentCodeByUploadFile(@RequestBody GetProcedureFileUploadDataQo getProcedureFileUploadDataQo) {
        List<String> list = procedureFileUploadService.getEquipmentCodeByUploadFile(getProcedureFileUploadDataQo);
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/getVersionNum")
    @ApiOperation("获取版本号,参数为空代表是最新的，传了代表获取自增1的版本号")
    public FebsResponse getVersionNum(@RequestParam("fileVersion") String fileVersion) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (fileVersion.isEmpty() || fileVersion.equals(" ")) {
            fileVersion = "V" + sdf.format(new Date()) + String.format("%02d", 01);
            return new FebsResponse().code("200").data(fileVersion);
        }
        String existingCode = fileVersion != null ? fileVersion.substring(9) : "01";
        int intCode = Integer.parseInt(existingCode) + 1;
        String newCode = intCode < 10 ? "0" + intCode : String.valueOf(intCode);
//      System.out.println(newCode);
//      System.out.println("V"+sdf.format(new Date())+newCode);
        fileVersion = "V" + sdf.format(new Date()) + newCode;
        return new FebsResponse().code("200").data(fileVersion);
    }

    //TODO 加 @requestParam、@RequestBody 俩注解 防止空指针异常
    @ApiOperation("文件、多文件、文件夾上傳接口")
    //@RequestMapping(value="/uploadSoundCode",method= RequestMethod.POST)
    @PostMapping("/uploadSoundCode")
    public FileVo uploadFile(List<MultipartFile> multipartFiles) throws IOException {
        String filePath = procedureFileUploadService.uploadFileByProcedure(multipartFiles);
        return new FileVo().code("200").filePath(filePath);
    }

    @ApiOperation("文件相關數據插入")
    @PostMapping("/insertFileUpload")
    public FebsResponse insertFileUpload(@RequestBody TDataProcedureFileUpload tDataProcedureFileUpload) {
        return new FebsResponse().code("200").message("插入成功，數量為:" + procedureFileUploadService.insertFileUploadData(tDataProcedureFileUpload));
    }

    @ApiOperation("文件相關數據更新")
    @PostMapping("/updateFileUpload")
    public FebsResponse updateFileUpload(@RequestBody TDataProcedureFileUpload tDataProcedureFileUpload) throws IOException {
        procedureFileUploadService.updateFileUpload(tDataProcedureFileUpload);
        System.out.println(tDataProcedureFileUpload);
        return new FebsResponse().code("200").message("更新成功").data(tDataProcedureFileUpload);
    }

    //文件下载
    //TODO 加 @requestParam、@RequestBody 俩注解 防止空指针异常
    @ApiOperation("文件下载")
    @GetMapping("/getFileDownload")
    @CrossOrigin(origins = "*")
    public void getBatchFileDownload(@RequestParam List<String> filePaths, HttpServletResponse response) {
        try {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/octet-stream");
            for (String filePath : filePaths) {
                File file = new File(filePath);
                String fileName = file.getName();
                try (FileInputStream fis = new FileInputStream(file);
                     BufferedInputStream is = new BufferedInputStream(fis)) {
                    byte[] buffer = new byte[is.available()];
                    is.read(buffer);
                    //TODO 文件编码格式受浏览器限制，部分场景无法生效，会乱码，建议使用非中文命名,一定用中文建议前端处理
                    //String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
                    response.addHeader("Content-Disposition", "attachment;filename=\"" + new String(fileName.getBytes("utf-8"), "ISO8859-1"));
                    response.addHeader("content-Length", String.valueOf(file.length()));
                    try (OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
                        outputStream.write(buffer);
                        outputStream.flush();
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    @GetMapping("/queryEngineryByWorkId")
    @ApiOperation("根據工號獲取機能，例如：裝配、電鍍、成型")
    public FebsResponse queryEngineryByWorkId(@RequestParam("workId")String workId){
        String enginery = procedureFileUploadService.queryEngineryByWorkId(workId);
        return  new FebsResponse().code("200").data(enginery);
    }





}