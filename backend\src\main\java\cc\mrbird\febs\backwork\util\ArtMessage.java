package cc.mrbird.febs.backwork.util;




import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class ArtMessage {
    public String getHtmlCode() throws UnsupportedEncodingException {
        String htmlCode ="<html>\n"+
                "<head>\n"+
                "<title>"+"mingp" +"</title>\n"
                +"<style>\n"
                +"body {\n" +
                "\t\t\tfont-family: Arial, sans-serif;\n" +
                "\t\t\tbackground-color: #f2f2f2;\n" +
                "\t\t}"+".card {\n" +
                "\t\t\twidth: 340px;\n" +
                "\t\t\tpadding: 20px;\n" +
                "\t\t\tmargin: 50px auto;\n" +
                "\t\t\tbackground-color: #fff;\n" +
                "\t\t\tborder-radius: 8px;\n" +
                "\t\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n" +
                "\t\t}"+".name {\n" +
                "\t\t\tfont-size: 24px;\n" +
                "\t\t\tfont-weight: bold;\n" +
                "\t\t\tcolor: #333;\n" +
                "\t\t\tmargin-bottom: 10px;\n" +
                "\t\t}"+"\n" +
                "\t\t.job-title {\n" +
                "\t\t\tfont-size: 16px;\n" +
                "\t\t\tcolor: #666;\n" +
                "\t\t\tmargin-bottom: 10px;\n" +
                "\t\t}"+".contact {\n" +
                "\t\t\tfont-size: 14px;\n" +
                "\t\t\tcolor: #999;\n" +
                "\t\t\tmargin-bottom: 10px;\n" +
                "\t\t}"
                +"</style>\n"
                +"</head>\n"
                +"<body>\n"
                +"<div class=\"card\">\n" +
                "\t\t<div class=\"name\"></div>\n" +
                "\t\t<div class=\"job-title\"></div>\n" +
                "\t\t<div class=\"contact\">如遇系统问题，可通过以下方式联系</div>\n" +
                "\t\t<div class=\"contact\">邮箱:<EMAIL></div>\n" +
                "\t\t<div class=\"contact\">电话:568-25023->王業桂  5068-26332->李光翔</div>\n" +
                "\t</div>"
                +"</body>\n"
                +"</html>";
        return URLEncoder.encode(htmlCode, String.valueOf(StandardCharsets.UTF_8));
    }
}
