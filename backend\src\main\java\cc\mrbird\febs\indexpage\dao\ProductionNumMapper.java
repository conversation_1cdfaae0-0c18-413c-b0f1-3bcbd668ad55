package cc.mrbird.febs.indexpage.dao;

import cc.mrbird.febs.indexpage.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("one")
public interface ProductionNumMapper {
    List<TDataByCountProductionNum> selectProductionNum(ProductionNum productionNum);

    int insertByTDataContext(TDataContext tDataContext);

    List<TDataContext> selectContext();
    String countByLine(String workId);

    String countByDay(String workId);

    int countByBackwork(String workId);

    int countBackNum(String workId);

    List<PartNumberByIndex> getPartNumber( DateAndWorkIdQo andWorkIdQo);

    List<SumProductivityData> getProductivityData(String pl,String discern);

    boolean updateContext(TDataContext tDataContext );

    List<VisitPageData> selectVisitData(String workId);

    int insertVisitData(VisitPageData visitPageData);

    //根据Time、USER查询136比对表
    List<PageData> selectSheetNameData(SelectSheetByName selectSheetByName);
    //根据Time、USER查询生产日报表
    List<PageData>  selectDataByDay(SelectSheetByName selectSheetByName);
    //根据Time、USER 查询线长交接表
    List<PageData>  selectDataByLine(SelectSheetByName selectSheetByName);
    //根据Time、USER查询计划做线体
    List<PageData> selectDataByPlan(SelectSheetByName selectSheetByName);

    int countBy136(String name);
    int countByDayNew(String workId);
    int countByLineNew(String workId);


}
