package cc.mrbird.febs.wxby.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "T_Equipment_MaintenanceLog")

public class MaintenanceLog1 {
    private static final long serialVersionUID = 1L;
    @TableId(value = "Id",type = IdType.AUTO)
    @ApiModelProperty(value = "Id",required=true,position = 1)
    private Long ID ;
    @ApiModelProperty(value = "系列",required=true,position = 2)
    @TableField(value = "Production")
    private String production;
    @ApiModelProperty(value = "线体",required = true,position = 3)
    @TableField(value = "ProductionLine")
    private String productionLine;
    @ApiModelProperty(value = "填写日期",required = true,position = 4)
    @TableField(value = "RecordDate")
    private String recordDate;
    @TableField(value = "ClassInfo")
    @ApiModelProperty(value = "班别",required = true,position = 5)
    private String classInfo;
    @TableField(value = "EquipmentName")
    @ApiModelProperty(value = "设备名称",required = true,position = 6)
    private String equipmentName;
    @TableField(value = "EquipmentId")
    @ApiModelProperty(value = "设备编号",required = true,position = 7)
    private String equipmentId;
    @TableField(value = "SCLH")
    @ApiModelProperty(value = "生产料号",required = true,position = 8)
    private String sclh;
    @TableField(value = "StartTime")
    @ApiModelProperty(value = "开始时间",required = true,position = 9)
    private String startTime;
    @TableField(value = "EndTime")
    @ApiModelProperty(value = "结束时间",required = true,position = 10)
    private String endTime;
    @TableField(value = "LossTime")
    @ApiModelProperty(value = "损失时间",required = true,position = 11)
    private String lossTime;
    @TableField(value = "MaintenanceNature")
    @ApiModelProperty(value = "维修保养性质",required = true,position =12)
    private String maintenanceNature;
    @TableField(value = "ChangeStation")
    @ApiModelProperty(value = "变更工位",required = true,position = 13)
    private String changeStation;
    @TableField(value = "ChangeNumber")
    @ApiModelProperty(value = "变更编号",required = true,position = 14)
    private String changeNumber;
    @TableField(value = "FaultType")
    @ApiModelProperty(value = "故障类型",required = true,position = 15)
    private String faultType;
    @TableField(value = "FaultCause")
    @ApiModelProperty(value = "故障内容",required = true,position = 16)
    private String faultCause;
    @TableField(value = "CounterPlan")
    @ApiModelProperty(value = "解决对策",required = true,position = 17)
    private String counterPlan;
    @TableField(value = "STATUS")
    @ApiModelProperty(value = "状态",required = true,position = 18)
    private String status;
    @TableField(value = "QH1")
    @ApiModelProperty(value = "线长签核状态",required = true,position = 19)
    private String qh1;       //线长 =>  I 1 true
    @TableField(value = "QH2")
    @ApiModelProperty(value = "责任生技签核状态",required = true,position = 20)
    private String qh2;
    @TableField(value = "QH3")
    @ApiModelProperty(value = "维修審核狀態",required = true,position = 21)
    private String qh3;
    @TableField(value = "QH4")
    @ApiModelProperty(value = "維修核准狀態",required = true,position = 22)
    private String qh4;
    @TableField(value = "FQR")
    @ApiModelProperty(value = "線長名字",required = true,position = 23)
    private String fqr;
    @TableField(value = "ZRSJ")
    @ApiModelProperty(value = "責任生技名字",required = true,position = 24)
    private String zrsj;
    @TableField(value = "WXSH")
    @ApiModelProperty(value = "維修審核名字",required = true,position = 25)
    private String wxsh;
    @TableField(value = "WXHZ")
    @ApiModelProperty(value = "维修核准人",required = true,position = 26)
    private String wxhz;
    @TableField(value = "PGQRR")
    @ApiModelProperty(value = "品管確認人名字",required = true,position = 27)
    private String pgqrr;

}
