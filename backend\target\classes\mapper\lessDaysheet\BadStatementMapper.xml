<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.lessDaysheet.dao.BadStatementMapper">
    <!--新增不良製程報表1-->
    <insert id="insertAll" parameterType="cc.mrbird.febs.lessDaysheet.entity.BadStatement">
        USE Schedule
        INSERT INTO t_data_badStatement(uuid, production, discern, classInfo, checkStatus, recordDate, productionLine,
                                        workStation, partNumber, inputNumber1,
                                        inputNumber2, inputNumber3, inputNumber4, inputNumber5, lossRate1, lossRate2
            , lossRate3, lossRate4, lossRate5, yield1, yield2, yield3, yield4, yield5, productionLineCheckName, qcName)
        VALUES ( #{uuid}, #{production}, #{discern}, #{classInfo}, #{checkStatus}, #{recordDate}, #{productionLine}
               , #{workStation}, #{partNumber}, #{inputNumber1}
               , #{inputNumber2}, #{inputNumber3}, #{inputNumber4}, #{inputNumber5}, #{lossRate1}, #{lossRate2}
               , #{lossRate3}, #{lossRate4}, #{lossRate5}, #{yield1}, #{yield2}, #{yield3}, #{yield4}, #{yield5}
               , #{productionLineCheckName}, #{qcName})
    </insert>
    <!--新增不良製程報表2-->
    <insert id="insertByBadStatementUUID" parameterType="cc.mrbird.febs.lessDaysheet.entity.BadStatementUUID">
        INSERT INTO t_data_badStatement_uuid(uuid, badItem, mo, badNum1, badNum2, badNum3, badNum4, badNum5,
                                             remark,badTotal)VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.uuid}, #{item.badItem}, #{item.mo}, #{item.badNum1}, #{item.badNum2}, #{item.badNum3},
             #{item.badNum4}, #{item.badNum5}, #{item.remark},#{item.badTotal})
        </foreach>
    </insert>
    <update id="updateByStatementOnLineLeader"
            parameterType="cc.mrbird.febs.lessDaysheet.entity.dto.BadStatementUpdateDto">
        USE
        Schedule
        update t_data_badStatement
        <set>
            productionLineCheckName =#{productionLineCheckName}
        </set>
        <where>
            id = #{id}
        </where>
    </update>
    <update id="updateByStatementOnQc" parameterType="cc.mrbird.febs.lessDaysheet.entity.dto.BadStatementQcDto">
        USE
        Schedule
        <foreach collection="collection" item="date">
        update t_data_badStatement
        <set>
            qcName =#{date.qcName}
        </set>
        <where>
            id = #{date.id}
        </where>
        </foreach>
    </update>
    <select id="selectAllByBadStatement" resultType="cc.mrbird.febs.lessDaysheet.entity.vo.BadStatementVo">
        select A.id,
               A.discern,
               A.classInfo,
               A.checkStatus,
               A.production,
               A.productionLine,
               A.workStation,
               A.partNumber,
               A.recordDate,
               B.badItem,
               B.mo,
               B.remark,
               A.inputNumber1,
               A.inputNumber2,
               A.inputNumber3,
               A.inputNumber4,
               A.inputNumber5,
               A.lossRate1,
               A.lossRate2,
               A.lossRate3,
               A.lossRate4,
               A.lossRate5,
               A.yield1,
               A.yield2,
               A.yield3,
               A.yield4,
               A.yield5,
               A.productionLineCheckName,
               A.qcName,
               B.badNum1,
               B.badNum2,
               B.badNum3,
               B.badNum4,
               B.badNum5,
               B.badTotal
        from t_data_badStatement AS A
                 INNER JOIN t_data_badStatement_uuid AS B
                            ON A.uuid = B.uuid
        WHERE A.uuid = #{uuid}
        ORDER BY A.id DESC
    </select>
    <select id="selectBadStatementVo" resultType="cc.mrbird.febs.lessDaysheet.entity.vo.BadStatementSelectVo">
        USE
        Schedule
        select uuid, workStation, partNumber, recordDate, productionLine,productionLineCheckName,qcName,id
        from t_data_badStatement
        <where>
            <if test="production != null and production != ''">
                AND production = #{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND productionLine = #{productionLine}
            </if>
            <if test="classInfo != null and classInfo != ''">
                AND classInfo = #{classInfo}
            </if>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND recordDate >= #{startDate}
                AND recordDate &lt;= #{endDate}
            </if>
        </where>
    </select>
    <select id="selectBadItem" resultType="cc.mrbird.febs.lessDaysheet.entity.BadStatementUUID">
        USE
        Schedule
        select badItem,
               mo,
               badNum1,
               badNum2,
               badNum3,
               badNum4,
               badNum5,
               remark
        from t_data_badStatement_uuid
        WHERE uuid = #{uuid}
    </select>

    <select id="getRole" resultType="int">

        SELECT case WHEN RoleName  =N'線長'
                        then 0
                    WHEN RoleName =N'品管確認人' then 1
                    else 2 END  AS role

        FROM T_Data_Group   WHERE CardId=#{workId}
    </select>

    <select id="selectQCName" resultType="java.lang.String">
        SELECT id FROM t_data_badStatement WHERE id =#{id} AND qcName IS NULL
    </select>

    <update id="badStatementByEdit">
        USE Schedule
        update t_data_badStatement set
            inputNumber1=#{inputNumber1},inputNumber2=#{inputNumber2}
            ,inputNumber3=#{inputNumber3}
            ,inputNumber4=#{inputNumber4}
            ,inputNumber5=#{inputNumber5}
            ,lossRate1=#{lossRate1}
            ,lossRate2=#{lossRate2}
            ,lossRate3=#{lossRate3}
            ,lossRate4=#{lossRate4}
            ,lossRate5=#{lossRate5}
            ,yield1=#{yield1}
            ,yield2=#{yield2}
            ,yield3=#{yield3}
            ,yield4=#{yield4}
            ,yield5=#{yield5}

       where
           uuid =#{uuid}
    </update>

    <update id="badStatementUUIDByEdit">
       USE Schedule
           update t_data_badStatement_uuid
               set
        badItem=#{badItem}
      ,mo=#{mo}
      ,badNum1=#{badNum1}
      ,badNum2=#{badNum2}
      ,badNum3=#{badNum3}
      ,badNum4=#{badNum4}
      ,badNum5=#{badNum5}
      ,remark=#{remark}
       ,badTotal=#{badTotal}

        where
            uuid=#{uuid} and badItem=#{badItem}

    </update>

    <delete id="delByBadStatementUUID" >
        USE Schedule
        delete t_data_badStatement_uuid WHERE uuid=#{uuid}
    </delete>
    <!--查詢所有不良製程報表-->
</mapper>