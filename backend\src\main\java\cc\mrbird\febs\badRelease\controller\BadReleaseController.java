package cc.mrbird.febs.badRelease.controller;


import cc.mrbird.febs.badRelease.entity.qo.ConditionUseingQueryQo;
import cc.mrbird.febs.badRelease.entity.vo.BadReleaseDetailVo;
import cc.mrbird.febs.badRelease.entity.vo.BadReleaseInfoVo;
import cc.mrbird.febs.badRelease.service.BadReleaseService;
import cc.mrbird.febs.common.domain.FebsResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("badRelease")
@Api(tags = "不良釋放")
@Slf4j
public class BadReleaseController {

    @Autowired
    private BadReleaseService badReleaseService;

    /**
     * 查詢產品處
     * @param workId
     * @return
     */
    @GetMapping("/querySBU")
    @ApiOperation("查詢產品處")
    public FebsResponse querySBU (@RequestParam("workId") String workId){
        String SBU = badReleaseService.querySBU(workId);
        return new FebsResponse().code("200").message("查詢成功").data(SBU);
    }

    /**
     * 查詢機能
     * @param workId
     * @return
     */
    @GetMapping("/queryFunction")
    @ApiOperation("查詢機能")
    public FebsResponse queryFunction(@RequestParam("workId") String workId){
        String section = badReleaseService.queryFunction(workId);
        return new FebsResponse().code("200").message("查詢成功").data(section);
    }

    /**
     * 查詢課別
     * @return
     */
    @GetMapping("/querySection")
    @ApiOperation("查詢課別")
    public FebsResponse querySection(){
        List<String> section = badReleaseService.querySection();
        return new FebsResponse().code("200").message("查詢成功").data(section);
    }

    /**
     * 查詢線體
     * @return
     */
    @GetMapping("/queryLine")
    @ApiOperation("查詢線體")
    public FebsResponse queryLine(){
        List<String> lines = badReleaseService.queryLine();
        return new FebsResponse().code("200").message("查詢成功").data(lines);
    }

    /**
     * 查詢料號
     * @return
     */
    @GetMapping("/queryMaterialNumber")
    @ApiOperation("查詢料號")
    public FebsResponse queryMaterialNumber(){
        List<String> materialNumbers = badReleaseService.queryMaterialNumber();
        return new FebsResponse().code("200").message("查詢成功").data(materialNumbers);
    }

    /**
     * 查詢不良釋放日期
     * @return
     */
    @GetMapping("/queryBadReleaseDate")
    @ApiOperation("查詢不良釋放日期")
    public FebsResponse queryBadReleaseDate(){
        List<String> badReleaseDate = badReleaseService.queryBadReleaseDate();
        return new FebsResponse().code("200").message("查詢成功").data(badReleaseDate);
    }

    /**
     * 條件查詢
     * 條件為：課別、線體、料號、不良釋放日期
     * @param conditionUseingQuery
     * @return
     */
    @PostMapping("/queryByCondition")
    @ApiOperation("條件查詢，條件為：課別、線體、料號、不良釋放日期")
    public FebsResponse queryCondition(@RequestBody ConditionUseingQueryQo conditionUseingQuery){
        List<BadReleaseInfoVo> badReleaseInfos = badReleaseService.queryCondition(conditionUseingQuery);
        return new FebsResponse().code("200").message("查詢成功").data(badReleaseInfos);
    }

    /**
     * 詳情展示
     * id查詢
     * @param id
     * @return
     */
    @PostMapping("/queryById")
    @ApiOperation("詳情頁面展示，id查詢")
    public FebsResponse queryById(@RequestParam("id") Integer id){
        BadReleaseDetailVo badReleaseDetailVos = badReleaseService.queryById(id);
        return new FebsResponse().code("200").message("查詢成功").data(badReleaseDetailVos);
    }
}
