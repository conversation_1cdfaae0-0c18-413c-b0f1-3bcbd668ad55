package cc.mrbird.febs.rdProject.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import cc.mrbird.febs.rdProject.entity.RdProjectTeamUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的團隊與個人的映射數據訪問接口
 */
@DS("primary")
public interface RdProjectTeamUserMapper {

    // 插入團隊用戶映射關係
    int insertRdProjectTeamUser(RdProjectTeamUser rdProjectTeamUser);

    // 根據工號和團隊ID刪除團隊用戶映射關係
    int deleteRdProjectTeamUser(@Param("workerId") String workerId, @Param("teamId") Long teamId);

    // 根據工號刪除所有團隊用戶映射關係
    int deleteRdProjectTeamUserByWorkerId(@Param("workerId") String workerId);

    // 根據團隊ID刪除所有團隊用戶映射關係
    int deleteRdProjectTeamUserByTeamId(@Param("teamId") Long teamId);

    // 更新團隊用戶映射關係（實際上這個表沒有其他字段可以更新，主要用於存在性檢查）
    int updateRdProjectTeamUser(RdProjectTeamUser rdProjectTeamUser);

    // 根據工號和團隊ID查詢團隊用戶映射關係
    RdProjectTeamUser selectRdProjectTeamUser(@Param("workerId") String workerId, @Param("teamId") Long teamId);

    // 根據工號查詢該用戶所屬的所有團隊
    List<RdProjectTeamUser> selectRdProjectTeamUserByWorkerId(@Param("workerId") String workerId);

    // 根據團隊ID查詢該團隊的所有用戶
    List<RdProjectTeamUser> selectRdProjectTeamUserByTeamId(@Param("teamId") Long teamId);

    // 查詢所有團隊用戶映射關係
    List<RdProjectTeamUser> selectAllRdProjectTeamUser();
}
