package cc.mrbird.febs.agv.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AGVTaskList {
    @ApiModelProperty(value = "任务ID", position = 0)
    private Integer id;
    @ApiModelProperty(value = "机器人ID", position = 1)
    private String robotId;
    @ApiModelProperty(value = "任务创建者姓名", position = 2)
    private String taskCreateUserName;
    @ApiModelProperty(value = "任务创建者工号", position = 3)
    private String taskCreateUserId;
    @ApiModelProperty(value = "任务创建日期", position = 4)
    private String taskCreateDate;
    @ApiModelProperty(value = "起始站点ID", position = 5)
    private String slSiteId;
    @ApiModelProperty(value = "起始站点名称", position = 6)
    private String slSiteName;
    @ApiModelProperty(value = "目标站点ID", position = 7)
    private String xlSiteId;
    @ApiModelProperty(value = "目标站点名称", position = 8)
    private String xlSiteName;
    @ApiModelProperty(value = "任务状态", position = 9)
    private String taskStatus;
    @ApiModelProperty(value = "返回信息", position = 10)
    private String returnInfo;
    @ApiModelProperty(value = "状态", position = 11)
    private Integer status;
}