<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.indexpage.dao.CommentSectionMapper">
    <insert id="insertDataByTComment">
        USE IDS_BK_PaperLessSystem
        INSERT INTO t_data_comment(content, userName, parentId, lever, createdAt,isSort)
        VALUES (#{content,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER},
                #{lever,jdbcType=INTEGER}, #{createdAt,jdbcType=VARCHAR},#{isSort,jdbcType=INTEGER})
    </insert>

    <insert id="insertDataByUsers">
        USE IDS_BK_PaperLessSystem
        INSERT INTO t_data_users(userName, avatar)
        VALUES (#{userName}, #{avatar})
    </insert>

    <select id="getRepliesByParentIdAndLevel" resultType="cc.mrbird.febs.indexpage.entity.TDataComment">
        USE IDS_BK_PaperLessSystem
        SELECT *
        FROM t_data_comment
        WHERE parentId = #{param1}
          AND lever = #{param2}
    </select>

    <select id="getAllCommentData" resultType="cc.mrbird.febs.indexpage.entity.TDataCommentAndUsers">
        USE IDS_BK_PaperLessSystem
        SELECT distinct c1.id AS id,
                        c1.content AS content,
                        c1.userName AS userName,
                        c1.parentId AS parentId,
                        c1.lever AS lever,
                        c1.createdAt AS createdAt,
                        u.avatar AS avatar,
                        u.workId AS workId,
                        c1.isSort
        FROM t_data_comment c1
                 LEFT JOIN t_data_users u ON c1.userName = u.userName
                 LEFT JOIN t_data_comment c2 ON c1.parentId = c2.id
        ORDER BY isSort DESC, parentId, c1.createdAt DESC;
    </select>

    <select id="setSort" >
        USE IDS_BK_PaperLessSystem
        update  t_data_comment set isSort=1  WHERE id=#{id}
    </select>

    <update id="updateBySort">
        USE IDS_BK_PaperLessSystem
        update  t_data_comment set isSort=''  WHERE isSort='1'
    </update>

    <select id="selectSortId" resultType="int">
        USE IDS_BK_PaperLessSystem
        select id from t_data_comment WHERE isSort=1
    </select>
</mapper>