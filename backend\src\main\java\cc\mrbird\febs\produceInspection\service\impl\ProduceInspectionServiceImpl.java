package cc.mrbird.febs.produceInspection.service.impl;

import cc.mrbird.febs.common.utils.ExcelUtils;
import cc.mrbird.febs.produceInspection.dao.ProduceInspectionMapper;
import cc.mrbird.febs.produceInspection.entity.*;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionInspectProjectsQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionMainInfoQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionPartNumberQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionUserInfoQueryParam;
import cc.mrbird.febs.produceInspection.service.ProduceInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/19
 * @Time: 10:04
 */
@Service
public class ProduceInspectionServiceImpl implements ProduceInspectionService {

    private final ProduceInspectionMapper mapper;

    @Autowired
    public ProduceInspectionServiceImpl(ProduceInspectionMapper emapper) {
        this.mapper = emapper;
    }

    @Override
    public List<ProduceInspectionUserInfo> queryByUserId(String userID) {
        return mapper.queryByUserId(userID);
    }

    @Override
    public List<String> querySBU() {
        return mapper.querySBU();
    }

    @Override
    public List<String> queryFunctions(String sbu) {
        return mapper.queryFunctions(sbu);
    }

    @Override
    public List<String> queryDiscern(String sbu, String functions) {
        return mapper.queryDiscern(sbu, functions);
    }

    @Override
    public List<String> queryProduction(String sbu, String functions, String discern) {
        return mapper.queryProduction(sbu, functions, discern);
    }

    @Override
    public List<String> queryProductionLine(String sbu, String functions, String discern, String production) {
        return mapper.queryProductionLine(sbu, functions, discern, production);
    }

    @Override
    public ProduceInspectionNotInspect queryIsNotDJ(
            ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam) {
        return mapper.queryIsNotDJ(produceInspectionMainInfoQueryParam);
    }

    @Override
    public List<ProduceInspectionMainInfo> queryProductionInspectionMainInfo(
            ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam) {
        return mapper.queryProductionInspectionMainInfo(produceInspectionMainInfoQueryParam);
    }

    @Override
    public List<ProduceInspectionInspectProjects> queryProductionInspectionProjectData(
            ProduceInspectionInspectProjectsQueryParam produceInspectionInspectProjectsQueryParam) {
        return mapper.queryProductionInspectionProjectData(produceInspectionInspectProjectsQueryParam);
    }

    @Override
    public List<ProduceInspectionPartNumber> queryProduceInspectionPartNumberData(
            ProduceInspectionPartNumberQueryParam produceInspectionPartNumberQueryParam) {
        return mapper.queryProduceInspectionPartNumberData(produceInspectionPartNumberQueryParam);
    }

    @Override
    public List<ProduceInspectionUserInfo> queryProduceInspectionUserInfoData(
            ProduceInspectionUserInfoQueryParam produceInspectionUserInfoQueryParam) {
        return mapper.queryProduceInspectionUserInfoData(produceInspectionUserInfoQueryParam);
    }

    /**
     * 导入人员信息
     */
    public String importPersonnelData(MultipartFile file) throws IOException {
        List<ProduceInspectionUserInfo> personnelList = ExcelUtils.readExcel(file, ProduceInspectionUserInfo.class);
        int count = 0;
        for (ProduceInspectionUserInfo personnel : personnelList) {
            String type = personnel.getType();
            //人員類型 1：點檢人 2：確認人
            if("點檢人".equals(type)||"點檢".equals(type)){
                personnel.setType("1");
            }else if("確認人".equals(type)||"確認".equals(type)){
                personnel.setType("2");
            }
            count += this.mapper.insertPersonnelData(personnel);
        }
        return "成功导入" + count + "条人员信息";
    }

    /**
     * 导入自主点检项目
     */
    public String importInspectionItems(MultipartFile file) throws IOException {
        List<ProduceInspectionInspectProjects> itemList = ExcelUtils.readExcel(file,
                ProduceInspectionInspectProjects.class);
        int count = 0;
        int invalidCount = 0;
        StringBuilder invalidItems = new StringBuilder();

        for (ProduceInspectionInspectProjects item : itemList) {
            // 验证frequence字段，只允许"班/次"或"2H/次"
            String frequence = item.getFrequence();
            if (frequence != null && !"次/班".equals(frequence) && !"2H/次".equals(frequence)
            && !"4H/次".equals(frequence)&& !"周/次".equals(frequence)&& !"物料上料時".equals(frequence)) {
                invalidCount++;
                // 记录无效的项目信息，仅返回无效频率信息
                //append("[SBU:").append(item.getSbu())
                //.append(", 機能:").append(item.getFunctions())
                //.append(", 課別:").append(item.getSection())
                invalidItems.append("[頻率:").append(frequence)
                          .append("] ");
                continue; // 跳过此项目，不插入数据库
            }

            count += this.mapper.insertInspectionItems(item);
        }

        String result = "成功导入" + count + "条点检项目";
        if (invalidCount > 0) {
            result += "，有" + invalidCount + "条记录因頻率值不符合要求（只允许'次/班'或'2H/次'或'4H/次'或'周/次'或'物料上料時'）而未导入: " + invalidItems.toString();
        }
        return result;
    }

    /**
     * 导入料号信息
     */
    public String importMaterialInfo(MultipartFile file) throws IOException {
        List<ProduceInspectionPartNumber> materialList = ExcelUtils.readExcel(file, ProduceInspectionPartNumber.class);
        int count = 0;
        for (ProduceInspectionPartNumber material : materialList) {
            count += this.mapper.insertPartNumber(material);
        }
        return "成功导入" + count + "条料号信息";
    }

    @Override
    public int updatePartNumber(ProduceInspectionPartNumber partNumber) {
        return mapper.updatePartNumber(partNumber);
    }

    @Override
    public int deletePartNumber(Integer id) {
        return mapper.deletePartNumber(id);
    }

    @Override
    public int updateUserInfo(ProduceInspectionUserInfo userInfo) {
        return mapper.updateUserInfo(userInfo);
    }

    @Override
    public int deleteUserInfo(Integer id) {
        return mapper.deleteUserInfo(id);
    }

    @Override
    public int updateInspectProject(ProduceInspectionInspectProjects project) {
        return mapper.updateInspectProject(project);
    }

    @Override
    public int deleteInspectProject(Integer id) {
        return mapper.deleteInspectProject(id);
    }

    @Override
    public List<ProduceInspectionPicUrl> queryPicsByMainInfoId(String mainInfoId) {
        return mapper.queryPicsByMainInfoId(mainInfoId);
    }

    @Override
    public List<String> queryWorkStation(String sbu, String functions, String discern, String production, String line) {
        return mapper.queryWorkStation(sbu,functions,discern,production,line);
    }
}
