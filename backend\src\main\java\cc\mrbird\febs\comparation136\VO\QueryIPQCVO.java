package cc.mrbird.febs.comparation136.VO;

import cc.mrbird.febs.comparation136.entity.QueryIPQC;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryIPQCVO implements Serializable {

   private String QHRName;

   private String QHRWorkId;
}
