package cc.mrbird.febs.comparison.dao;

import cc.mrbird.febs.comparison.entity.*;
import cc.mrbird.febs.wxby.entity.GroupEq;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Mapper
@DS("mssql")
public interface TDataComparisonMapper {
    //136比對錶查詢頁面  查詢結果
    List<TDataComparison> selectComparison(QueryDto queryDto);
    //136比對錶彈出框插入結果
    boolean insertComparison(InsertDto insertDto);
    //136比對錶只查詢當天新建的數據結果
    List<TDataComparison> selectComparisonByGetDate(String production,String productionLine,String pl);
    //136比對錶組長簽核
    boolean updateByGroupLeader(@Param("groupLeader") List<GroupLeader> groupLeaders);
    //136比對錶查詢有異常狀態的數據
    List<TDataComparison> selectComparisonByAbnormal(@RequestBody SelectAbnormalDto selectAbnormalDto);

    List<String> selectAchievementRate(String production,String productionLine,String recordDate,String industry,String classInfo);

    boolean delete(Integer id);


    List<String> selectPartNumberByPL(PartNumberDto partNumberDto);
}
