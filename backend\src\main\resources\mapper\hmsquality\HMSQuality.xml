<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.hmsquality.dao.HMSQualityMapper">


    <select id="selectHMSQualityData" resultType="cc.mrbird.febs.hmsquality.entity.dto.HMSQualityDto">
        SELECT
        [id]              as id,
        [series]          as production,
        [line]            as productionLine,
        [date]                 ,
        [DC_DateCode]            as dateCode,
        [CPLH_PFN]            as pfn,
        [userID]            ,
        [userName]          ,
        [ins_date] as insDate
        FROM [APPGCL_Process].[dbo].[HMS_PZJL]
        <where>
            <if test="userID != null and userID != ''">
                AND userID = #{userID}
            </if>
            <if test="production != null and production != ''">
                AND series = #{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND line = #{productionLine}
            </if>
            <if test="dateCode != null and dateCode != ''">
                AND DC_DateCode = #{dateCode}
            </if>
            <if test="pfn != null and pfn != ''">
                AND CPLH_PFN=#{pfn}
            </if>
            <if test="startDate != null and startDate != ''">
                AND  DateDIFF(day,date,#{startDate}) &lt;=0
            </if>
            <if test="endDate != null and endDate != ''">
                AND  DateDIFF(day,date,#{endDate}) >=0
            </if>
        </where>
    </select>

    <select id="selectHmsPics" resultType="cc.mrbird.febs.hmsquality.entity.HmsPicture">
        SELECT
        [PicUrl]          as picUrl,
        [PicName]            as picName,
        [PicOrder]            as picOrder
        FROM [APPGCL_Process].[dbo].[HMS_PicUrl]
        <where>
            <if test="pzID != null and pzID != ''">
                AND PzID = #{pzID}
            </if>
        </where>
    </select>
    <select id="selectHmsPicsByIds" resultType="cc.mrbird.febs.hmsquality.entity.HmsPicture">
        SELECT
        [PicUrl]          as picUrl,
        [PicName]            as picName,
        [PicOrder]            as picOrder,
        [PzID]            as pzId
        FROM [APPGCL_Process].[dbo].[HMS_PicUrl]
        WHERE PzID IN
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by PicOrder
    </select>
    <select id="selectProduction" resultType="java.lang.String">
        SELECT DISTINCT  series
        FROM
            [APPGCL_Process].[dbo].[HMS_PZJL]
    </select>
    <select id="selectProductionLine" resultType="java.lang.String">
        SELECT DISTINCT  line
        FROM
            [APPGCL_Process].[dbo].[HMS_PZJL]
        WHERE series=#{production}
    </select>
    <select id="selectDC" resultType="java.lang.String">
        SELECT DISTINCT  DC_DateCode
        FROM
            [APPGCL_Process].[dbo].[HMS_PZJL]
        WHERE series=#{production} AND line=#{productionLine}
    </select>
    <select id="selectPFN" resultType="java.lang.String">
        SELECT DISTINCT  CPLH_PFN
        FROM
            [APPGCL_Process].[dbo].[HMS_PZJL]
        WHERE series=#{production} AND line=#{productionLine} AND DC_DateCode=#{dateCode}
    </select>
    <select id="selectUser" resultType="java.lang.String">
        SELECT DISTINCT  userID+'('+userName+')'
        FROM
            [APPGCL_Process].[dbo].[HMS_PZJL]
        WHERE series=#{production} AND line=#{productionLine} AND DC_DateCode=#{dateCode} AND CPLH_PFN=#{pfn}
    </select>
</mapper>