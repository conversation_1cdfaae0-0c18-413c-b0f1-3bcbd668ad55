package cc.mrbird.febs.line.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 *
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("T_Configure_ClassKPI")
public class TConfigureClasskpi implements Serializable {

    private static final long serialVersionUID = 1L;
    private String id;

    /**
     * 计划产量
     */
    @ApiModelProperty(value = "计划产量",required = true)
    private BigDecimal planOutput;

    /**
     * 实际产量
     */
    @ApiModelProperty(value = "实际产量",required = true)
    private BigDecimal realOutput;

    /**
     * 产品料号
     */
    @ApiModelProperty(value = "产品料号",required = true)
    private String productQuantity;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称",required = true)
    private String customerName;

    /**
     * 客户料号
     */
    @ApiModelProperty(value = "客户料号",required = true)
    private String customerQuantity;

    /**
     * 工单
     */
    @ApiModelProperty(value = "工单",required = true)
    private String ticket;

    /**
     * D/C
     */
    @ApiModelProperty(value = "D/C",required = true)
    private String dc;

    /**
     * 入库单号
     */
    @ApiModelProperty(value = "入库单号",required = true)
    private String inboundOrderNumber;

        @TableField("Person")
    private String person;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期",required = true)
        @TableField("DateTime")
    private String dateTime;


}
