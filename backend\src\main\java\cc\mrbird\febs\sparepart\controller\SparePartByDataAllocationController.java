package cc.mrbird.febs.sparepart.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.sparepart.dao.SparePartByDataMapper;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndSparePart;
import cc.mrbird.febs.sparepart.entity.dto.TConfigurePersonAndEquipment;
import cc.mrbird.febs.sparepart.entity.qo.SparePartAndEquipmentQo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "備品管控-數據維護")
@ResponseBody
@Slf4j
@RequestMapping("/SparePartByDataAllocation")
public class SparePartByDataAllocationController {
    private final SparePartByDataMapper sparePartByDataMapper;



    public SparePartByDataAllocationController(SparePartByDataMapper sparePartByDataMapper) {
        this.sparePartByDataMapper = sparePartByDataMapper;
    }

    //獲取IDS1
    @PostMapping("/queryPl")
    @ApiOperation(value = "獲取pl 例: IDS1 IDS2 IDS3")
    public FebsResponse queryPl() {
        List<String> list = sparePartByDataMapper.queryPl();
        return new FebsResponse().code("200").data(list);
    }

    //根據 IDS 獲取課別

    @GetMapping("/queryDiscern")
    @ApiOperation(value = "根據IDS 獲取課別  例： 裝配一課 裝配二課")
    public FebsResponse queryDiscern(@RequestParam("pl") String pl) {
        List<String> list = sparePartByDataMapper.queryDiscern(pl);
        return new FebsResponse().code("200").data(list);
    }

    //根據 IDS 課別 獲取 系列
    @PostMapping("/queryProduction")
    @ApiOperation(value = "根据事業處、课别获取系列",notes = "參數只需要傳 pl、 discern")
   public FebsResponse queryProduction(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentQo){
        List<String> list =sparePartByDataMapper.queryProduction(sparePartAndEquipmentQo);
        return  new FebsResponse().code("200").data(list);
   }



    //根據 IDS 課別 系列 獲取 線體
    @PostMapping("/queryProductionLine")
    @ApiOperation(value = "根據 pl 課別 系列獲取線體，參數只需要傳：pl,discern,production")
    public FebsResponse queryProductionLine(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentVo) {
        List<String> list = sparePartByDataMapper.queryProductionLine(sparePartAndEquipmentVo);
        return new FebsResponse().code("200").data(list);
    }

    //根據 IDS 課別 獲取 備件代碼
    @PostMapping("/querySparePartCode")
    @ApiOperation(value = "根據 pl 課別 獲取備品代碼，參數只需要傳: pl,discern")
    public FebsResponse querySparePartCode(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentVo) {
        List<String> list = sparePartByDataMapper.querySparePArtCode(sparePartAndEquipmentVo);
        return new FebsResponse().code("200").data(list);
    }


    @PostMapping("/queryBySharePartAndEquipment")
    @ApiOperation(value = "備品管控數據維護-查詢壽命管控設備關係")
    public FebsResponse queryBySharePartAndEquipment(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentVo) {

        List<TConfigureEquipmentAndSparePart> list = sparePartByDataMapper.queryBySharePartAndEquipment(sparePartAndEquipmentVo);

        return new FebsResponse().code("200").data(list);

    }

    @PostMapping("/queryByPersonAndEquipment")
    @ApiOperation(value = "備品管控數據維護-查詢壽命管控維護")
    public FebsResponse queryBySharePartByPersonAndEquipment(@RequestBody SparePartAndEquipmentQo sparePartAndEquipmentVo) {
        System.out.println(sparePartAndEquipmentVo);
        List<TConfigurePersonAndEquipment> list = sparePartByDataMapper.queryByPersonAndEquipment(sparePartAndEquipmentVo);

        return new FebsResponse().code("200").data(list);

    }

    @PostMapping("/upDataBySparePartAndEquipment")
    @ApiOperation(value = "设备与备件关系-修改单条数据", notes = "根据ID修改单条记录，ID必须传")
    public FebsResponse upDataBySparePartAndEquipment(@RequestBody TConfigureEquipmentAndSparePart tConfigureEquipmentAndSparePart) {
        int i = sparePartByDataMapper.upDataBySparePartAndEquipment(tConfigureEquipmentAndSparePart);
        try{

                return new FebsResponse().code("200").message("修改成功"+"修改的数据条数为："+i);

        }catch (Exception ex){
            return new FebsResponse().code("500").message("修改失败,传的参数值为:" + tConfigureEquipmentAndSparePart);
        }

    }

    @PostMapping("/upDtaByPersonAndEquipment")
    @ApiOperation(value = "备品寿命管控与人员关系-修改数据")
    public FebsResponse upDtaByPersonAndEquipment(@RequestBody TConfigurePersonAndEquipment tConfigurePersonAndEquipment){
        int i =sparePartByDataMapper.upDataByPersonAndEquipment(tConfigurePersonAndEquipment);
        if (i==1||i==0){
            return  new FebsResponse().code("200").message("修改成功");
        }else {
            return  new FebsResponse().code("500").message("修改失败,传的参数值为:"+tConfigurePersonAndEquipment);
        }

    }

    //设备、备品Excel导入
    @PostMapping("/importExcelBySparePart")
    @ApiOperation(value = "设备、备品关系Excel导入",notes = "参数值为Excel文件")
    public FebsResponse importExcelBySparePart (@RequestParam("file") MultipartFile file){
        if (file.isEmpty()) {
            return new FebsResponse().code("500").message("没有可以上传的文件，是1KB也没有，没有");
        }
        if (!isExcelFile(file)) {
            return new FebsResponse().code("500").message("这个文件不是Excel文件,不是");
        }
        try {
            List<TConfigureEquipmentAndSparePart> products = parseExcelFile(file);
            saveToDatabase(products);

        } catch (Exception ex) {
            return  new FebsResponse().code("500").message("导入失败"+ex.getMessage());
        }

        return  new FebsResponse().code("200").message("导入成功");
    }
    //备品、人员关系导入
    @PostMapping("/importExcelBySparePartAndPerson")
    @ApiOperation(value = "人员关系维护-Excel导入",notes = "参数值为excel文件")
    public FebsResponse importExcelBySparePartAndPerson(@RequestParam("file") MultipartFile file){
        if (file.isEmpty()) {
            return new FebsResponse().code("500").message("没有可以上传的文件，是1KB也没有，没有");
        }
        if (!isExcelFile(file)) {
            return new FebsResponse().code("500").message("这个文件不是Excel文件,不是");
        }
        try {
            List<TConfigurePersonAndEquipment> products = parseExcelFileByPersonAndEquipment(file);
            saveToDatabaseByPersonAndEquipment(products);
        } catch (Exception ex) {
            return  new FebsResponse().code("500").message("导入失败"+ ex);
        }

        return  new FebsResponse().code("200").message("导入成功");
    }
    //设备、备品表模板下载
    @GetMapping("/downloadExcelBySparePartAndEquipment")
    @CrossOrigin(origins = "*")
    @ApiOperation(value = "设备、备品关系Excel模板下载")
    public FebsResponse downloadExcelBySparePartAndEquipment(HttpServletResponse response){
        String filePath ="D:\\Fit\\muban\\111.xls";
        try {
            URLDecoder.decode(filePath, "UTF-8");
            File file = new File(filePath);
            String fileName = file.getName();
            String ext = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            FileInputStream fis = new FileInputStream(file);
            InputStream is = new BufferedInputStream(fis);
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            is.close();
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode(fileName, "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            response.addHeader("content-Length", "" + file.length());
            response.setHeader("Access-Control-Allow-Origin", "*");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return  new FebsResponse().code("200").message("下载成功");
    }

    //备品、人员模板下载
    @GetMapping("/downloadExcelByPersonAndSparePart")
    @CrossOrigin(origins = "*")
    @ApiOperation(value = "备品和人员关系Excel模板下载")
    public FebsResponse downloadExcelByPersonAndSparePart(HttpServletResponse response){
        String filePath ="D:\\Fit\\muban\\222.xls";
        try {
            URLDecoder.decode(filePath, "UTF-8");
            File file = new File(filePath);
            String fileName = file.getName();
            String ext = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            FileInputStream fis = new FileInputStream(file);
            InputStream is = new BufferedInputStream(fis);
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            is.close();
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode(fileName, "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            response.addHeader("content-Length", "" + file.length());
            response.setHeader("Access-Control-Allow-Origin", "*");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return  new FebsResponse().code("200").message("下载成功");
    }



    //设备编号对应表下载
    @GetMapping("/downloadExcelByEquipmentId")
    @CrossOrigin(origins = "*")
    @ApiOperation(value = "EQID对照Excel模板下载")
    public FebsResponse downloadExcelByEquipmentId(HttpServletResponse response){
        String filePath ="D:\\Fit\\muban\\333.xls";
        try {
            URLDecoder.decode(filePath, "UTF-8");
            File file = new File(filePath);
            String fileName = file.getName();
            String ext = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            FileInputStream fis = new FileInputStream(file);
            InputStream is = new BufferedInputStream(fis);
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            is.close();
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode(fileName, "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            response.addHeader("content-Length", "" + file.length());
            response.setHeader("Access-Control-Allow-Origin", "*");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return  new FebsResponse().code("200").message("下载成功");
    }

    @PostMapping("/getDataExcelExport")
    @CrossOrigin(origins = "*")
    public FebsResponse getDataExcelExport(@RequestBody List<Map<String, Object>> map,HttpServletResponse response) throws IOException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        String sheetName ="excel"  + dateFormat.format(new Date());
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet =workbook.createSheet("sheet1");
        // Create header row
        Row header = sheet.createRow(0);
        int headerCellIndex = 0;
        for (String key : map.get(0).keySet()) {
            Cell cell = header.createCell(headerCellIndex++);
            cell.setCellValue(key);
        }

        int rowIndex = 1;
        for (Map<String, Object> rowMap : map) {
            Row row = sheet.createRow(rowIndex++);
            int cellIndex = 0;
            for (Object value : rowMap.values()) {
                Cell cell = row.createCell(cellIndex++);
                if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Integer) {
                    cell.setCellValue((Integer) value);
                }

            }
        }
        URLEncoder.encode(sheetName,"utf-8");
        // Write the workbook to response
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename="+new String(sheetName.getBytes())+".xls");

        // Flush the file to the output stream
        try {
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (IOException e) {
            e.printStackTrace();
        }

        // Flush the response
        response.flushBuffer();
        return new FebsResponse().code("200").message("下載成功");
    }


    //根据模板导入Excel01
    private boolean isExcelFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType.equals("application/vnd.ms-excel") ||
                contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                contentType.equals("text/csv");
    }

    private List<TConfigureEquipmentAndSparePart> parseExcelFile(MultipartFile file) throws IOException, InvalidFormatException {
        List<TConfigureEquipmentAndSparePart> equipmentAndSpareParts = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            if(row.getRowNum() == 0) continue;
            String pl = getValueFromCell(row.getCell(0));
            String enginery = getValueFromCell(row.getCell(1));
            String discern = getValueFromCell(row.getCell(2));
            String production=getValueFromCell(row.getCell(3));
            String productionLine = getValueFromCell(row.getCell(4));
            String equipmentName = getValueFromCell(row.getCell(5));
            String equipmentId = getValueFromCell(row.getCell(6));
            String sparePartName = getValueFromCell(row.getCell(7));
            String lifeLimit=getValueFromCell(row.getCell(8));
            String sparePartId = getValueFromCell(row.getCell(9));
            equipmentAndSpareParts.add(new TConfigureEquipmentAndSparePart(pl, enginery, discern,production, productionLine, equipmentName, equipmentId, sparePartName, sparePartId,lifeLimit));
        }
        return equipmentAndSpareParts;
    }

    private void saveToDatabase(List<TConfigureEquipmentAndSparePart> listOfObjects) throws Exception {
        for (TConfigureEquipmentAndSparePart object : listOfObjects) {
            int isDuplicate =sparePartByDataMapper.existsWithSameFields(object);
            if (isDuplicate>=1) {
                throw new Exception("数据库已存在相同的数据，请勿添加，谢谢!"+object);
            }else {
                sparePartByDataMapper.insertByParePartAndEquipment(object);
            }

        }
    }
    private String getValueFromCell(Cell cell) {
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((int)numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            default:
                return "";
        }
    }
    //懒得整合   表02导入模板
    private List<TConfigurePersonAndEquipment> parseExcelFileByPersonAndEquipment(MultipartFile file) throws IOException, InvalidFormatException {
        List<TConfigurePersonAndEquipment> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            if(row.getRowNum() == 0) continue;
            String pl = getValueFromCell(row.getCell(0));
            String enginery = getValueFromCell(row.getCell(1));
            String discern = getValueFromCell(row.getCell(2));
            String sparePartName = getValueFromCell(row.getCell(3));
            String sparePartCode = getValueFromCell(row.getCell(4));
            String personInChargeNameBy01 = getValueFromCell(row.getCell(5));
            String personInChargeWorkIdBy01 = getValueFromCell(row.getCell(6));
            String personInChargeEmailBy01 = getValueFromCell(row.getCell(7));
            String personInChargeNameBy02 = getValueFromCell(row.getCell(8));
            String personInChargeWorkIdBy02 = getValueFromCell(row.getCell(9));
            String personInChargeEmailBy02 = getValueFromCell(row.getCell(10));
            String personInChargeNameByMax = getValueFromCell(row.getCell(11));
            String personInChargeWorkIdByMax = getValueFromCell(row.getCell(12));
            String personInChargeEmailByMax = getValueFromCell(row.getCell(13));
            list.add(new TConfigurePersonAndEquipment(pl, enginery, discern, sparePartName, sparePartCode, personInChargeNameBy01, personInChargeEmailBy01, personInChargeWorkIdBy01, personInChargeNameBy02, personInChargeEmailBy02, personInChargeWorkIdBy02, personInChargeNameByMax, personInChargeEmailByMax, personInChargeWorkIdByMax));
        }
        return list;
    }

    private void saveToDatabaseByPersonAndEquipment(List<TConfigurePersonAndEquipment> listOfObjects) throws Exception {
        for (TConfigurePersonAndEquipment object : listOfObjects) {
            int isDuplicate =sparePartByDataMapper.existsWithSameFieldsByPersonAndEquipment(object);
            if (isDuplicate>=1) {
                throw new Exception("数据库已存在相同的数据，请勿添加，谢谢!"+object);
            }else {
                sparePartByDataMapper.insertByPersonAndEquipment(object);
            }

        }
    }








}
