package cc.mrbird.febs.rdProject.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的任務責任人(團隊)表實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectTaskAssignments {

    @ApiModelProperty(value = "任务ID", position = 0)
    private Long taskId;

    @ApiModelProperty(value = "类型（0=用户,1=团队）", position = 1)
    private Integer assigneeType;

    @ApiModelProperty(value = "用户工号（当type=0时），团队ID（当type=1时）", position = 2)
    private String assigneeId;
}
