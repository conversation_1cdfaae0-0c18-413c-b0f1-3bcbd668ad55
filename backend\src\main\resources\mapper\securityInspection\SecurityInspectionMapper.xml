<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.securityInspection.dao.SecurityInspectionMapper">

<!--    查询时间-->
    <select id="queryDate" resultType="java.lang.String">
        SELECT DISTINCT
            [NY_YearMonth]
        FROM [APPGCL_Process].[dbo].[ZGAQXJ_MainInfo] ORDER BY [NY_YearMonth] DESC
    </select>

<!--    查询厂区-->
    <select id="queryFactory" resultType="java.lang.String">
        SELECT DISTINCT
            [factory]
        FROM [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
    </select>

<!--    查询楼栋-->
    <select id="queryBuilding" resultType="java.lang.String">
        SELECT DISTINCT
            [building]
        FROM [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
    </select>

<!--    查询楼层-->
    <select id="queryFloor" resultType="java.lang.String">
        SELECT DISTINCT
             [floor]
         FROM [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
    </select>

<!--    查询主要信息-->
    <select id="queryInfo" resultType="cc.mrbird.febs.securityInspection.entity.SecurityInspectionExportExcelAllPoint">
        WITH C_UsersPerBuilding AS (
            -- 获取每个 building 下的所有巡查人，并带上 XJ_Number（应巡次数）
            SELECT DISTINCT
                building,
                XJ_UserName AS name,
                XJ_Number
            FROM
           [APPGCL_Process].[dbo].[ZGAQXJ_User]
            ),
            AllStations AS (
        -- 提取所有 QRCode 对应的真实物理点位
        SELECT
            building,
            floor,
            SJWZ_Refer AS station,
            QRCode_Text
        FROM
            [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
            ),
            AllStationUserCombinations AS (
        -- 构建所有巡查人 × 真实点位的组合，并带上应巡次数
        SELECT
            S.building,
            S.floor,
            S.station,
            S.QRCode_Text,
            U.name,
            U.XJ_Number
        FROM
            AllStations S
            INNER JOIN
            C_UsersPerBuilding U ON S.building = U.building
            )
-- 主查询：LEFT JOIN + 统计 + 显示应巡次数
        SELECT
            A.building,
            A.floor,
            A.station,
            A.name,
            A.QRCode_Text,
            A.XJ_Number AS task, -- 应巡次数
            COALESCE(COUNT(B.Ins_Date), 0) AS number, -- 实际巡查次数
            COALESCE(COUNT(CASE WHEN B.SF_NG = 'Y' THEN 1 END), 0) AS NGNumber -- 异常次数
        FROM
            AllStationUserCombinations A
                LEFT JOIN
            [APPGCL_Process].[dbo].[ZGAQXJ_MainInfo] B
        ON
            A.name = B.XJZG_UserName
            AND A.QRCode_Text = B.QRCode_Text
            AND B.NY_YearMonth LIKE CONCAT('%', #{date}, '%')
        <where>
            <if test="building != null and building != ''">
                AND A.building = #{building}
            </if>
            <if test="floor != null and floor != ''">
                AND A.floor = #{floor}
            </if>
        </where>
        GROUP BY
            A.building,
            A.floor,
            A.station,
            A.name,
            A.QRCode_Text,
            A.XJ_Number
        ORDER BY
            A.building,
            A.floor,
            A.station,
            A.name;
    </select>


    <!--    获取查询樓層表数据-->
    <select id="queryInfoByFloor" resultType="cc.mrbird.febs.securityInspection.entity.SecurityInspectionExportExcelFloor">
        WITH PointCountsPerFloor AS (
            -- 统计每栋楼、每层楼有多少个点位
            SELECT
                building,
                floor,
                COUNT(DISTINCT QRCode_Text) AS point_count
            FROM
            [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
        GROUP BY
            building, floor
            ),
            TaskPerUser AS (
        -- 每个巡查人的任务量 = 点位数 × XJ_Number
        SELECT
            U.building,
            P.floor,
            U.XJ_UserName AS name,
            P.point_count * U.XJ_Number AS task
        FROM
            [APPGCL_Process].[dbo].[ZGAQXJ_User] U
            INNER JOIN
            PointCountsPerFloor P ON U.building = P.building
            ),
            ActualCounts AS (
        -- 统计每个巡查人，在每栋楼、每层楼的实际巡查次数和NG次数
        SELECT
            A.building,
            A.floor,
            B.XJZG_UserName AS name,
            COUNT(*) AS number,
            SUM(CASE WHEN B.SF_NG IN ('Y', '是', '1') THEN 1 ELSE 0 END) AS NG
        FROM
            [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo] A
            INNER JOIN
            [APPGCL_Process].[dbo].[ZGAQXJ_MainInfo] B
        ON A.QRCode_Text = B.QRCode_Text
        WHERE
            B.NY_YearMonth LIKE CONCAT('%',#{date},'%') -- 可替换为参数
        GROUP BY
            A.building, A.floor, B.XJZG_UserName
            )
        -- 合并任务量与实际完成量及NG次数
        SELECT
            T.building,
            T.floor,
            T.name,
            T.task,
            COALESCE(A.number, 0) AS number,
            COALESCE(A.NG, 0) AS NGNumber
        FROM
            TaskPerUser T
                LEFT JOIN
            ActualCounts A
            ON T.building = A.building
                AND T.floor = A.floor
                AND T.name = A.name
        <where>
            <if test="building != null and building != ''">
                AND T.building = #{building}
            </if>
            <if test="floor != null and floor != ''">
                AND T.floor = #{floor}
            </if>
        </where>
        ORDER BY
            T.building, T.floor, T.name;
    </select>


    <select id="selectFloor" resultType="cc.mrbird.febs.securityInspection.entity.InspectionStationRaw">
        WITH PointCountsPerFloor AS (
        -- 统计每栋楼、每层楼有多少个点位
        SELECT
        building,
        floor,
        COUNT(DISTINCT QRCode_Text) AS point_count
        FROM
        [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
        GROUP BY
        building, floor
        ),
        TaskPerUser AS (
        -- 每个巡查人的任务量 = 点位数 × XJ_Number
        SELECT
        U.building,
        P.floor,
        U.XJ_UserName AS name,
        U.XJ_Number,
        P.point_count * U.XJ_Number AS task
        FROM
        [APPGCL_Process].[dbo].[ZGAQXJ_User] U
        INNER JOIN
        PointCountsPerFloor P ON U.building = P.building
        ),
        ActualCounts AS (
        -- 统计每个巡查人，在每栋楼、每层楼的实际巡查次数和NG次数
        SELECT
        A.building,
        A.floor,
        B.XJZG_UserName AS name,
        COUNT(*) AS number,
        SUM(CASE WHEN B.SF_NG IN ('Y', '是', '1') THEN 1 ELSE 0 END) AS NG
        FROM
        [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo] A
        INNER JOIN
        [APPGCL_Process].[dbo].[ZGAQXJ_MainInfo] B
        ON A.QRCode_Text = B.QRCode_Text
        WHERE
        B.NY_YearMonth LIKE CONCAT('%',#{date},'%') -- 可替换为参数
        GROUP BY
        A.building, A.floor, B.XJZG_UserName
        )
        -- 合并任务量与实际完成量及NG次数
        SELECT
        T.building,
        T.floor,
        T.name,
        T.task,
        T.XJ_Number AS queryNumber,
        COALESCE(A.number, 0) AS number,
        COALESCE(A.NG, 0) AS NGNumber
        FROM
        TaskPerUser T
        LEFT JOIN
        ActualCounts A
        ON T.building = A.building
        AND T.floor = A.floor
        AND T.name = A.name
        <where>
            <if test="building != null and building != ''">
                AND T.building = #{building}
            </if>
            <if test="floor != null and floor != ''">
                AND T.floor = #{floor}
            </if>
        </where>
        ORDER BY
        T.building, T.floor, T.name;
    </select>

    <select id="queryStation" resultType="cc.mrbird.febs.securityInspection.entity.InspectionStationRaw">
            WITH C_UsersPerBuilding AS (
            SELECT DISTINCT building, XJ_UserName AS name, XJ_Number
            FROM [APPGCL_Process].[dbo].[ZGAQXJ_User]
            ),
            AllStations AS (
            SELECT building, floor, SJWZ_Refer AS station, QRCode_Text
            FROM [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
            ),
            AllStationUserCombinations AS (
            SELECT S.building, S.floor, S.station, S.QRCode_Text, U.name, U.XJ_Number
            FROM AllStations S
            INNER JOIN C_UsersPerBuilding U ON S.building = U.building
            )
            SELECT
            A.building,
            A.floor,
            A.station,
            A.name,
            A.XJ_Number AS task,
            A.XJ_Number AS queryNumber,
            COALESCE(COUNT(B.Ins_Date), 0) AS number,
            COALESCE(COUNT(CASE WHEN B.SF_NG = 'Y' THEN 1 END), 0) AS NGNumber
            FROM AllStationUserCombinations A
            LEFT JOIN [APPGCL_Process].[dbo].[ZGAQXJ_MainInfo] B
            ON A.name = B.XJZG_UserName AND A.QRCode_Text = B.QRCode_Text AND B.NY_YearMonth LIKE CONCAT('%', #{date}, '%')
            <where>
                <if test="building != null and building != ''">
                    AND A.building = #{building}
                </if>
                <if test="floor != null and floor != ''">
                    AND A.floor = #{floor}
                </if>
            </where>
            GROUP BY A.building, A.floor, A.station, A.name, A.XJ_Number
            ORDER BY A.building, A.floor, A.station, A.name;

    </select>


    <select id="queryPoint" resultType="cc.mrbird.febs.securityInspection.entity.SecurityInspectionVO">
        WITH C_UsersPerBuilding AS (
        SELECT DISTINCT building, XJ_UserName AS name, XJ_Number
        FROM [APPGCL_Process].[dbo].[ZGAQXJ_User]
        ),
        AllStations AS (
        SELECT building, floor, SJWZ_Refer AS station, QRCode_Text
        FROM [APPGCL_Process].[dbo].[ZGAQXJ_PlaceInfo]
        ),
        AllStationUserCombinations AS (
        SELECT S.building, S.floor, S.station, S.QRCode_Text, U.name, U.XJ_Number
        FROM AllStations S
        INNER JOIN C_UsersPerBuilding U ON S.building = U.building
        )
        SELECT
        A.building,
        A.floor,
        A.station,
        A.name,
        CONCAT(B.[XC_PicUrl],B.[XC_PicName01]) AS img1,
        CASE WHEN B.[XC_PicName02] IS NOT NULL THEN CONCAT(B.[XC_PicUrl],B.[XC_PicName02]) ELSE '' END AS img2,
        CASE WHEN B.[SF_NG] IS NULL THEN 'Y' ELSE B.[SF_NG] END AS status,
        B.[NG_Cause] AS description,
        CASE WHEN B.[Ins_Date] IS NULL THEN '未簽到' ELSE B.[Ins_Date] END AS date
        FROM AllStationUserCombinations A
        LEFT JOIN [APPGCL_Process].[dbo].[ZGAQXJ_MainInfo] B
        ON A.name = B.XJZG_UserName AND A.QRCode_Text = B.QRCode_Text AND B.NY_YearMonth LIKE CONCAT('%', #{date}, '%')
        <where>
            <if test="building != null and building != ''">
                AND A.building = #{building}
            </if>
            <if test="floor != null and floor != ''">
                AND A.floor = #{floor}
            </if>
        </where>
        ORDER BY A.building, A.floor, A.station, A.name;
    </select>
</mapper>
