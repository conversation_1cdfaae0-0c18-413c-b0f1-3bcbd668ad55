package cc.mrbird.febs.other.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TConfigureCause {
    @ApiModelProperty(value = "自增id",position = 0)
    private String         id;
    @ApiModelProperty(value = "设备id编号",position = 1)
    private String         equipmentId;
    @ApiModelProperty(value = "设备异常代码")
    private String         errorCode;
    @ApiModelProperty(value = "异常类型",position = 2)
    private String         errorType;
    @ApiModelProperty(value = "异常原因",position = 3)
    private String         causation;
    @ApiModelProperty(value = "解决对策",position = 4)
    private String         countermeasures;
    @ApiModelProperty(value = "修改人",position = 5)
    private String         editor;
    @ApiModelProperty(value = "修改时间",position = 6)
    private String         editTime;
    @ApiModelProperty(value = "标准维修工时",position=7)
    private String         standardMaintenanceDuration;
}
