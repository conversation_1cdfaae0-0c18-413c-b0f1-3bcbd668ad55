<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectFileMapper">

    <!-- 結果映射 -->
    <resultMap id="RdProjectFileResultMap" type="cc.mrbird.febs.rdProject.entity.RdProjectFile">
        <id column="file_id" property="fileId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_path" property="filePath"/>
        <result column="task_id" property="taskId"/>
    </resultMap>

    <!-- 插入文件記錄 -->
    <insert id="insertRdProjectFile" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectFile" useGeneratedKeys="true" keyProperty="fileId">
        INSERT INTO rd_project_file (file_name, file_path, task_id)
        VALUES (#{fileName}, #{filePath}, #{taskId})
    </insert>

    <!-- 根據文件ID刪除文件記錄 -->
    <delete id="deleteRdProjectFileById">
        DELETE FROM rd_project_file WHERE file_id = #{fileId}
    </delete>

    <!-- 根據任務ID刪除文件記錄 -->
    <delete id="deleteRdProjectFileByTaskId">
        DELETE FROM rd_project_file WHERE task_id = #{taskId}
    </delete>

    <!-- 更新文件記錄 -->
    <update id="updateRdProjectFile" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectFile">
        UPDATE rd_project_file
        SET file_name = #{fileName},
            file_path = #{filePath},
            task_id = #{taskId}
        WHERE file_id = #{fileId}
    </update>

    <!-- 根據文件ID查詢文件記錄 -->
    <select id="selectRdProjectFileById" resultMap="RdProjectFileResultMap">
        SELECT file_id, file_name, file_path, task_id
        FROM rd_project_file
        WHERE file_id = #{fileId}
    </select>

    <!-- 根據任務ID查詢文件記錄列表 -->
    <select id="selectRdProjectFileByTaskId" resultMap="RdProjectFileResultMap">
        SELECT file_id, file_name, file_path, task_id
        FROM rd_project_file
        WHERE task_id = #{taskId}
    </select>

    <!-- 查詢所有文件記錄 -->
    <select id="selectAllRdProjectFile" resultMap="RdProjectFileResultMap">
        SELECT file_id, file_name, file_path, task_id
        FROM rd_project_file
    </select>

</mapper>
