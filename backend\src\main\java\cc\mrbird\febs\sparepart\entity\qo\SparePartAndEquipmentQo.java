package cc.mrbird.febs.sparepart.entity.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SparePartAndEquipmentQo {
   @ApiModelProperty(value = "事業處",position = 0,example = "IDS1")
   private String pl;
   @ApiModelProperty(value = "課別",position = 1,example = "裝配一課")
   private String discern;
   @ApiModelProperty(value = "系列",position = 2,example = "1700SKT")
   private String production;
   @ApiModelProperty(value = "線體",position = 3,example = "V3")
   private String productionLine;
   @ApiModelProperty(value = "備品代碼",position = 4,example = "1")
   private String sparePartCode;
   @ApiModelProperty(value = "機能",position = 5,example = "裝配、電鍍、成型、沖壓")
   private String enginery;
}
