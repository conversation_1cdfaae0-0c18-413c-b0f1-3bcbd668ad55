package cc.mrbird.febs.indexpage.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataComment {
    @ApiModelProperty(value = "顶级留言&&回复判别式：0表示顶级留言 其他表示回复（对应parentId）")
    private Long id;
    @ApiModelProperty(value = "留言或者回复内容",notes = "0")
    private String content;
    @ApiModelProperty(value = "创建时间",notes="1")
    @JsonIgnore
    private String createdAt;
    @ApiModelProperty(value = "留言或者回复的父id,表示回复关系",notes="2")
    private Integer parentId;
    @ApiModelProperty(value = "层级关系",notes = "3")
    private Integer lever;
    @ApiModelProperty(value = "姓名")
    private String userName;
    @ApiModelProperty(value = "工号")
    private String workId;
    private Integer isSort;
}
