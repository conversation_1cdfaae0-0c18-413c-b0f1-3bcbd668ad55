package cc.mrbird.febs.backwork.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName t_data_backwork
 */
@TableName(value ="t_data_backwork")
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class TDataBackwork implements Serializable {
   @TableField(exist = false)
   private static final long serialVersionUID = 1L;
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uuid;
    /**
     * 系列
     */
   @ApiModelProperty("系列")
    private String production;

    /**
     * 线体
     */
    @ApiModelProperty("线体")
    private String productionline;

    /**
     * 填表日期
     */
    @ApiModelProperty("填表日期")
    private String recorddate;

    /**
     * 重工单位
     */
    @ApiModelProperty("重工单位")
    private String backworkunit;

    /**
     * 重工类型
     */
    @ApiModelProperty("重工类型")
    private String backworktype;

    /**
     * 料号
     */
    @ApiModelProperty("料号")
    private String partnumber;

    /**
     * dc
     */
    @ApiModelProperty("dc")
    private String dc;

    /**
     * 重工作业指导书编号
     */
    @ApiModelProperty("重工作业指导书编号")
    private String backworknumber;

    /**
     * 重工原因
     */
    @ApiModelProperty("重工原因")
    private String backworkcause;

    /**
     * 重工数量
     */
    @ApiModelProperty("重工数量")
    private String backworkquantity;

    /**
     * 不良品数
     */
    @ApiModelProperty("不良品数")
    private String nod;

    /**
     * 重工工时
     */
    @ApiModelProperty("重工工时")
    private String backworkhour;

    /**
     * 重工方式
     */
    @ApiModelProperty("重工方式")
    private String backworkmethod;

    /**
     * 重工人员
     */
    @ApiModelProperty("重工人员")
    private String backworkuser;

    /**
     * 重工次数
     */
    @ApiModelProperty("重工次数")
    private String backworklimit;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 通過正式評鑒人員
     */
    @ApiModelProperty("通過正式評鑒人員")
    private String yesuser;

    /**
     * 未經正式評鑒人員
     */
    @ApiModelProperty("未經正式評鑒人員")
    private String disuser;

    /**
     * 不滿足資格狀況說明
     */
    @ApiModelProperty("不滿足資格狀況說明")
    private String nqtalk;

    /**
     * 臨時接受培訓內容說明
     */
    @ApiModelProperty("臨時接受培訓內容說明")
    private String lstalk;

    /**
     * 培訓人
     */
    @ApiModelProperty("培訓人")
    private String trainer;

    /**
     * 完成時間
     */
    @ApiModelProperty("完成時間")
    private String completiontime;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why1cause;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why2cause;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why3cause;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why4cause;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why5cause;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why1outflow;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why2outflow;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why3outflow;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why4outflow;

    /**
     * 
     */
    @ApiModelProperty("")
    private String why5outflow;

    /**
     * 改善追蹤
     */
    @ApiModelProperty("改善追蹤")
    private String itrackingWhy;

    /**
     * 責任人
     */
    @ApiModelProperty("責任人")
    private String picWhy;

    /**
     * 預計完成時間
     */

    @ApiModelProperty("預計完成時間")
    private String extimeWhy;

    /**
     * 實際完成時間
     */
    @ApiModelProperty("實際完成時間")
    private String endtimeWhy;

    /**
     * 抽樣方法
     */

    @ApiModelProperty("抽樣方法")
    private String smethod;

    /**
     * 檢驗項目
     */
    @ApiModelProperty("檢驗項目")
    private String insitem;

    /**
     * 合格數 
     */
    @ApiModelProperty("合格數")
    private String anumber;

    /**
     * 不合格數
     */
    @ApiModelProperty("不合格數")
    private String nanumber;

    /**
     * 不合格處理方式說明
     */
    @ApiModelProperty("不合格處理方式說明")
    private String natalk;

    /**
     * 填表契機
     */
    @ApiModelProperty("填表契機")
    private String moment;
    /**
     * 文件編號
     */
    @ApiModelProperty("文件編號(文件名稱)")
    private String fileId;
    @ApiModelProperty("文件路徑")
    private String filePath;
    @ApiModelProperty("文件名字")
    private String fileName;
    @ApiModelProperty("流出原因改善追踪")
    private String itrackingOut;
    @ApiModelProperty("流出原因责任人")
    private String         picOut;
    @ApiModelProperty("流出原因预计完成时间")
     private String        exTimeOut;
    @ApiModelProperty("流出原因实际完成时间")
    private String       endTimeOut;
    @ApiModelProperty("重工费用")
    private String laborCost;
    @ApiModelProperty("人工费用")
    private String processCost;
    @ApiModelProperty("总费用")
    private String cancellationCost;
    /**
     * 品保單位主管
     */
    @ApiModelProperty("生產線長承辦")
    private String qh1;

    /**
     * 品保/品管
     */
    @ApiModelProperty("課長審核")
    private String qh2;
    /**
     * 重工單位主管
     */
    @ApiModelProperty("品保審核並確定重工責任單位")
    private String qh3;
    @ApiModelProperty("重工責任單位主管審核")
    private String qh4;
    @ApiModelProperty("工號")
    private String workId;
    @ApiModelProperty("簽核狀態:")
    private String qhStatus;
    @ApiModelProperty("品保最终审核")
    private String qh5;
    @ApiModelProperty("簽核流程編號")
    private String stNo;
    @ApiModelProperty("线长名字")
    private String lineLeaderName;
    @ApiModelProperty("责任品保工程师")
    private String zrqc;
    private String showId;
    private String pl;
    @ApiModelProperty("重工類型 0是其他1是客訴")
    private String isOther;
    private String qh6;
    private String isBack;
    private String goBackWhy;
}