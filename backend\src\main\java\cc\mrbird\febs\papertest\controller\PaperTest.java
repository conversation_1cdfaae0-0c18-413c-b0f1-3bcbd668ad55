package cc.mrbird.febs.papertest.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@ResponseBody
@Api(tags = "test-controller")
@Slf4j
@RequestMapping("/PaperTestCommonController")
public class PaperTest {
        @PostMapping("/testExcelPortByDateTime")
        public FebsResponse testExcelPortByDateTime(@RequestParam("file") MultipartFile file){
            List<String> times=new ArrayList<>();
            if (file.isEmpty()) {
                return new FebsResponse().code("500").message("没有可以上传的文件，是1KB也没有，没有");
            }
            if (!isExcelFile(file)) {
                return new FebsResponse().code("500").message("这个文件不是Excel文件,不是");
            }
            try {
                Workbook workbook = WorkbookFactory.create(file.getInputStream());
                Sheet sheet = workbook.getSheetAt(0);
                for (Row row : sheet) {
                    if(row.getRowNum() == 0) continue;

                  String  recordDate= importByExcelForDate((row.getCell(0)));
                    System.out.println(recordDate);
                    times.add(recordDate);
                }
            } catch (Exception ex) {
                return  new FebsResponse().code("500").message("导入失败"+ex.getMessage());
            }

            return  new FebsResponse().code("200").message("导入成功");
        }

    private boolean isExcelFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType.equals("application/vnd.ms-excel") ||
                contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                contentType.equals("text/csv");
    }
    private String getValueFromCell(Cell cell) {
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((int)numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            default:
                return "";
        }
    }

    public static String importByExcelForDate(Cell currentCell) {
        String currentCellValue = "";
        if (0 == currentCell.getCellType()) {
            if (DateUtil.isCellDateFormatted(currentCell)) {
                Date d = currentCell.getDateCellValue();
                DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                currentCellValue = formatter.format(d);
            } else {
                currentCell.setCellType(currentCell.CELL_TYPE_STRING);
                currentCellValue = currentCell.toString();
            }
        } else {
            currentCellValue = currentCell.toString();
        }
        return currentCellValue;
    }

}
