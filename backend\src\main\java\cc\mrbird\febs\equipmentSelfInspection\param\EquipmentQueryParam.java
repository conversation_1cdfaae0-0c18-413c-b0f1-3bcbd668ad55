package cc.mrbird.febs.equipmentSelfInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 覃金華
 * Date: 2024-08-13
 * Time: 下午 01:08
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class EquipmentQueryParam {
    @ApiModelProperty(value = "課別,裝配一課|裝配二課",example = "裝配一課",position = 0)
    private String discern;
    @ApiModelProperty(value = "系列,1700SKT|4189SKT",example = "1700SKT",position = 1)
    private String production;
    @ApiModelProperty(value = "線體,V1|V2",example = "V1",position = 2)
    private String productionLine;
    @ApiModelProperty(value = "設備名稱,1#插針機|2#插針機|壓平機",example = "2#插針機",position = 3)
    private String equipmentName;
    @ApiModelProperty(value = "點檢類型,1|2|3",example = "1",position = 4)
    private String selfInspectionType;

}
