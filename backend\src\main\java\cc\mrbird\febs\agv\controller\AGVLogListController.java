package cc.mrbird.febs.agv.controller;

import cc.mrbird.febs.agv.param.AgvLogListQueryParam;
import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.agv.entity.AGVLogList;
import cc.mrbird.febs.agv.service.AGVLogListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@Api(tags = "AGV小车")
@RestController
@RequestMapping("/agv/logs")
public class AGVLogListController {
    private final AGVLogListService agvLogListService;

    @Autowired
    public AGVLogListController(AGVLogListService agvLogListService) {
        this.agvLogListService = agvLogListService;
    }

    // 查询AGV日志
    @ApiOperation(value = "查询AGV日志", httpMethod = "POST")
    @PostMapping("/queryLogs")
    public FebsResponse queryLogs(@RequestBody AgvLogListQueryParam agvLogQueryParam) {
        List<AGVLogList> logs;
        try {
            logs = agvLogListService.queryAGVLogList(agvLogQueryParam); // 这里可以根据需要修改为带参数的查询
        } catch (Exception e) {
            return new FebsResponse().code("500").message("错误参数" + agvLogQueryParam);
        }
        return new FebsResponse().code("200").data(logs);
    }
}