<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.badRelease.dao.BadReleaseMapper">

    <!--查詢產品處-->
    <select id="querySBU" resultType="java.lang.String">
        select SBU from [APPGCL_Process].[dbo].[USER_Table]
        <where>
            <if test="workId != null and workId != ''">
                userid = #{workId}
            </if>
        </where>
    </select>

    <!--查詢機能-->
    <select id="queryFunction" resultType="java.lang.String">
        select functions from [APPGCL_Process].[dbo].[USER_Table]
        <where>
            <if test="workId != null and workId != ''">
                userid = #{workId}
            </if>
        </where>
    </select>

    <!--查詢課別-->
    <select id="querySecntion" resultType="java.lang.String">
        select section from [APPGCL_Process].[dbo].[BLSF_MainInfo]
    </select>

    <!--查詢線體-->
    <select id="queryLine" resultType="java.lang.String">
        select line from [APPGCL_Process].[dbo].[BLSF_MainInfo]
    </select>

    <!--查詢料號-->
    <select id="queryMaterialNumber" resultType="java.lang.String">
        select CPLH_PFN from [APPGCL_Process].[dbo].[BLSF_MainInfo]
    </select>

    <!--查詢不良釋放日期-->
    <select id="queryBadReleaseDate" resultType="java.lang.String">
        select YF_MonthDate from [APPGCL_Process].[dbo].[BLSF_MainInfo]
        order by id desc
    </select>

    <!--條件查詢，條件為：課別、線體、料號、不良釋放日期-->
    <select id="queryCondition" resultType="cc.mrbird.febs.badRelease.entity.vo.BadReleaseInfoVo">
        select id,section,line,CPLH_PFN materialNumber,YF_MonthDate badReleaseDate,SFRY_Name name,
            SF_NG NgStatus,NG_Cause NgCause,Upload_AIPicURL AIPicture,Upload_CCDPicURL CCDPicture  from [APPGCL_Process].[dbo].[BLSF_MainInfo]
        <where>
            <if test="section != null and section != ''">
                section = #{section}
            </if>
            <if test="line != null and line != ''">
                and line = #{line}
            </if>
            <if test="materialNumber != null and materialNumber != ''">
                and CPLH_PFN = #{materialNumber}
            </if>
            <if test="badReleaseDate != null and badReleaseDate != ''">
                and YF_MonthDate = #{badReleaseDate}
            </if>
        </where>
        order by YF_MonthDate desc
    </select>

    <!--詳情頁面展示，id查詢-->
    <select id="queryById" resultType="cc.mrbird.febs.badRelease.entity.dto.BadReleaseDetailDto">
        select id,SBU,functions,section,line,CPLH_PFN materialNumber,YF_MonthDate badReleaseDate,SFRY_Name name,STATUS status,
            SF_NG NgStatus,NG_Cause NgCause,Upload_AIPicURL AIPicture,Upload_CCDPicURL CCDPicture,JCJG_Test result  from [APPGCL_Process].[dbo].[BLSF_MainInfo]
        <where>
            <if test="id != null and id != ''">
                id = #{id}
            </if>
        </where>
    </select>

    <!--詳情頁面展示，id查詢，附表料號輔助查詢-->
    <select id="queryByMaterialNumber" resultType="cc.mrbird.febs.badRelease.entity.dto.AdditionalInfoDto">
        select id,BLXM_Item badItem,JCGX_Process badProcess,JT_Shot lens,
               SWTP_PicURL badSamplePicture,AITestTP_PicURL testPicture,JCJG_Default FROM [APPGCL_Process].[dbo].[BLSF_PoorList]
        <where>
            <if test="materialNumber != null and materialNumber != ''">
                CPLH_PFN = #{materialNumber}
            </if>
        </where>
        order by id ASC
    </select>


</mapper>