package cc.mrbird.febs.backwork.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class HtmlCode {
    public String getBackHtmlCode(String jsr,String yy,String lh) throws UnsupportedEncodingException {
        String code="<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>Document</title>\n" +
                "</head>\n" +
                "<body>\n" +
                "<table style=\"border-collapse: collapse; width: 100%; border: 1px solid black;\" >\n" +
                "    <tr>\n" +
                "        <th style=\"border: 1px solid black; padding: 15px; text-align: left;\">接收人</th>\n" +
                "        <th style=\"border: 1px solid black; padding: 15px; text-align: left;\">消息</th>\n" +
                "        <th style=\"border: 1px solid black; padding: 15px; text-align: left;\">料号</th>\n" +
                "    </tr>\n" +
                "    <tr>\n" +
                "        <td style=\"border: 1px solid black; padding: 15px; text-align: left;\">"+jsr+"</td>\n" +
                "        <td style=\"border: 1px solid black; padding: 15px; text-align: left;\">你好。你填写的重工记录表被退签了，原因为："+yy+"</td>\n" +
                "        <td style=\"border: 1px solid black; padding: 15px; text-align: left;\">"+lh+"</td>\n" +
                "    </tr>\n" +
                "</table>\n" +
                "</body>\n" +
                "</html>";
        return URLEncoder.encode(code,"utf-8");
    }
}
