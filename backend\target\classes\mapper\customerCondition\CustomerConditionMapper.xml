<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.customerCondition.dao.CustomerConditionMapper">

    <!-- 查詢客戶列表 -->
    <select id="selectCustomerList" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionCustomerData">
        SELECT
            [customer_id] as customerId,
            [customer_name] as customerName
        FROM [APPGCL_Process].[dbo].[customer_condition_customer_data]
    </select>

    <!-- 根據ID查詢客戶 -->
    <select id="selectCustomerById" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionCustomerData">
        SELECT
            [customer_id] as customerId,
            [customer_name] as customerName
        FROM [APPGCL_Process].[dbo].[customer_condition_customer_data]
        WHERE [customer_id] = #{customerId}
    </select>

    <!-- 查詢廠區列表 -->
    <select id="selectSiteAreaList" resultType="java.lang.String">
        SELECT DISTINCT [site_area]
        FROM [APPGCL_Process].[dbo].[customer_condition_user]
    </select>

    <!-- 根據廠區查詢產品処列表 -->
    <select id="selectSbuBySiteArea" resultType="java.lang.String">
        SELECT DISTINCT [sbu]
        FROM [APPGCL_Process].[dbo].[customer_condition_user]
        WHERE [site_area] = #{siteArea}
    </select>

    <!-- 根據廠區和產品処查詢機能列表 -->
    <select id="selectFunctionsBySiteAreaAndSbu" resultType="java.lang.String">
        SELECT DISTINCT [functions]
        FROM [APPGCL_Process].[dbo].[customer_condition_user]
        WHERE [site_area] = #{siteArea} AND [sbu] = #{sbu}
    </select>

    <!-- 根據廠區、產品処和機能查詢課別列表 -->
    <select id="selectSectionBySiteAreaAndSbuAndFunctions" resultType="java.lang.String">
        SELECT DISTINCT [section]
        FROM [APPGCL_Process].[dbo].[customer_condition_user]
        WHERE [site_area] = #{siteArea} AND [sbu] = #{sbu} AND [functions] = #{functions}
    </select>

    <!-- 查詢客戶條件記錄主要信息            <if test="startDate != null">
                AND [upload_date] >= #{startDate}
            </if>
            <if test="endDate != null">
                AND [upload_date] &lt;= #{endDate}
            </if> -->
    <select id="selectCustomerConditionMainInfo" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionMainInfo">
        SELECT
            [customer_condition_main_info_id] as customerConditionMainInfoId,
            [customer_condition_file_id] as customerConditionFileId,
            [customer_condition_file_name] as customerConditionFileName,
            [upload_date] as uploadDate,
            [upload_user_work_id] as uploadUserWorkId,
            [upload_user_name] as uploadUserName,
            [sbu] as sbu,
            [functions] as functions,
            [site_area] as siteArea,
            [section] as section,
            [customer_id] as customerId,
            [customer_name] as customerName,
            [update_remark] as updateRemark
        FROM [APPGCL_Process].[dbo].[customer_condition_main_info]
        <where>
            <if test="sbu != null and sbu != ''">
                AND [sbu] = #{sbu}
            </if>
            <if test="functions != null and functions != ''">
                AND [functions] = #{functions}
            </if>
            <if test="siteArea != null and siteArea != ''">
                AND [site_area] = #{siteArea}
            </if>
            <if test="section != null and section != ''">
                AND [section] = #{section}
            </if>
            <if test="customerId != null">
                AND [customer_id] = #{customerId}
            </if>
            <if test="customerName != null and customerName != ''">
                AND [customer_name] = #{customerName}
            </if>

            <if test="uploadUserWorkId != null and uploadUserWorkId != ''">
                AND [upload_user_work_id] = #{uploadUserWorkId}
            </if>
            <if test="uploadUserName != null and uploadUserName != ''">
                AND [upload_user_name] = #{uploadUserName}
            </if>
        </where>
        ORDER BY [upload_date] DESC
    </select>

    <!-- 查詢客戶條件記錄文件 -->
    <select id="selectCustomerConditionFileById" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionFile">
        SELECT
            [customer_condition_file_id] as customerConditionFileId,
            [customer_condition_file_name] as customerConditionFileName,
            [customer_condition_file_url] as customerConditionFileUrl,
            [upload_date] as uploadDate
        FROM [APPGCL_Process].[dbo].[customer_condition_file]
        WHERE [customer_condition_file_id] = #{fileId}
    </select>

    <!-- 查詢客戶條件記錄用戶 -->
    <select id="selectCustomerConditionUsers" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionUser">
        SELECT
            [customer_condition_upload_user_id] as customerConditionUploadUserId,
            [customer_condition_upload_user_name] as customerConditionUploadUserName,
            [sbu] as sbu,
            [functions] as functions,
            [site_area] as siteArea,
            [section] as section,
            [work_id] as workId
        FROM [APPGCL_Process].[dbo].[customer_condition_user]
    </select>

    <!-- 根據多條件查詢用戶信息 -->
    <select id="selectCustomerConditionUsersByCondition" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionUser">
        SELECT
            [customer_condition_upload_user_id] as customerConditionUploadUserId,
            [customer_condition_upload_user_name] as customerConditionUploadUserName,
            [sbu] as sbu,
            [functions] as functions,
            [site_area] as siteArea,
            [section] as section,
            [work_id] as workId
        FROM [APPGCL_Process].[dbo].[customer_condition_user]
        <where>
            <if test="customerConditionUploadUserId != null">
                AND [customer_condition_upload_user_id] = #{customerConditionUploadUserId}
            </if>
            <if test="customerConditionUploadUserName != null and customerConditionUploadUserName != ''">
                AND [customer_condition_upload_user_name] LIKE '%' + #{customerConditionUploadUserName} + '%'
            </if>
            <if test="sbu != null and sbu != ''">
                AND [sbu] = #{sbu}
            </if>
            <if test="functions != null and functions != ''">
                AND [functions] = #{functions}
            </if>
            <if test="siteArea != null and siteArea != ''">
                AND [site_area] = #{siteArea}
            </if>
            <if test="section != null and section != ''">
                AND [section] = #{section}
            </if>
            <if test="workId != null and workId != ''">
                AND [work_id] = #{workId}
            </if>
        </where>
    </select>

    <!-- 查詢客戶條件記錄郵件 -->
    <select id="selectCustomerConditionMails" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionMail">
        SELECT
            [mail_id] as mailId,
            [user_name] as userName,
            [mail_address] as mailAddress,
            [customer_id] as customerId,
            [customer_name] as customerName,
            [leader_mail_id] as leaderMailId,
            [user_type] as userType
        FROM [APPGCL_Process].[dbo].[customer_condition_mail]
        <where>
            <if test="customerId != null">
                AND [customer_id] = #{customerId}
            </if>
        </where>
    </select>

    <!-- 保存客戶條件記錄文件 -->
    <insert id="insertCustomerConditionFile" parameterType="cc.mrbird.febs.customerCondition.entity.CustomerConditionFile" useGeneratedKeys="true" keyProperty="customerConditionFileId">
        INSERT INTO [APPGCL_Process].[dbo].[customer_condition_file]
        (
            [customer_condition_file_name],
            [customer_condition_file_url],
            [upload_date]
        )
        VALUES
        (
            #{customerConditionFileName},
            #{customerConditionFileUrl},
            SYSDATETIME()
        )
    </insert>

    <!-- 保存客戶條件記錄主要信息 -->
    <insert id="insertCustomerConditionMainInfo" parameterType="cc.mrbird.febs.customerCondition.entity.CustomerConditionMainInfo" useGeneratedKeys="true" keyProperty="customerConditionMainInfoId">
        INSERT INTO [APPGCL_Process].[dbo].[customer_condition_main_info]
        (
            [customer_condition_file_id],
            [customer_condition_file_name],
            [upload_date],
            [upload_user_work_id],
            [upload_user_name],
            [sbu],
            [functions],
            [site_area],
            [section],
            [customer_id],
            [customer_name],
            [update_remark],
            [version]
        )
        VALUES
        (
            #{customerConditionFileId},
            #{customerConditionFileName},
            SYSDATETIME(),
            #{uploadUserWorkId},
            #{uploadUserName},
            #{sbu},
            #{functions},
            #{siteArea},
            #{section},
            #{customerId},
            #{customerName},
            #{updateRemark},
            #{version}
        )
    </insert>

    <!-- 查詢客戶條件記錄上傳次數 -->
    <select id="selectCustomerConditionUploadCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM [APPGCL_Process].[dbo].[customer_condition_main_info]
        WHERE [site_area] = #{siteArea}
          AND [sbu] = #{sbu}
          AND [functions] = #{functions}
          AND [section] = #{section}
          AND [customer_id] = #{customerId}
    </select>

    <!-- 查詢客戶條件記錄詳情 -->
    <select id="selectCustomerConditionDetail" resultType="cc.mrbird.febs.customerCondition.entity.dto.CustomerConditionDto">
        SELECT
            m.[customer_condition_main_info_id] as customerConditionMainInfoId,
            m.[customer_condition_file_id] as customerConditionFileId,
            m.[customer_condition_file_name] as customerConditionFileName,
            m.[upload_date] as uploadDate,
            m.[upload_user_work_id] as uploadUserWorkId,
            m.[upload_user_name] as uploadUserName,
            m.[sbu] as sbu,
            m.[functions] as functions,
            m.[site_area] as siteArea,
            m.[section] as section,
            m.[customer_id] as customerId,
            m.[customer_name] as customerName,
            m.[update_remark] as updateRemark,
            m.[version] as version,
            f.[customer_condition_file_url] as fileUrl
        FROM [APPGCL_Process].[dbo].[customer_condition_main_info] m
        LEFT JOIN [APPGCL_Process].[dbo].[customer_condition_file] f ON m.[customer_condition_file_id] = f.[customer_condition_file_id]
        WHERE m.[customer_condition_main_info_id] = #{mainInfoId}
    </select>

    <!-- 保存客戶條件記錄summary文件 -->
    <insert id="insertCustomerConditionSummaryFile" parameterType="cc.mrbird.febs.customerCondition.entity.CustomerConditionSummaryFile" useGeneratedKeys="true" keyProperty="summaryFileId">
        INSERT INTO [APPGCL_Process].[dbo].[customer_condition_summary_file]
        (
            [summary_file_name],
            [upload_date]
        )
        VALUES
        (
            #{summaryFileName},
            SYSDATETIME()
        )
    </insert>

    <!-- 查詢最新的客戶條件記錄summary文件 -->
    <select id="selectLatestCustomerConditionSummaryFile" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionSummaryFile">
        SELECT TOP 1
            [summary_file_id] as summaryFileId,
            [summary_file_name] as summaryFileName,
            [upload_date] as uploadDate
        FROM [APPGCL_Process].[dbo].[customer_condition_summary_file]
        ORDER BY [upload_date] DESC
    </select>

    <!-- 查詢本月未更新記錄的客戶和對應的local cs郵箱 -->
    <select id="selectCustomersWithoutUpdateThisMonth" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionMail">
        SELECT DISTINCT
            m.[mail_id] as mailId,
            m.[user_name] as userName,
            m.[mail_address] as mailAddress,
            m.[customer_id] as customerId,
            m.[customer_name] as customerName,
            m.[leader_mail_id] as leaderMailId,
            m.[user_type] as userType
        FROM [APPGCL_Process].[dbo].[customer_condition_mail] m
        WHERE m.[user_type] = '1' -- 只選擇local cs郵箱
        AND m.[customer_id] > 0 -- 只選擇有客戶關聯的郵箱
        AND NOT EXISTS (
            -- 查詢本月已經更新記錄的客戶
            SELECT 1
            FROM [APPGCL_Process].[dbo].[customer_condition_main_info] i
            WHERE i.[customer_id] = m.[customer_id]
            AND YEAR(i.[upload_date]) = YEAR(GETDATE())
            AND MONTH(i.[upload_date]) = MONTH(GETDATE())
        )
    </select>

    <!-- 根據郵箱ID查詢郵箱信息 -->
    <select id="selectCustomerConditionMailById" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionMail">
        SELECT
            [mail_id] as mailId,
            [user_name] as userName,
            [mail_address] as mailAddress,
            [customer_id] as customerId,
            [customer_name] as customerName,
            [leader_mail_id] as leaderMailId,
            [user_type] as userType
        FROM [APPGCL_Process].[dbo].[customer_condition_mail]
        WHERE [mail_id] = #{mailId}
    </select>

    <!-- 批量查詢郵箱信息 -->
    <select id="selectCustomerConditionMailByIds" resultType="cc.mrbird.febs.customerCondition.entity.CustomerConditionMail">
        SELECT
            [mail_id] as mailId,
            [user_name] as userName,
            [mail_address] as mailAddress,
            [customer_id] as customerId,
            [customer_name] as customerName,
            [leader_mail_id] as leaderMailId,
            [user_type] as userType
        FROM [APPGCL_Process].[dbo].[customer_condition_mail]
        WHERE [mail_id] IN
        <foreach collection="list" item="mailId" open="(" separator="," close=")">
            #{mailId}
        </foreach>
    </select>

    <!-- 查詢客戶條件記錄詳細信息 -->
    <select id="selectCustomerConditionDto" resultType="cc.mrbird.febs.customerCondition.entity.dto.CustomerConditionDto">
        SELECT
            m.[customer_condition_main_info_id] as customerConditionMainInfoId,
            m.[customer_condition_file_id] as customerConditionFileId,
            m.[customer_condition_file_name] as customerConditionFileName,
            m.[upload_date] as uploadDate,
            m.[upload_user_work_id] as uploadUserWorkId,
            m.[upload_user_name] as uploadUserName,
            m.[sbu] as sbu,
            m.[functions] as functions,
            m.[site_area] as siteArea,
            m.[section] as section,
            m.[customer_id] as customerId,
            m.[customer_name] as customerName,
            m.[update_remark] as updateRemark,
            m.[version] as version,
            f.[customer_condition_file_url] as fileUrl
        FROM [APPGCL_Process].[dbo].[customer_condition_main_info] m
        LEFT JOIN [APPGCL_Process].[dbo].[customer_condition_file] f ON m.[customer_condition_file_id] = f.[customer_condition_file_id]
        <where>
            <if test="sbu != null and sbu != ''">
                AND m.[sbu] = #{sbu}
            </if>
            <if test="functions != null and functions != ''">
                AND m.[functions] = #{functions}
            </if>
            <if test="siteArea != null and siteArea != ''">
                AND m.[site_area] = #{siteArea}
            </if>
            <if test="section != null and section != ''">
                AND m.[section] = #{section}
            </if>
            <if test="customerId != null">
                AND m.[customer_id] = #{customerId}
            </if>
            <if test="customerName != null and customerName != ''">
                AND m.[customer_name] = #{customerName}
            </if>
            <if test="uploadUserWorkId != null and uploadUserWorkId != ''">
                AND m.[upload_user_work_id] = #{uploadUserWorkId}
            </if>
            <if test="uploadUserName != null and uploadUserName != ''">
                AND m.[upload_user_name] = #{uploadUserName}
            </if>
        </where>
        ORDER BY m.[upload_date] DESC
    </select>

</mapper>
