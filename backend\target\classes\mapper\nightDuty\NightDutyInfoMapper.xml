<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.nightDuty.dao.NightDutyInfoMapper">

    <select id="queryInfo" resultType="cc.mrbird.febs.nightDuty.entity.NightDutyInfo">
        select  A.ZYZG_UserName AS name,CONCAT(A.XC_PicUrl,A.XC_PicName01) AS picture1,CONCAT(A.XC_PicUrl,A.XC_PicName02) AS picture2,
               A.NG_Cause AS description,A.Ins_Date AS time,A.SF_NG AS NGStatus,
        B.SJWZ_Refer AS area,A.ZYZG_UserID AS workId,B.building AS building, B.floor AS floor,B.YCBM_Section AS department,B.BMZG_Head AS header
        from[APPGCL_Process].[dbo].[ZGCY_MainInfo] AS A Join [APPGCL_Process].[dbo].[ZGCY_PlaceInfo] AS B
        ON A.PlaceInfoID = B.id
        <where>
            <if test="date != null and date != ''">
                A.ZY_Date LIKE CONCAT ('%',#{date},'%')
            </if>
        </where>
        <if test="date != null and date != ''">
           order by A.PlaceInfoID asc
        </if>
    </select>

    <!--查詢簽核狀態-->
    <select id="queryStatus" resultType="java.lang.Integer">
        select COUNT(*) from [APPGCL_Process].[dbo].[ZGCY_PlaceInfo]
        <where>
            <if test="building != null and building != ''">
                building = #{building}
            </if>
            <if test="area != null and area != ''">
               and SJWZ_Refer = #{area}
            </if>
        </where>
    </select>

    <select id="queryArea" resultType="java.lang.String">
        select SJWZ_Refer from [APPGCL_Process].[dbo].[ZGCY_PlaceInfo]
        <where>
        <if test="building != null and building != ''">
            building = #{building}
        </if>
        </where>
        order by id asc
    </select>

    <!--查詢層數-->
    <select id="queryFloor" resultType="java.lang.String">
        select floor from [APPGCL_Process].[dbo].[ZGCY_PlaceInfo]
        <where>
            <if test="building != null and building != ''">
                building = #{building}
            </if>
        </where>
        order by id asc
    </select>

    <!--查詢責任部門-->
    <select id="queryDepartment" resultType="java.lang.String">
        select YCBM_Section from [APPGCL_Process].[dbo].[ZGCY_PlaceInfo]
        <where>
            <if test="building != null and building != ''">
                building = #{building}
            </if>
        </where>
        order by id asc
    </select>

    <!--查詢責任主管-->
    <select id="queryHeader" resultType="java.lang.String">
        select BMZG_Head from [APPGCL_Process].[dbo].[ZGCY_PlaceInfo]
        <where>
            <if test="building != null and building != ''">
                building = #{building}
            </if>
        </where>
        order by id asc
    </select>

    <!--查詢時間日期-->
    <select id="queryDate" resultType="java.lang.String">
        select Distinct ZY_Date from [APPGCL_Process].[dbo].[ZGCY_MainInfo]
    </select>
    <select id="queryInfoByBuilding" resultType="cc.mrbird.febs.nightDuty.entity.NightDutyInfo">
        select A.PlaceInfoID AS ID,A.ZYZG_UserName AS name,CONCAT(A.XC_PicUrl,A.XC_PicName01) AS picture1,CONCAT(A.XC_PicUrl,A.XC_PicName02) AS picture2,
        A.NG_Cause AS description,A.Ins_Date AS time,A.SF_NG AS NGStatus,
        B.SJWZ_Refer AS area,A.ZYZG_UserID AS workId,B.building AS building, B.floor AS floor,B.YCBM_Section AS department,B.BMZG_Head AS header
        from[APPGCL_Process].[dbo].[ZGCY_MainInfo] AS A Join [APPGCL_Process].[dbo].[ZGCY_PlaceInfo] AS B
        ON A.PlaceInfoID = B.id
        <where>
            <if test="date != null and date != ''">
                A.ZY_Date LIKE CONCAT ('%',#{date},'%')
            </if>
            <if test="s != null and s != ''">
               and B.building = #{s}
            </if>
        </where>
        <if test="date != null and date != ''">
            order by A.PlaceInfoID asc
        </if>
    </select>
</mapper>