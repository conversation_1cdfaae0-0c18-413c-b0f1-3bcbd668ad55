package cc.mrbird.febs.produceInspection.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表点检项目
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionInspectProjects {
    @ExcelIgnore
    private Integer id;
    @ExcelProperty("SBU")
    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;
    @ExcelProperty("機能")
    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;
    @ExcelProperty("課別")
    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;
    @ExcelProperty("綫體")
    @ApiModelProperty(value = "綫體", example = "V1", position = 4)
    private String line;
    @ExcelProperty("系列")
    @ApiModelProperty(value = "系列", example = "1700SKT", position = 5)
    private String series;
    @ExcelProperty("工站")
    @ApiModelProperty(value = "工站", example = "工站", position = 6)
    private String workStation;
    @ExcelProperty("設備名稱")
    @ApiModelProperty(value = "設備名稱", example = "設備名稱", position = 7)
    private String deviceName;
    @ExcelProperty("管制類別")
    @ApiModelProperty(value = "管制類別", example = "管制類別", position = 8)
    private String restraintCategory;
    @ExcelProperty("管制項目")
    @ApiModelProperty(value = "管制項目", example = "管制項目", position = 9)
    private String restraintProject;
    @ExcelProperty("設定值")
    @ApiModelProperty(value = "設定值", example = "設定值", position = 10)
    private String settingValue;
    @ExcelProperty("單位")
    @ApiModelProperty(value = "單位", example = "單位", position = 11)
    private String unit;
    @ExcelProperty("檢查方法")
    @ApiModelProperty(value = "檢查方法", example = "檢查方法", position = 12)
    private String inspectMethod;
    @ExcelIgnore
    private String remark;
    @ExcelProperty("頻率")
    @ApiModelProperty(value = "頻率", example = "10h/次", position = 13)
    private String frequence;
    @ExcelProperty("文件編號")
    @ApiModelProperty(value = "文件編號", example = "ABC-123", position = 14)
    private String fileNo;
    @ExcelProperty("文件ECN NO")
    @ApiModelProperty(value = "文件ECN NO", example = "ECN-456", position = 15)
    private String fileEcnNo;
    @ExcelProperty("文件REV.")
    @ApiModelProperty(value = "文件REV.", example = "A", position = 16)
    private String fileRev;
    @ExcelProperty("版次號")
    @ApiModelProperty(value = "版次號", example = "V1.0", position = 17)
    private String editionNo;
}