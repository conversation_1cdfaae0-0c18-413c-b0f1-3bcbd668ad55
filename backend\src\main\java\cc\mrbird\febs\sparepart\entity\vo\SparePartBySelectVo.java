package cc.mrbird.febs.sparepart.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SparePartBySelectVo {
    @ApiModelProperty(value = "線體",position = 1)
    private String productionLine;
    @ApiModelProperty(value = "設備名稱",position = 2)
    private String equipmentName;
    @ApiModelProperty(value = "備品名稱",position = 3)
    private String sparePartName;
    @ApiModelProperty(value = "壽命上限",position = 4)
    private String lifeLimit;
    @ApiModelProperty(value = "實際產量",position = 5)
    private String activeOutPut;
    @ApiModelProperty(value = "監控狀態",position = 6)
    private String state;
    @ApiModelProperty(value = "操作描述",position = 7)
    private String operation;
}
