<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.produceInspection.dao.ProduceInspectionMapper">
    <insert id="insertPersonnelData">
        INSERT INTO [H_APPGCL_Process].[dbo].[ProduceInspection_UserInfo] (
            [SBU],
            [functions],
            [section],
            [WorkId],
            [Name],
            [Type],
            [series],
            [line]
        ) VALUES (
                     #{sbu},
                     #{functions},
                     #{section},
                     #{workId},
                     #{name},
                     #{type},
                     #{series},
                     #{line}
                 )
    </insert>
    <insert id="insertInspectionItems">
        INSERT INTO [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects] (
            [SBU],
            [functions],
            [section],
            [line],
            [series],
            [workStation],
            [deviceName],
            [restraintCategory],
            [restraintProject],
            [settingValue],
            [unit],
            [inspectMethod],
            [remark],
            [frequence],
            [fileNo],
            [fileECN_No],
            [fileREV],
            [editionNo]
        ) VALUES (
                     #{sbu},
                     #{functions},
                     #{section},
                     #{line},
                     #{series},
                     #{workStation},
                     #{deviceName},
                     #{restraintCategory},
                     #{restraintProject},
                     #{settingValue},
                     #{unit},
                     #{inspectMethod},
                     #{remark},
                     #{frequence},
                     #{fileNo},
                     #{fileEcnNo},
                     #{fileRev},
                     #{editionNo}
                 )
    </insert>
    <insert id="insertPartNumber">
        INSERT INTO [H_APPGCL_Process].[dbo].[ProduceInspection_PartNumber] (
            SBU,
            functions,
            section,
            line,
            series,
            partNumber
        ) VALUES (
                     #{sbu},
                     #{functions},
                     #{section},
                     #{line},
                     #{series},
                     #{partNumber}
                 )
    </insert>

    <insert id="saveProduceInspectionPicUrl"
            parameterType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionPicUrl">
        INSERT INTO [H_APPGCL_Process].[dbo].[ProduceInspection_PicUrl] (
            mainInfoId,
            picUrl,
            picName,
            picOrder,
            ins_date
        ) VALUES (
            #{mainInfoId},
            #{picUrl},
            #{picName},
            #{picOrder},
            #{insDate}
            )
    </insert>

    <select id="queryByUserId" resultType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionUserInfo">
        SELECT
            [id]              as id,
            [SBU]             as sbu,
            [functions]       as functions,
            [section]         AS section,
            [WorkId]          as workId,
            [Name]            as name,
            [Type]            as type,
            [series]          as series,
            [line]            as line
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_UserInfo]
        WHERE WorkId=#{userID}
    </select>
    <select id="querySBU" resultType="java.lang.String">
        SELECT DISTINCT  SBU
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
    </select>
    <select id="queryFunctions" resultType="java.lang.String">
        SELECT DISTINCT  functions
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        WHERE SBU=#{sbu}
    </select>
    <select id="queryDiscern" resultType="java.lang.String">
        SELECT DISTINCT  section
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        WHERE SBU=#{sbu} AND functions=#{functions}
    </select>
    <select id="queryProduction" resultType="java.lang.String">
        SELECT DISTINCT  series
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        WHERE SBU=#{sbu} AND functions=#{functions} AND section=#{discern}
    </select>
    <select id="queryProductionLine" resultType="java.lang.String">
        SELECT DISTINCT  line
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        WHERE SBU=#{sbu} AND functions=#{functions} AND section=#{discern} AND series=#{production}
    </select>
    <select id="queryWorkStation" resultType="java.lang.String">
        SELECT DISTINCT  workStation
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        WHERE SBU=#{sbu} AND functions=#{functions} AND section=#{discern} AND series=#{production} AND line = #{line}
    </select>
    <select id="queryIsNotDJ" resultType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionNotInspect">
        SELECT [SBU]             as sbu,
        [functions]       as functions,
        [section] AS section,
        [series]          as series,
        [line]            as line
        FROM [H_APPGCL_Process].[dbo].[ProduceInspection_NotInspect]
        <where>
            SBU = #{sbu} AND functions = #{functions} AND section = #{section} AND series = #{series}
            AND line = #{line} AND  inspect_Date = #{date}
        </where>
    </select>
    <select id="queryProductionInspectionMainInfo"
            resultType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionMainInfo">
        SELECT
        [id]              as id,
        [SBU]             as sbu,
        [functions]       as functions,
        [section] AS section,
        [series]          as series,
        [line]            as line,
        [partNumber]   as partNumber,
        [produceClass]   as produceClass,
        [date]     as date,
        [remark]     as remark,
        [status]     as status,
        [examinerWorkId]     as examinerWorkId,
        [examinerName]   as examinerName,
        [reviewerName]     as reviewerName,
        [reviewerWorkId]  as reviewerWorkId,
        [reviewerSignDate]  as reviewerSignDate,
        [resultValue]  as resultValue,
        [startTime]  as startTime,
        [endTime]  as endTime,
        [projectRemark] as projectRemark
        FROM [H_APPGCL_Process].[dbo].[ProduceInspection_MainInfo]
        <where>
            <if test="sbu != null and sbu != ''">
                AND SBU = #{sbu}
            </if>
            <if test="functions != null and functions != ''">
                AND functions = #{functions}
            </if>
            <if test="section != null and section != ''">
                AND section = #{section}
            </if>
            <if test="series != null and series != ''">
                AND series = #{series}
            </if>
            <if test="line != null and line != ''">
                AND line = #{line}
            </if>
            <if test="partNumber != null and partNumber != ''">
                AND partNumber=#{partNumber}
            </if>
            <if test="produceClass != null and produceClass != ''">
                AND  produceClass = #{produceClass}
            </if>
            <if test="startTime != null and startTime != ''">
                AND  DateDIFF(day,date,#{startTime}) &lt;=0
            </if>
            <if test="endTime != null and endTime != ''">
                AND  DateDIFF(day,date,#{endTime}) >=0
            </if>
        </where>
        ORDER BY date DESC
    </select>
    <select id="queryProductionInspectionProjectData"
            resultType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionInspectProjects">
        SELECT
        [id]              as id,
        [SBU]             as sbu,
        [functions]       as functions,
        [section] AS section,
        [series]          as series,
        [line]            as line,
        [workStation]   as workStation,
        [deviceName]   as deviceName,
        [restraintCategory]     as restraintCategory,
        [restraintProject]     as restraintProject,
        [settingValue]     as settingValue,
        [unit]     as unit,
        [inspectMethod]   as inspectMethod,
        [remark]     as remark,
        [frequence]  as frequence,
        [fileNo]     as fileNo,
        [fileECN_No] as fileEcnNo,
        [fileREV]    as fileRev,
        [editionNo]  as editionNo
        FROM [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        <where>
            <if test="sbu != null and sbu != ''">
                AND SBU = #{sbu}
            </if>
            <if test="functions != null and functions != ''">
                AND functions = #{functions}
            </if>
            <if test="section != null and section != ''">
                AND section = #{section}
            </if>
            <if test="series != null and series != ''">
                AND series = #{series}
            </if>
            <if test="line != null and line != ''">
                AND line = #{line}
            </if>
            <if test="workStation != null and workStation != ''">
                AND workStation = #{workStation}
            </if>
            <if test="fileNo != null and fileNo != ''">
                AND fileNo = #{fileNo}
            </if>
            <if test="fileEcnNo != null and fileEcnNo != ''">
                AND [fileECN_No] = #{fileEcnNo}
            </if>
            <if test="fileRev != null and fileRev != ''">
                AND [fileREV] = #{fileRev}
            </if>
            <if test="editionNo != null and editionNo != ''">
                AND editionNo = #{editionNo}
            </if>
        </where>
    </select>
    <select id="queryProduceInspectionPartNumberData"
            resultType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionPartNumber">
        SELECT
        [id]              as id,
        [SBU]             as sbu,
        [functions]       as functions,
        [section] AS section,
        [series]          as series,
        [line]            as line,
        [partNumber]   as partNumber
        FROM [H_APPGCL_Process].[dbo].[ProduceInspection_PartNumber]
        <where>
            <if test="sbu != null and sbu != ''">
                AND SBU = #{sbu}
            </if>
            <if test="functions != null and functions != ''">
                AND functions = #{functions}
            </if>
            <if test="section != null and section != ''">
                AND section = #{section}
            </if>
            <if test="series != null and series != ''">
                AND series = #{series}
            </if>
            <if test="line != null and line != ''">
                AND line = #{line}
            </if>
        </where>
    </select>

    <select id="queryProduceInspectionUserInfoData"
            resultType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionUserInfo">
        SELECT
        [id]              as id,
        [SBU]             as sbu,
        [functions]       as functions,
        [section] AS section,
        [WorkId]          as workId,
        [Name]            as name,
        [Type]   as type,
        [series]          as series,
        [line]            as line
        FROM [H_APPGCL_Process].[dbo].[ProduceInspection_UserInfo]
        <where>
            <if test="sbu != null and sbu != ''">
                AND SBU = #{sbu}
            </if>
            <if test="functions != null and functions != ''">
                AND functions = #{functions}
            </if>
            <if test="section != null and section != ''">
                AND section = #{section}
            </if>
            <if test="workId != null and workId != ''">
                AND WorkId = #{workId}
            </if>
            <if test="name != null and name != ''">
                AND Name = #{name}
            </if>
            <if test="type != null and type != ''">
                AND Type = #{type}
            </if>
            <if test="series != null and series != ''">
                AND series = #{series}
            </if>
            <if test="line != null and line != ''">
                AND line = #{line}
            </if>
        </where>
    </select>
    <select id="getPicCount" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_PicUrl]
        WHERE mainInfoId=#{mainInfoId}
    </select>
    <select id="queryPicsByMainInfoId" resultType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionPicUrl"
            parameterType="java.lang.String">
        SELECT
            [id] as id,
            [mainInfoId] as mainInfoId,
            [picUrl] as picUrl,
            [picName] as picName,
            picOrder as picOrder,
            ins_date as insDate
        FROM
            [H_APPGCL_Process].[dbo].[ProduceInspection_PicUrl]
        WHERE mainInfoId=#{mainInfoId}
    </select>


    <update id="updatePartNumber" parameterType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionPartNumber">
        UPDATE [H_APPGCL_Process].[dbo].[ProduceInspection_PartNumber]
        <set>
            <if test="sbu != null and sbu != ''">
                SBU = #{sbu},
            </if>
            <if test="functions != null and functions != ''">
                functions = #{functions},
            </if>
            <if test="section != null and section != ''">
                section = #{section},
            </if>
            <if test="line != null and line != ''">
                line = #{line},
            </if>
            <if test="series != null and series != ''">
                series = #{series},
            </if>
            <if test="partNumber != null and partNumber != ''">
                partNumber = #{partNumber}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deletePartNumber" parameterType="java.lang.Integer">
        DELETE FROM [H_APPGCL_Process].[dbo].[ProduceInspection_PartNumber]
        WHERE id = #{id}
    </delete>

    <!-- 更新人员信息 -->
    <update id="updateUserInfo" parameterType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionUserInfo">
        UPDATE [H_APPGCL_Process].[dbo].[ProduceInspection_UserInfo]
        <set>
            <if test="sbu != null and sbu != ''">
                SBU = #{sbu},
            </if>
            <if test="functions != null and functions != ''">
                functions = #{functions},
            </if>
            <if test="section != null and section != ''">
                section = #{section},
            </if>
            <if test="workId != null and workId != ''">
                WorkId = #{workId},
            </if>
            <if test="name != null and name != ''">
                Name = #{name},
            </if>
            <if test="type != null and type != ''">
                Type = #{type},
            </if>
            <if test="series != null and series != ''">
                series = #{series},
            </if>
            <if test="line != null and line != ''">
                line = #{line}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除人员信息 -->
    <delete id="deleteUserInfo" parameterType="java.lang.Integer">
        DELETE FROM [H_APPGCL_Process].[dbo].[ProduceInspection_UserInfo]
        WHERE id = #{id}
    </delete>

    <!-- 更新点检项目 -->
    <update id="updateInspectProject" parameterType="cc.mrbird.febs.produceInspection.entity.ProduceInspectionInspectProjects">
        UPDATE [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        <set>
            <if test="sbu != null and sbu != ''">
                SBU = #{sbu},
            </if>
            <if test="functions != null and functions != ''">
                functions = #{functions},
            </if>
            <if test="section != null and section != ''">
                section = #{section},
            </if>
            <if test="line != null and line != ''">
                line = #{line},
            </if>
            <if test="series != null and series != ''">
                series = #{series},
            </if>
            <if test="workStation != null and workStation != ''">
                workStation = #{workStation},
            </if>
            <if test="deviceName != null and deviceName != ''">
                deviceName = #{deviceName},
            </if>
            <if test="restraintCategory != null and restraintCategory != ''">
                restraintCategory = #{restraintCategory},
            </if>
            <if test="restraintProject != null and restraintProject != ''">
                restraintProject = #{restraintProject},
            </if>
            <if test="settingValue != null and settingValue != ''">
                settingValue = #{settingValue},
            </if>
            <if test="unit != null and unit != ''">
                unit = #{unit},
            </if>
            <if test="inspectMethod != null and inspectMethod != ''">
                inspectMethod = #{inspectMethod},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="frequence != null and frequence != ''">
                frequence = #{frequence},
            </if>
            <if test="fileNo != null and fileNo != ''">
                fileNo = #{fileNo},
            </if>
            <if test="fileEcnNo != null and fileEcnNo != ''">
                [fileECN_No] = #{fileEcnNo},
            </if>
            <if test="fileRev != null and fileRev != ''">
                [fileREV] = #{fileRev},
            </if>
            <if test="editionNo != null and editionNo != ''">
                editionNo = #{editionNo}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除点检项目 -->
    <delete id="deleteInspectProject" parameterType="java.lang.Integer">
        DELETE FROM [H_APPGCL_Process].[dbo].[ProduceInspection_InspectProjects]
        WHERE id = #{id}
    </delete>

</mapper>