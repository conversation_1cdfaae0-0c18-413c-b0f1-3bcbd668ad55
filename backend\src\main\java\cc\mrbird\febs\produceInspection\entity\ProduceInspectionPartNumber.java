package cc.mrbird.febs.produceInspection.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表料号信息
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionPartNumber {
    @ExcelIgnore
    private Integer id;
    @ExcelProperty("SBU")
    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;
    @ExcelProperty("機能")
    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;
    @ExcelProperty("課別")
    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;
    @ExcelProperty("綫體")
    @ApiModelProperty(value = "綫體", example = "V1", position = 4)
    private String line;
    @ExcelProperty("系列")
    @ApiModelProperty(value = "系列", example = "1700SKT", position = 5)
    private String series;
    @ExcelProperty("產品料號")
    @ApiModelProperty(value = "料號", example = "P5AA1700V2-01", position = 6)
    private String partNumber;
}