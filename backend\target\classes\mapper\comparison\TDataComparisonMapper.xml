<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.comparison.dao.TDataComparisonMapper">
        <insert id="insertComparison">
        USE Schedule
        INSERT INTO t_data_comparison(industry,enginery,production,productionLine,classNum,moduleNo,abnormalState,
            abnormalItems,processingResults,spotChecker,classInfo,recordDate,fillingDate,productionNum,fillingTime,saveDate)
            values (#{industry},#{enginery},#{production},#{productionLine},#{classNum},#{moduleNo},#{abnormalState},#{abnormalItems}
        ,#{processingResults},#{spotChecker},#{classInfo},#{recordDate},#{fillingDate},#{productionNum},#{fillingTime},#{saveDate})
        </insert>
        <update id="updateByGroupLeader" >
            USE Schedule
             <foreach collection="groupLeader" item="date" >
                 update  t_data_comparison
                <set>
                    groupLeader =#{date.groupLeader}
                </set>
            <where>
                Id = #{date.Id}
            </where>
             </foreach>
        </update>

        <select id="selectComparison" resultType="cc.mrbird.febs.comparison.entity.TDataComparison">
                USE Schedule
                SELECT * FROM t_data_comparison
                <where>
                <if test="production !=null and production !=''">
                        AND  production =#{production}
                </if>
                <if test="productionLine !=null and productionLine !=''">
                        AND productionLine =#{productionLine}
                </if>
                <if test="classInfo !=null and classInfo !=''">
                        AND classInfo = #{classInfo}
                </if>
                <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                        AND recordDate &gt;= #{startDate} AND recordDate &lt;= #{endDate}
                </if>
                <if test="enginery !=null and enginery !=''">
                        AND enginery =#{enginery}
                </if>
                    <if test="industry !=null and industry !=''">
                        AND industry =#{industry}
                    </if>
                 </where>
            ORDER BY  Id DESC
    </select>
        <select id="selectComparisonByGetDate" resultType="cc.mrbird.febs.comparison.entity.TDataComparison">
                USE Schedule
                SELECT * FROM t_data_comparison WHERE recordDate = CONVERT (date,getDate())  AND production = #{production} AND productionLine = #{productionLine} AND industry=#{pl}
        </select>
    <select id="selectComparisonByAbnormal" resultType="cc.mrbird.febs.comparison.entity.TDataComparison">
        USE Schedule
        SELECT
        COUNT(productionLine) AS total,
        recordDate,productionNum,classNum,fillingDate,moduleNo,abnormalState,
        abnormalItems,processingResults,spotChecker,groupLeader
        FROM t_data_comparison
        <where>
            <if test="production !=null and production !=''">
                AND  production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND productionLine =#{productionLine}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND classInfo = #{classInfo}
            </if>
            <if test="recordDate !=null and recordDate !='' and recordDate !=null and recordDate !=''">
                AND recordDate = #{recordDate}
            </if>
            <if test="industry !=null and industry !=''">
                AND industry = #{industry}
            </if>
            AND    abnormalState = '是'
        </where>
        GROUP BY
        productionLine,production,recordDate,productionNum,classNum,fillingDate,moduleNo,
        abnormalState,abnormalItems,processingResults,spotChecker,groupLeader
    </select>
    <select id="selectAchievementRate" resultType="java.lang.String">
        SELECT
                 CASE
                     WHEN   count(classNum)  = 5		THEN '100%'
                     WHEN   COUNT(classNum) = 4		    THEN '80%'
                     WHEN   COUNT(classNum)  = 3		THEN '60%'
                     WHEN   COUNT(classNum)  = 2		THEN '40%'
                     WHEN   COUNT(classNum)  = 1		THEN '20%'
                     ELSE   '0%'  END AS achievementRate
        FROM t_data_comparison
        WHERE
        production = #{production} AND productionLine=#{productionLine}
        AND   recordDate =  #{recordDate} AND industry=#{industry} AND classInfo =#{classInfo}
        GROUP BY production, productionLine
    </select>

        <delete id="delete">
    delete from t_data_comparison where Id=#{id}
    </delete>
        <select id="selectPartNumberByPL" resultType="java.lang.String">
            USE Schedule
            SELECT DISTINCT partNumber FROM t_configure_partNumber WHERE pl=#{pl} AND production=#{production} AND productionLine=#{productionLine}
    </select>
</mapper>