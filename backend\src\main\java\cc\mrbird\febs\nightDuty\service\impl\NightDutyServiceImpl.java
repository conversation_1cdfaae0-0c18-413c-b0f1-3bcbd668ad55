package cc.mrbird.febs.nightDuty.service.impl;

import cc.mrbird.febs.nightDuty.dao.NightDutyMapper;
import cc.mrbird.febs.nightDuty.service.NightDutyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NightDutyServiceImpl implements NightDutyService {

    @Autowired
    private NightDutyMapper nightDutyMapper;

    /**
     * 批量删除人员信息
     * @param ids
     */
    public void deletePerson(List<Long> ids) {

        for (int i = 0; i < ids.size(); i++) {
            long id = ids.get(i);
            nightDutyMapper.deletePerson(id);
        }
    }
}
