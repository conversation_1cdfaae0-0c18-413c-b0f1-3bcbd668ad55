package cc.mrbird.febs.newo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "V_Date_Statement")
public class GL {
    @TableField(value = "Enginery")
    private String Enginery;
    @TableField(value = "Discern")
    private String Discern;
    @TableField(value = "Production")
    private String Production;
    @TableField(value = "Production_TD")
    private String Production_TD;
    @TableField(value = "ProductionLine")
    private String ProductionLine;
    @TableField(value = "SectionManagerName")
    private String SectionManagerName;
    @TableField(value = "SectionManagerNumber")
    private String SectionManagerNumber;
    @TableField(value = "GroupLeaderName")
    private String GroupLeaderName;
    @TableField(value = "GroupLeaderNumber")
    private String GroupLeaderNumber;
    @TableField(value = "LineLeaderName")
    private String LineLeaderName;
    @TableField(value = "LineLeaderNumber")
    private String LineLeaderNumber;
    @TableField(value = "ClassInfo")
    private String ClassInfo;
    @TableField(value = "line")
    private String line;
    @TableField(value = "SCSL_QOP")
    private String SCSL_QOP;
    @TableField(value = "CPLH_PFN")
    private String CPLH_PFN;
    @TableField(value = "KHLH_CMN")
    private String KHLH_CMN;
    @TableField(value = "GLBH_WON")
    private String GLBH_WON;
    @TableField(value = "DC_SCDC")
    private String DC_SCDC;
    @TableField(value = "RUKUDANHAO")
    private String RUKUDANHAO;
    @TableField(value = "DATE_SCRQ")
    private String DATE_SCRQ;
    @TableField(value = "DATE_TDRQ")
    private String DATE_TDRQ;
    @TableField(value = "KHMC_CN")
    private String KHMC_CN;



}
