package cc.mrbird.febs.agv.controller;

import cc.mrbird.febs.agv.entity.dto.AgvDto;
import cc.mrbird.febs.agv.param.AgvQueryParam;
import cc.mrbird.febs.agv.service.AGVService;
import cc.mrbird.febs.common.domain.FebsResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:08
 */
@Api(tags = "AGV小车")
@RequestMapping("/AGV")
@Slf4j
@RestController
@ResponseBody
public class AGVController {
    private final AGVService agvService;

    @Autowired
    public AGVController(AGVService agvService) {
        this.agvService = agvService;
    }

    // 查詢AGV
    @ApiOperation(value = "查詢AGV", httpMethod = "POST")
    @PostMapping("/queryAgv")
    public FebsResponse queryAgv(@RequestBody AgvQueryParam agvQueryParam) {
        List<AgvDto> list;
        try {
            list = agvService.queryAgv(agvQueryParam);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數" + agvQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }
}
