package cc.mrbird.febs.comparison.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_data_comparison")
public class InsertDto {
//    private static final long serialVersionUID = 1L;
//    @TableId(value = "Id",type= IdType.AUTO)
//    @ApiModelProperty(value = "表自增主鍵Id,插入和更新用",required = false)
//    private Integer Id;
    /*    @ApiModelProperty(value = "事業處",required = true ,position = 1)
        private String industry;*/
    @ApiModelProperty(value = "事業處")
    private String industry;
    @ApiModelProperty(value = "成品料號",required = true,position = 1)
    private String productionNum ;
    @ApiModelProperty(value = "課別",required = true,position = 2)
    private String enginery;
    @ApiModelProperty(value = "系列",required = true,position = 3)
    private String production;
    @ApiModelProperty(value = "線體",required = true,position = 4)
    private String productionLine;
    @ApiModelProperty(value = "課數",required = true,position = 5)
    private String classNum;
    @ApiModelProperty(value = "模號",required = true,position = 6)
    private String moduleNo;
    @ApiModelProperty(value = "是否異常",required = true,position = 7)
    private String abnormalState;
    @ApiModelProperty(value = "異常項目",required = true,position = 8)
    private String abnormalItems;
    @ApiModelProperty(value = "處理結果",required = true,position = 9)
    private String processingResults;
    @ApiModelProperty(value = "點檢人簽名",required = true,position = 10)
    private String spotChecker;
    @ApiModelProperty(value = "班別",required = true,position = 13)
    private String classInfo;
    @ApiModelProperty(value = "日期",required = true,position = 14)
    private String recordDate;
    @ApiModelProperty(value = "線長創建表單的時間",required = true,position = 15)
    private String fillingDate;
    @ApiModelProperty(value = "填表時機",required = true,position = 16)
    private String fillingTime;
    @ApiModelProperty(value = "保存时间",required = true,position = 17)
    private String saveDate;
}
