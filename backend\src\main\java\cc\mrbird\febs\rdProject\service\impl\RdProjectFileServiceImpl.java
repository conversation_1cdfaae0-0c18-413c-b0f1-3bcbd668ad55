package cc.mrbird.febs.rdProject.service.impl;

import cc.mrbird.febs.rdProject.dao.RdProjectFileMapper;
import cc.mrbird.febs.rdProject.entity.RdProjectFile;
import cc.mrbird.febs.rdProject.service.RdProjectFileService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Author: AI Assistant
 * Date: 2025/7/22
 * Time: 10:49
 * 研究項目協同管理的任務附件服務實現類
 */
@Service
public class RdProjectFileServiceImpl implements RdProjectFileService {

    @Autowired
    private RdProjectFileMapper rdProjectFileMapper;

    // 文件保存根路径，可以通过配置文件配置
    private static final String BASE_FILE_PATH = "D:/rdProjectFile";

    @Override
    @DSTransactional
    public void saveAttachments(Long taskId, List<MultipartFile> attachments) throws IOException {
        if (attachments == null || attachments.isEmpty()) {
            return;
        }

        // 获取当前日期，用于创建年月文件夹
        Date uploadDate = new Date();
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        String year = yearFormat.format(uploadDate);
        String month = monthFormat.format(uploadDate);

        // 构建年月文件夹路径
        String yearMonthPath = BASE_FILE_PATH + File.separator + year + File.separator + month;
        File yearMonthDir = new File(yearMonthPath);
        if (!yearMonthDir.exists()) {
            yearMonthDir.mkdirs();
        }

        // 批量保存文件
        for (MultipartFile file : attachments) {
            if (file.isEmpty()) {
                continue;
            }

            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                continue;
            }

            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                fileExtension = originalFilename.substring(lastDotIndex);
            }

            // 生成随机文件名
            String randomFileName = UUID.randomUUID().toString() + fileExtension;

            // 保存文件到本地
            File destFile = new File(yearMonthPath + File.separator + randomFileName);
            file.transferTo(destFile);

            // 保存文件记录到数据库
            RdProjectFile rdProjectFile = new RdProjectFile();
            rdProjectFile.setFileName(originalFilename); // 保存原始文件名
            rdProjectFile.setFilePath(destFile.getAbsolutePath()); // 保存完整的文件路径
            rdProjectFile.setTaskId(taskId);

            rdProjectFileMapper.insertRdProjectFile(rdProjectFile);
        }
    }

    @Override
    public List<RdProjectFile> getFilesByTaskId(Long taskId) {
        return rdProjectFileMapper.selectRdProjectFileByTaskId(taskId);
    }

    @Override
    @DSTransactional
    public void deleteFileById(Long fileId) throws IOException {
        // 先查询文件信息
        RdProjectFile rdProjectFile = rdProjectFileMapper.selectRdProjectFileById(fileId);
        if (rdProjectFile != null) {
            // 删除本地文件
            File localFile = new File(rdProjectFile.getFilePath());
            if (localFile.exists()) {
                localFile.delete();
            }

            // 删除数据库记录
            rdProjectFileMapper.deleteRdProjectFileById(fileId);
        }
    }

    @Override
    @DSTransactional
    public void deleteFilesByTaskId(Long taskId) throws IOException {
        // 先查询所有相关文件
        List<RdProjectFile> files = rdProjectFileMapper.selectRdProjectFileByTaskId(taskId);
        
        // 删除本地文件
        for (RdProjectFile file : files) {
            File localFile = new File(file.getFilePath());
            if (localFile.exists()) {
                localFile.delete();
            }
        }

        // 删除数据库记录
        rdProjectFileMapper.deleteRdProjectFileByTaskId(taskId);
    }
}
