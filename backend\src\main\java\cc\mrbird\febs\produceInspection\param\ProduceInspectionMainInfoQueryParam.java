package cc.mrbird.febs.produceInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表主表信息查询参数
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionMainInfoQueryParam {

    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;

    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;

    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;

    @ApiModelProperty(value = "綫體", example = "V1", position = 4)
    private String line;

    @ApiModelProperty(value = "系列", example = "1700SKT", position = 5)
    private String series;

    @ApiModelProperty(value = "料號", example = "P5AA1700V2-01", position = 6)
    private String partNumber;

    @ApiModelProperty(value = "生產班別", example = "白班", position = 7)
    private String produceClass;

    @ApiModelProperty(value = "點檢日期", example = "2024-03-19", position = 8)
    private String date;

    @ApiModelProperty(value = "點檢狀態", example = "1", position = 9)
    private Integer status;

    @ApiModelProperty(value = "點檢人工號", example = "EMP001", position = 10)
    private String examinerWorkId;

    @ApiModelProperty(value = "點檢人名字", example = "張三", position = 11)
    private String examinerName;

    @ApiModelProperty(value = "確認人名字", example = "李四", position = 12)
    private String reviewerName;

    @ApiModelProperty(value = "確認人工號", example = "EMP002", position = 13)
    private String reviewerWorkId;

    @ApiModelProperty(value = "點檢開始時間", example = "2024-03-13", position = 14)
    private String startTime;

    @ApiModelProperty(value = "點檢結束時間", example = "2024-03-19", position = 15)
    private String endTime;
}