package cc.mrbird.febs.nightDuty.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NightDutyPersonVO implements Serializable {

    private long id;


    @ApiModelProperty("厂区")
    private String factory;


    @ApiModelProperty("楼栋")
    //A4-A5
    private String building;


    @ApiModelProperty("值夜日期")
    //日期
    private String date;


    @ApiModelProperty("工号")
    //工号
    private String workId;


    @ApiModelProperty("名字")
    //名字
    private String name;
}
