package cc.mrbird.febs.agv.controller;

import cc.mrbird.febs.agv.entity.AGVLogList;
import cc.mrbird.febs.agv.param.AGVTaskListQueryParam;
import cc.mrbird.febs.agv.param.AgvLogListQueryParam;
import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.agv.entity.AGVTaskList;
import cc.mrbird.febs.agv.service.AGVTaskListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@Api(tags = "AGV小车")
@RestController
@RequestMapping("/agv/tasks")
public class AGVTaskListController {
    private final AGVTaskListService agvTaskListService;

    @Autowired
    public AGVTaskListController(AGVTaskListService agvTaskListService) {
        this.agvTaskListService = agvTaskListService;
    }

    // 查询AGV任务
    @ApiOperation(value = "查询AGV任务", httpMethod = "POST")
    @PostMapping("/queryAgvTask")
    public FebsResponse queryAgvTask(@RequestBody AGVTaskListQueryParam agvTaskListQueryParam) {
        List<AGVTaskList> tasks;
        try {
            tasks = agvTaskListService.queryAGVTaskList(agvTaskListQueryParam); // 这里可以根据需要修改为带参数的查询
        } catch (Exception e) {
            return new FebsResponse().code("500").message("错误参数" + agvTaskListQueryParam);
        }
        return new FebsResponse().code("200").data(tasks);
    }
}