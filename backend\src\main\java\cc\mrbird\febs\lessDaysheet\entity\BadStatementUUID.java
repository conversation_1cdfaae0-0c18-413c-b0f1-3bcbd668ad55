package cc.mrbird.febs.lessDaysheet.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_data_badStatement_uuid")
public class BadStatementUUID {
    @JsonIgnore
    private String uuid;
    @ApiModelProperty(value = "不良項目",position = 1)
    private String badItem;
    @ApiModelProperty(value = "管理目標",position = 2)
    private String mo;
    @ApiModelProperty(value = "第一節課不良數",position = 3)
    private String badNum1;
    @ApiModelProperty(value = "第二節課不良數",position = 4)
    private String badNum2;
    @ApiModelProperty(value = "第三節課不良數",position = 5)
    private String badNum3;
    @ApiModelProperty(value = "第四節課不良數",position = 6)
    private String badNum4;
    @ApiModelProperty(value = "第五節課不良數",position = 7)
    private String badNum5;
    @ApiModelProperty(value = "備註",position = 8)
    private String remark;
    @ApiModelProperty(value = "不良项目统计",position = 9)
    private String badTotal;
}
