package cc.mrbird.febs.produceInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表点检项目查询参数
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionInspectProjectsQueryParam {

    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;

    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;

    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;

    @ApiModelProperty(value = "綫體", example = "V1", position = 4)
    private String line;

    @ApiModelProperty(value = "系列", example = "1700SKT", position = 5)
    private String series;

    @ApiModelProperty(value = "工站", example = "工站", position = 6)
    private String workStation;

    @ApiModelProperty(value = "設備名稱", example = "設備名稱", position = 7)
    private String deviceName;

    @ApiModelProperty(value = "管制類別", example = "管制類別", position = 8)
    private String restraintCategory;

    @ApiModelProperty(value = "管制項目", example = "管制項目", position = 9)
    private String restraintProject;

    @ApiModelProperty(value = "設定值", example = "設定值", position = 10)
    private String settingValue;

    @ApiModelProperty(value = "單位", example = "單位", position = 11)
    private String unit;

    @ApiModelProperty(value = "檢查方法", example = "檢查方法", position = 12)
    private String inspectMethod;

    @ApiModelProperty(value = "頻率", example = "10h/次", position = 13)
    private String frequence;

    @ApiModelProperty(value = "文件編號", example = "ABC-123", position = 14)
    private String fileNo;

    @ApiModelProperty(value = "文件ECN NO", example = "ECN-456", position = 15)
    private String fileEcnNo;

    @ApiModelProperty(value = "文件REV.", example = "A", position = 16)
    private String fileRev;

    @ApiModelProperty(value = "版次號", example = "V1.0", position = 17)
    private String editionNo;
}