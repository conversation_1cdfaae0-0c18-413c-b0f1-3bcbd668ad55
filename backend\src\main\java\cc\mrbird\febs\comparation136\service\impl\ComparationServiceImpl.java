package cc.mrbird.febs.comparation136.service.impl;

import cc.mrbird.febs.comparation136.VO.CheckComparation136VO;
import cc.mrbird.febs.comparation136.VO.QueryIdComparation136VO;
import cc.mrbird.febs.comparation136.VO.QueryInformationVO;
import cc.mrbird.febs.comparation136.dao.Comparation136Mapper;
import cc.mrbird.febs.comparation136.entity.*;
import cc.mrbird.febs.comparation136.service.ComparationService;
import cc.mrbird.febs.comparation136.result.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ComparationServiceImpl implements ComparationService {

    @Autowired
    private Comparation136Mapper comparation136Mapper;

   /* *//**
     * 分页查询
     * @param comparation136QueryPage
     * @return
     *//*
    public PageResult pageQuery(Comparation136QueryPage comparation136QueryPage) {
        PageHelper.startPage(comparation136QueryPage.getPage(), comparation136QueryPage.getPageSize());
        Page<QueryInformationVO> page = comparation136Mapper.pageQucery(comparation136QueryPage);
        return new PageResult(page.getTotal(),page.getResult());
    }*/

    /**
     * 通过id查询数据
     * @param id
     * @return
     */
    public QueryIdComparation136VO getByIdWithInformation(Long id) {
        QueryIdComparation136VO queryIdComparation136VO=comparation136Mapper.selectById(id);
        List<ImageInformation> imageInformation = comparation136Mapper.queryImageInformation(id);

        queryIdComparation136VO.setImageInformation(imageInformation);
        return queryIdComparation136VO;
    }

    /**
     * 新增136对比信息
     * @param saveComparationInformation
     */
    public void saveComparationInformation(SaveComparationInformation saveComparationInformation) {
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        saveComparationInformation.setIns_Date(localDateTime.format(formatter));
      comparation136Mapper.saveInformation(saveComparationInformation);
      System.out.println("新增數據成功");
/*      Long id = comparation136Mapper.QueryId(saveComparationInformation);
      *//*通过id更新写入信息为PC*//*
        comparation136Mapper.updateWriter(id);
      System.out.println(id);
      comparation136Mapper.updateStatus(id);
    }*/}

    /**
     * 签核相关接口
     * @param
     * @return
     */
    public CheckComparation136VO checkComparation136(CheckComparation136 checkComparation136) {

        //通過前端傳過來的id查詢該筆單據的簽核狀態
        Integer status = comparation136Mapper.selectStatusById(checkComparation136);
        Long id = checkComparation136.getId();
        //根據查詢到的狀態做判斷，如果status=1，則需要品保簽核，如果狀態為2，則需要組長簽單，如果狀態為3，則表示簽核已經完成
        CheckComparation136VO checkComparation136VO = new CheckComparation136VO();
        TDInformation tdInformation = new TDInformation();
        if (status == 1) {
            System.out.println("請品保完成簽核");
            //如果品保簽核通過則改變簽核狀態為2，表示等待組長簽核狀態
            if (checkComparation136.getStatus().equals("true") ) {
                status = 2;
                comparation136Mapper.updateCheckStatus(id, status);
                checkComparation136VO = CheckComparation136VO.builder()
                        .STATUS(status)
                        .build();
                //簽核被駁回，改變狀態要求填寫被駁回理由
            } else if (checkComparation136.getStatus().equals("false") ) {
                status = 4;
                comparation136Mapper.updateCheckStatus(id, status);
                System.out.println("請輸入退單理由");
                String description = checkComparation136.getTD_TDYY();
                //獲取退單人的信息，向退單表中插入一條數據
                String name = comparation136Mapper.selectTDRName(id);
                String workId = comparation136Mapper.selectTDRWorkId(id);
                String type = "IPQC退單";
                //獲取時間日期
                LocalDateTime localDateTime = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                tdInformation.setMainInfoID(id);
                tdInformation.setTD_type(type);
                tdInformation.setTD_TDYY(description);
                tdInformation.setTD_DATA(localDateTime.format(formatter));
                tdInformation.setTD_Name(name);
                tdInformation.setTD_WorkId(workId);
                comparation136Mapper.insertTDInformation(tdInformation);
                checkComparation136VO.setSTATUS(status);
                checkComparation136VO.setTD_TDYY(description);
                checkComparation136VO.setTD_Name(name);
                checkComparation136VO.setTD_Type(type);
                checkComparation136VO.setTD_WorkId(workId);
            }
        }
        if (status == 2) {
            System.out.println("請組長完成簽核");
            if(checkComparation136.getStatus2().equals("null")){
                System.out.println("請組長簽核");
            }
            if (checkComparation136.getStatus2().equals("true") ) {
                status = 3;
                comparation136Mapper.updateCheckStatus(id, status);
            } else if (checkComparation136.getStatus2().equals("false") ) {
                status = 4;
                comparation136Mapper.updateCheckStatus(id, status);
                String description = checkComparation136.getTD_TDYY();
                //獲取退單人的信息，向退單表中插入一條數據
                String name = comparation136Mapper.selectTDZZName(id);
                String workId = comparation136Mapper.selectTDZZWorkId(id);
                String type = "組長退單";
                LocalDateTime localDateTime = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                tdInformation.setMainInfoID(id);
                tdInformation.setTD_type(type);
                tdInformation.setTD_TDYY(description);
                tdInformation.setTD_DATA(localDateTime.format(formatter));
                tdInformation.setTD_Name(name);
                tdInformation.setTD_WorkId(workId);
                comparation136Mapper.insertTDInformation(tdInformation);
                checkComparation136VO.setSTATUS(status);
                checkComparation136VO.setTD_TDYY(description);
                checkComparation136VO.setTD_Name(name);
                checkComparation136VO.setTD_Type(type);
                checkComparation136VO.setTD_WorkId(workId);
            }
        }
        return checkComparation136VO;
    }

    /**
     * 被驳回之后重新编辑
     * @param saveComparationInformation
     */
    public void updateComparationInformation(SaveComparationInformation saveComparationInformation) {
        comparation136Mapper.updateInformation(saveComparationInformation);
        System.out.println("重新編輯成功");
        //重新編輯完成之後，重新更改狀態為帶簽核狀態
        Long id = saveComparationInformation.getId();
        comparation136Mapper.updateStatus(id);
    }
}
