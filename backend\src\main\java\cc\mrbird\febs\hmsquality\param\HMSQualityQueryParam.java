package cc.mrbird.febs.hmsquality.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:20
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class HMSQualityQueryParam {
    @ApiModelProperty(value = "記錄人員ID,PK200916|G4617224",example = "PK200916",position = 0)
    private String userID;
    @ApiModelProperty(value = "系列,4189|LGA1700",example = "4189",position = 1)
    private String production;
    @ApiModelProperty(value = "線體,P3/P4|V3",example = "P3/P4",position = 2)
    private String productionLine;
    @ApiModelProperty(value = "日期代碼,4B137|4AX",example = "4B137",position = 3)
    private String dateCode;
    @ApiModelProperty(value = "料號,PE41897-01NK5-1H|PE17007-11AA0-1H",example = "PE41897-01NK5-1H",position = 4)
    private String pfn;
    @ApiModelProperty(value = "起始時間,2024-11-06|2024-08-16|2024-09-10",example = "2024-11-06",position = 7)
    private String startDate;
    @ApiModelProperty(value = "截止時間,2024-11-06|2024-08-16|2024-09-10",example = "2024-11-06",position = 8)
    private String endDate;
}
