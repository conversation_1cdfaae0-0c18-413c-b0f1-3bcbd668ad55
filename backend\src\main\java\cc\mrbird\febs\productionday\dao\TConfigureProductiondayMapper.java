package cc.mrbird.febs.productionday.dao;

import cc.mrbird.febs.productionday.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("mssql")

public interface TConfigureProductiondayMapper extends BaseMapper<TConfigureProductionday> {

    void insertConfig(TConfigureProductionday tConfigureProductionday);

    List<ProductDayInfo> queryLinesInfo(String cardId);

    List<TConfigureProductionday> queryProductionDay(String discern,String production,String line,String classInfo);

    CapacityTrendDto queryCapacityTrend(String discern, String beginOfDay, String endOfDay);

    List<String> queryGroupLeader(String discern,String production);

    List<String> queryWorkOrder(String line,String classInfo,String production);

    List<String> queryCustomerName(List<String> khlh);

    List<String> queryCplh(String line);
    List<String> queryProduction(String discern);

    List<DayCountDto> queryDayCount(String production,String beginDate,String endDate);

    //查詢客戶料號
    List<String> queryKhlh(List<String> workOrder);

    List<CustomerScslQop> queryScslQops(String cardId,String discern,String production,String line);

    List<TConfigureProductionday> queryTConfigureProductionDayByTime(String beginDate1, String endDate1, String discern, String production, String productionline, String classInfo);

    List<String> queryRoleLeader(String discern, String production);

    int queryCount(String discern, String production, String line, String classInfo);

    void updateTConfigureProductionDay(TConfigureProductionday tConfigureProductionday);

    void lockConfig(TConfigureProductionday tConfigureProductionday);

    int queryCountLock(String discern, String production, String line, String classInfo,String beginDate,String endDate);

    int queryCountLockByTime(String discern, String production, String line, String classInfo, String beginDateTime, String endDateTime);

    void insertDBCL_OndutyYield(TConfigureProductionday tConfigureProductionday);
}
