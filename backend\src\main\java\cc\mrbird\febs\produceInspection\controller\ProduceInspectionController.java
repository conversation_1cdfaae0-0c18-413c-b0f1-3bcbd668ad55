package cc.mrbird.febs.produceInspection.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.produceInspection.entity.*;
import cc.mrbird.febs.produceInspection.entity.dto.ProduceInspectionTemplateDto;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionInspectProjectsQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionMainInfoQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionPartNumberQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionUserInfoQueryParam;
import cc.mrbird.febs.produceInspection.service.ProduceInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/19
 * @Time: 10:08
 */
@Api(tags = "生產自主檢查")
@RequestMapping("/produceInspection")
@Slf4j
@RestController
@ResponseBody
public class ProduceInspectionController {
    @Autowired
    private ProduceInspectionService produceInspectionService;

    // 根據工號查詢產品処、機能和課別
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SJ_UserID", value = "工號,G5347608|F0842364|F0529150", example = "F0842364")
    })
    @GetMapping("/queryByUserId")
    @ApiOperation("/根據工號查詢產品処、機能和課別")
    public FebsResponse queryByUserId(@RequestParam(value = "SJ_UserID", required = false) String userID) {
        List<ProduceInspectionUserInfo> dtoList;
        try {
            dtoList = produceInspectionService.queryByUserId(userID);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數userID：" + userID);
        }
        return new FebsResponse().code("200").data(dtoList);
    }

    // 查詢產品処
    @GetMapping("/querySBU")
    @ApiOperation("/獲取產品処")
    public FebsResponse querySBU() {
        List<String> list;
        try {
            list = produceInspectionService.querySBU();
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤");
        }
        return new FebsResponse().code("200").data(list);
    }

    // 根據產品処查詢機能
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU", value = "產品処,IDS1|IDS2|IDS3", example = "IDS1"),
    })
    @GetMapping("/queryFunctions")
    @ApiOperation("/根據產品処查詢機能")
    public FebsResponse queryFunctions(@RequestParam(value = "SBU", required = false) String SBU) {
        List<String> list;
        try {
            list = produceInspectionService.queryFunctions(SBU);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數SBU：" + SBU);
        }
        return new FebsResponse().code("200").data(list);
    }

    // 根據產品処和機能查詢課別
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU", value = "產品処,IDS1|IDS2|IDS3", example = "IDS1"),
            @ApiImplicitParam(name = "functions", value = "機能,裝配|成型|電鍍|衝壓", example = "裝配")
    })
    @GetMapping("/queryDiscern")
    @ApiOperation("/獲取課別")
    public FebsResponse queryDiscern(@RequestParam(value = "SBU", required = false) String SBU,
            @RequestParam(value = "functions", required = false) String functions) {
        List<String> list;
        try {
            list = produceInspectionService.queryDiscern(SBU, functions);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數SBU：" + SBU + "和functions：" + functions);
        }
        return new FebsResponse().code("200").data(list);
    }

    // 根據產品処、機能和課別查詢系列
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU", value = "產品処,IDS1|IDS2|IDS3", example = "IDS1"),
            @ApiImplicitParam(name = "functions", value = "機能,裝配|成型|電鍍|衝壓", example = "裝配"),
            @ApiImplicitParam(name = "discern", value = "課別,裝配一課|二課|三課", example = "裝配一課")
    })
    @GetMapping("/queryProduction")
    @ApiOperation("/獲取系列")
    public FebsResponse queryProduction(@RequestParam(value = "SBU", required = false) String SBU,
            @RequestParam(value = "functions", required = false) String functions,
            @RequestParam(value = "discern", required = false) String discern) {
        List<String> list;
        try {
            list = produceInspectionService.queryProduction(SBU, functions, discern);

        } catch (Exception e) {
            return new FebsResponse().code("500")
                    .message("錯誤參數SBU：" + SBU + "和functions：" + functions + "和discern：" + discern);
        }
        return new FebsResponse().code("200").data(list);
    }

    // 根據產品処、機能、課別和系列查詢綫體
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU", value = "產品処,IDS1|IDS2|IDS3", example = "IDS1"),
            @ApiImplicitParam(name = "functions", value = "機能,裝配|成型|電鍍|衝壓", example = "裝配"),
            @ApiImplicitParam(name = "discern", value = "課別,裝配一課|二課|三課", example = "裝配一課"),
            @ApiImplicitParam(name = "production", value = "系列,1700SKT|4186SKT|4677SKT", example = "1700SKT")
    })
    @GetMapping("/queryProductionLine")
    @ApiOperation("獲取綫體")
    public FebsResponse queryProductionLine(@RequestParam(value = "SBU", required = false) String SBU,
            @RequestParam(value = "functions", required = false) String functions,
            @RequestParam(value = "discern", required = false) String discern,
            @RequestParam(value = "production", required = false) String production) {
        List<String> list;
        try {
            list = produceInspectionService.queryProductionLine(SBU, functions, discern, production);

        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數" + discern + production);
        }
        return new FebsResponse().code("200").data(list);
    }

    // 根據產品処、機能、課別、系列和綫體查詢工站
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU", value = "產品処,IDS1|IDS2|IDS3", example = "IDS1"),
            @ApiImplicitParam(name = "functions", value = "機能,裝配|成型|電鍍|衝壓", example = "裝配"),
            @ApiImplicitParam(name = "discern", value = "課別,裝配一課|二課|三課", example = "裝配一課"),
            @ApiImplicitParam(name = "production", value = "系列,1700SKT|4186SKT|4677SKT", example = "1700SKT"),
            @ApiImplicitParam(name = "line", value = "綫體,V1|V2|V3", example = "V1")
    })
    @GetMapping("/queryWorkStation")
    @ApiOperation("獲取工站")
    public FebsResponse queryWorkStation(@RequestParam(value = "SBU", required = false) String SBU,
            @RequestParam(value = "functions", required = false) String functions,
            @RequestParam(value = "discern", required = false) String discern,
            @RequestParam(value = "production", required = false) String production,
            @RequestParam(value = "line", required = false) String line) {
        List<String> list;
        try {
            list = produceInspectionService.queryWorkStation(SBU, functions, discern, production, line);

        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數" + discern + production);
        }
        return new FebsResponse().code("200").data(list);
    }

    // 根據SBU、机能、課別、系列、綫體、點檢類型、日期查詢點檢主要信息
    @ApiOperation(value = "查詢點檢主要信息", httpMethod = "POST")
    @PostMapping("/queryProductionInspectionMainInfo")
    public FebsResponse queryProductionInspectionMainInfo(
            @RequestBody ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam) {
        List<ProduceInspectionMainInfo> list = new ArrayList<>();
        try {
            // 檢查查詢的綫體狀態是否為不點檢
            if (isNotDJ(produceInspectionMainInfoQueryParam)) {
                // 不點檢則返回信息
                String msg = produceInspectionMainInfoQueryParam.getLine() + "的"
                        + produceInspectionMainInfoQueryParam.getDate();// XX綫體的日期
                msg = msg + "已设置为不点检";
                return new FebsResponse().code("200").message(msg).data(null);
            } else {
                // 點檢則查詢數據
                list = produceInspectionService.queryProductionInspectionMainInfo(produceInspectionMainInfoQueryParam);
            }
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數" + produceInspectionMainInfoQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    // 查詢某條綫體是否設置為不點檢
    private boolean isNotDJ(ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam) {
        // 如果綫體查詢條件爲空則説明使用了無條件全部查詢，直接放過
        String productionLine = produceInspectionMainInfoQueryParam.getLine();
        if (StringUtils.isEmpty(productionLine)) {
            return false;
        }

        ProduceInspectionNotInspect dto = produceInspectionService.queryIsNotDJ(produceInspectionMainInfoQueryParam);
        if (dto == null) {
            // 該綫體不在不點檢表中
            return false;
        } else {
            // 該綫體在不點檢表中
            return true;
        }
    }

    // 查詢點檢圖片信息
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mainInfoId", value = "主要信息id,1|2|3", example = "1")
    })
    @GetMapping("/queryProduceInspectionPics")
    @ApiOperation("/根據主要信息ID查詢點檢圖片信息")
    public FebsResponse queryProduceInspectionPics(
            @RequestParam(value = "mainInfoId", required = false) String mainInfoId) {
        List<ProduceInspectionPicUrl> picList;
        try {
            picList = produceInspectionService.queryPicsByMainInfoId(mainInfoId);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數userID：" + mainInfoId);
        }
        return new FebsResponse().code("200").data(picList);
    }

    // 查詢點檢項目維護信息
    @ApiOperation(value = "查詢點檢項目維護信息", httpMethod = "POST")
    @PostMapping("/queryProductionInspectionProjectData")
    public FebsResponse queryProductionInspectionProjectData(
            @RequestBody ProduceInspectionInspectProjectsQueryParam produceInspectionInspectProjectsQueryParam) {
        List<ProduceInspectionInspectProjects> list;
        try {
            // 獲取設備的點檢項目列表
            list = produceInspectionService
                    .queryProductionInspectionProjectData(produceInspectionInspectProjectsQueryParam);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數" + produceInspectionInspectProjectsQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    /**
     * 导出点检项目信息
     *
     * @param produceInspectionInspectProjectsQueryParam 查询参数
     * @param response                                   HTTP响应对象
     */
    @ApiOperation("導出點檢項目信息")
    @PostMapping(value = "/exportInspectProjectData", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportInspectProjectData(
            @RequestBody ProduceInspectionInspectProjectsQueryParam produceInspectionInspectProjectsQueryParam,
            HttpServletResponse response) {
        try {
            // 获取点检项目数据
            List<ProduceInspectionInspectProjects> dataList = produceInspectionService
                    .queryProductionInspectionProjectData(produceInspectionInspectProjectsQueryParam);

            // 创建工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("点检项目信息");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("產品処");
            headerRow.createCell(1).setCellValue("機能");
            headerRow.createCell(2).setCellValue("課別");
            headerRow.createCell(3).setCellValue("綫體");
            headerRow.createCell(4).setCellValue("系列");
            headerRow.createCell(5).setCellValue("工站");
            headerRow.createCell(6).setCellValue("設備名稱");
            headerRow.createCell(7).setCellValue("管制類別");
            headerRow.createCell(8).setCellValue("管制項目");
            headerRow.createCell(9).setCellValue("設定值");
            headerRow.createCell(10).setCellValue("單位");
            headerRow.createCell(11).setCellValue("檢查方法");
            headerRow.createCell(12).setCellValue("頻率");
            headerRow.createCell(13).setCellValue("文件編號");
            headerRow.createCell(14).setCellValue("文件ECN NO");
            headerRow.createCell(15).setCellValue("文件REV.");
            headerRow.createCell(16).setCellValue("版次號");

            // 写入数据
            int rowNum = 1;
            for (ProduceInspectionInspectProjects item : dataList) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(item.getSbu());
                row.createCell(1).setCellValue(item.getFunctions());
                row.createCell(2).setCellValue(item.getSection());
                row.createCell(3).setCellValue(item.getLine());
                row.createCell(4).setCellValue(item.getSeries());
                row.createCell(5).setCellValue(item.getWorkStation());
                row.createCell(6).setCellValue(item.getDeviceName());
                row.createCell(7).setCellValue(item.getRestraintCategory());
                row.createCell(8).setCellValue(item.getRestraintProject());
                row.createCell(9).setCellValue(item.getSettingValue());
                row.createCell(10).setCellValue(item.getUnit());
                row.createCell(11).setCellValue(item.getInspectMethod());
                row.createCell(12).setCellValue(item.getFrequence());
                row.createCell(13).setCellValue(item.getFileNo());
                row.createCell(14).setCellValue(item.getFileEcnNo());
                row.createCell(15).setCellValue(item.getFileRev());
                row.createCell(16).setCellValue(item.getEditionNo());
            }

            // 设置响应头
            String fileName = URLEncoder.encode("点检项目信息.xlsx", "UTF-8")
                    .replaceAll("\\+", "%20");
            String headerValue = String.format(
                    "attachment; filename=\"%s\"; filename*=UTF-8''%s",
                    fileName, fileName);
            response.setHeader("Content-Disposition", headerValue);
            response.setContentType("application/octet-stream");

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("导出点检项目信息失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    // 查詢料號維護信息
    @ApiOperation(value = "查詢料號維護信息", httpMethod = "POST")
    @PostMapping("/queryPartNumberData")
    public FebsResponse queryPartNumberData(
            @RequestBody ProduceInspectionPartNumberQueryParam produceInspectionPartNumberQueryParam) {
        List<ProduceInspectionPartNumber> list;
        try {
            // 獲取設備的點檢項目列表
            list = produceInspectionService.queryProduceInspectionPartNumberData(produceInspectionPartNumberQueryParam);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數" + produceInspectionPartNumberQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    /**
     * 导出料号信息
     *
     * @param produceInspectionPartNumberQueryParam 查询参数
     * @param response                              HTTP响应对象
     */
    @ApiOperation("導出料號信息")
    @PostMapping(value = "/exportPartNumberData", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportPartNumberData(
            @RequestBody ProduceInspectionPartNumberQueryParam produceInspectionPartNumberQueryParam,
            HttpServletResponse response) {
        try {
            // 获取料号数据
            List<ProduceInspectionPartNumber> dataList = produceInspectionService
                    .queryProduceInspectionPartNumberData(produceInspectionPartNumberQueryParam);

            // 创建工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("料号信息");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("產品処");
            headerRow.createCell(1).setCellValue("機能");
            headerRow.createCell(2).setCellValue("課別");
            headerRow.createCell(3).setCellValue("綫體");
            headerRow.createCell(4).setCellValue("系列");
            headerRow.createCell(5).setCellValue("料號");

            // 写入数据
            int rowNum = 1;
            for (ProduceInspectionPartNumber item : dataList) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(item.getSbu());
                row.createCell(1).setCellValue(item.getFunctions());
                row.createCell(2).setCellValue(item.getSection());
                row.createCell(3).setCellValue(item.getLine());
                row.createCell(4).setCellValue(item.getSeries());
                row.createCell(5).setCellValue(item.getPartNumber());
            }

            // 设置响应头
            String fileName = URLEncoder.encode("料号信息.xlsx", "UTF-8")
                    .replaceAll("\\+", "%20");
            String headerValue = String.format(
                    "attachment; filename=\"%s\"; filename*=UTF-8''%s",
                    fileName, fileName);
            response.setHeader("Content-Disposition", headerValue);
            response.setContentType("application/octet-stream");

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("导出料号信息失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    // 查詢人員維護信息
    @ApiOperation(value = "查詢人員維護信息", httpMethod = "POST")
    @PostMapping("/queryWorkerData")
    public FebsResponse queryWorkerData(
            @RequestBody ProduceInspectionUserInfoQueryParam produceInspectionUserInfoQueryParam) {
        List<ProduceInspectionUserInfo> list;
        try {
            // 獲取設備的點檢項目列表
            list = produceInspectionService.queryProduceInspectionUserInfoData(produceInspectionUserInfoQueryParam);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數" + produceInspectionUserInfoQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    /**
     * 导出人员信息
     *
     * @param produceInspectionUserInfoQueryParam 查询参数
     * @param response                            HTTP响应对象
     */
    @ApiOperation("導出人員信息")
    @PostMapping(value = "/exportWorkerData", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportWorkerData(
            @RequestBody ProduceInspectionUserInfoQueryParam produceInspectionUserInfoQueryParam,
            HttpServletResponse response) {
        try {
            // 获取人员数据
            List<ProduceInspectionUserInfo> dataList = produceInspectionService
                    .queryProduceInspectionUserInfoData(produceInspectionUserInfoQueryParam);

            // 创建工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("人员信息");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("產品処");
            headerRow.createCell(1).setCellValue("機能");
            headerRow.createCell(2).setCellValue("課別");
            headerRow.createCell(3).setCellValue("工號");
            headerRow.createCell(4).setCellValue("姓名");
            headerRow.createCell(5).setCellValue("系列");
            headerRow.createCell(6).setCellValue("綫體");
            headerRow.createCell(7).setCellValue("賬號類型（點檢人 or 確認人）");


            // 写入数据
            int rowNum = 1;
            for (ProduceInspectionUserInfo item : dataList) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(item.getSbu());
                row.createCell(1).setCellValue(item.getFunctions());
                row.createCell(2).setCellValue(item.getSection());
                row.createCell(3).setCellValue(item.getWorkId());
                row.createCell(4).setCellValue(item.getName());
                row.createCell(5).setCellValue(item.getType());
                row.createCell(6).setCellValue(item.getSeries());
                row.createCell(7).setCellValue(item.getLine());
            }

            // 设置响应头
            String fileName = URLEncoder.encode("人员信息.xlsx", "UTF-8")
                    .replaceAll("\\+", "%20");
            String headerValue = String.format(
                    "attachment; filename=\"%s\"; filename*=UTF-8''%s",
                    fileName, fileName);
            response.setHeader("Content-Disposition", headerValue);
            response.setContentType("application/octet-stream");

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("导出人员信息失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 下载维护模板
     *
     * @param produceInspectionTemplateDto type:模板类型：1-人员维护模板，2-项目维护模板，3-料号维护模板
     * @param response                     HTTP响应对象
     */
    @ApiOperation("下載維護模板（type 模板类型：1-人员维护模板，2-项目维护模板，3-料号维护模板）")
    @PostMapping(value = "/downloadTemplate", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadTemplate(@RequestBody ProduceInspectionTemplateDto produceInspectionTemplateDto,
            HttpServletResponse response) {
        try {
            String fileName;
            String resourcePath;

            switch (produceInspectionTemplateDto.getType()) {
                case 1:
                    fileName = "renyuan.xlsx";
                    resourcePath = "templates/人员维护模板.xlsx";
                    break;
                case 2:
                    fileName = "xiangmu.xlsx";
                    resourcePath = "templates/项目维护模板.xlsx";
                    break;
                case 3:
                    fileName = "liaohao.xlsx";
                    resourcePath = "templates/料号维护模板.xlsx";
                    break;
                default:
                    throw new IllegalArgumentException("无效的模板类型");
            }

            Resource resource = new ClassPathResource(resourcePath);
            if (!resource.exists()) {
                throw new FileNotFoundException("模板文件不存在: " + resourcePath);
            }

            // 读取模板文件
            XSSFWorkbook workbook = new XSSFWorkbook(resource.getInputStream());
            XSSFSheet sheet = workbook.getSheetAt(0);

            // 在第二行（索引1）写入数据，作为预填充数据
            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue(produceInspectionTemplateDto.getSbu()); // 產品処
            dataRow.createCell(1).setCellValue(produceInspectionTemplateDto.getFunctions()); // 機能
            dataRow.createCell(2).setCellValue(produceInspectionTemplateDto.getSection()); // 課別

            // 如果是人员维护模板，添加系列和线体字段数据
            if (produceInspectionTemplateDto.getType() == 1) {
                dataRow.createCell(3).setCellValue(produceInspectionTemplateDto.getSeries()); // 系列
                dataRow.createCell(4).setCellValue(produceInspectionTemplateDto.getLine()); // 线体
            }

            // 项目维护模板不需要额外处理

            // RFC 5987编码文件名
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8")
                    .replaceAll("\\+", "%20");
            String headerValue = String.format(
                    "attachment; filename=\"%s\"; filename*=UTF-8''%s",
                    encodedFileName, encodedFileName);
            response.setHeader("Content-Disposition", headerValue);
            response.setContentType("application/octet-stream");

            // 将修改后的工作簿写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("下载模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Excel文件上传接口
     *
     * @param file 上传的Excel文件
     * @param type 文件类型：1-人员信息，2-自主点检项目，3-料号信息
     * @return 上传结果
     */
    @ApiOperation("上傳數據（type 文件類型：1-人員信息，2-自主點檢項目，3-料號信息）")
    @PostMapping("/upload")
    public FebsResponse uploadExcelFile(@RequestParam("file") MultipartFile file,
            @RequestParam("type") Integer type) {
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                return new FebsResponse().code("500").message("上传文件不能为空");
            }

            // 检查文件格式
            String fileName = file.getOriginalFilename();
            if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
                return new FebsResponse().code("500").message("只支持Excel文件上传");
            }

            // 根据type调用不同的服务处理方法
            String result;
            switch (type) {
                case 1:
                    // 人员信息上传，现在包含series和line字段
                    result = produceInspectionService.importPersonnelData(file);
                    break;
                case 2:
                    // 自主点检项目上传，包含对frequence字段的验证
                    result = produceInspectionService.importInspectionItems(file);
                    // 检查结果中是否包含错误信息
                    if (result.contains("因頻率值不符合要求")) {
                        // 如果包含错误信息，返回成功的响应
                        return new FebsResponse().code("200").message("部分数据导入成功").data(result);
                    }
                    break;
                case 3:
                    result = produceInspectionService.importMaterialInfo(file);
                    break;
                default:
                    return new FebsResponse().code("500").message("不支持的文件类型");
            }

            return new FebsResponse().code("200").message("上传成功").data(result);

        } catch (Exception e) {
            log.error("文件上传失败", e);
            return new FebsResponse().code("500").message("文件上传失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新料號信息")
    @PutMapping("/updatePartNumber")
    public FebsResponse updatePartNumber(@RequestBody ProduceInspectionPartNumber partNumber) {
        try {
            // 检查ID是否存在
            if (partNumber.getId() == null) {
                return new FebsResponse().code("500").message("ID不能为空");
            }

            int result = produceInspectionService.updatePartNumber(partNumber);
            if (result > 0) {
                return new FebsResponse().code("200").message("更新成功");
            } else {
                return new FebsResponse().code("500").message("更新失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("更新料号信息失败", e);
            return new FebsResponse().code("500").message("更新料号信息失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除料號信息")
    @DeleteMapping("/deletePartNumber/{id}")
    public FebsResponse deletePartNumber(@PathVariable Integer id) {
        try {
            int result = produceInspectionService.deletePartNumber(id);
            if (result > 0) {
                return new FebsResponse().code("200").message("删除成功");
            } else {
                return new FebsResponse().code("500").message("删除失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("删除料号信息失败", e);
            return new FebsResponse().code("500").message("删除料号信息失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新人员信息")
    @PutMapping("/updateUserInfo")
    public FebsResponse updateUserInfo(@RequestBody ProduceInspectionUserInfo userInfo) {
        try {
            if (userInfo.getId() == null) {
                return new FebsResponse().code("500").message("ID不能为空");
            }

            int result = produceInspectionService.updateUserInfo(userInfo);
            if (result > 0) {
                return new FebsResponse().code("200").message("更新成功");
            } else {
                return new FebsResponse().code("500").message("更新失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("更新人员信息失败", e);
            return new FebsResponse().code("500").message("更新人员信息失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除人员信息")
    @DeleteMapping("/deleteUserInfo/{id}")
    public FebsResponse deleteUserInfo(@PathVariable Integer id) {
        try {
            int result = produceInspectionService.deleteUserInfo(id);
            if (result > 0) {
                return new FebsResponse().code("200").message("删除成功");
            } else {
                return new FebsResponse().code("500").message("删除失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("删除人员信息失败", e);
            return new FebsResponse().code("500").message("删除人员信息失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新点检项目")
    @PutMapping("/updateInspectProject")
    public FebsResponse updateInspectProject(@RequestBody ProduceInspectionInspectProjects project) {
        try {
            if (project.getId() == null) {
                return new FebsResponse().code("500").message("ID不能为空");
            }

            int result = produceInspectionService.updateInspectProject(project);
            if (result > 0) {
                return new FebsResponse().code("200").message("更新成功");
            } else {
                return new FebsResponse().code("500").message("更新失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("更新点检项目失败", e);
            return new FebsResponse().code("500").message("更新点检项目失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除点检项目")
    @DeleteMapping("/deleteInspectProject/{id}")
    public FebsResponse deleteInspectProject(@PathVariable Integer id) {
        try {
            int result = produceInspectionService.deleteInspectProject(id);
            if (result > 0) {
                return new FebsResponse().code("200").message("删除成功");
            } else {
                return new FebsResponse().code("500").message("删除失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("删除点检项目失败", e);
            return new FebsResponse().code("500").message("删除点检项目失败：" + e.getMessage());
        }
    }
}
