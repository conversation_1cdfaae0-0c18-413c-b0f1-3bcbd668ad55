package cc.mrbird.febs.line.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class LineInfos {
    @ApiModelProperty(value = "课别",required = true)
    private String sections;
    @ApiModelProperty(value = "系列",required = true)
    private List<String> series;
    @ApiModelProperty(value = "线体",required = true)
    private List<String> lines;
}
