package cc.mrbird.febs.comparison.entity;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryDto {
    @ApiModelProperty(value = "開始日期",required = true,position = 1)
    private String startDate;
    @ApiModelProperty(value = "結束日期",required = true,position = 2)
    private String endDate;
    @ApiModelProperty(value = "課別",required = true,position = 3)
    private String enginery;
    @ApiModelProperty(value = "系列",required = true,position = 4)
    private String production;
    @ApiModelProperty(value = "線體",required = true,position = 5)
    private String productionLine;
    @ApiModelProperty(value = "班別",required = true,position = 6)
    private String classInfo;
    @ApiModelProperty(value = "事業處",required = true,position = 7)
    private String industry;
}
