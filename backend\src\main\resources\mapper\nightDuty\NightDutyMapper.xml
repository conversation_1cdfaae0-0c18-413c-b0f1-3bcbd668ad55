<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.nightDuty.dao.NightDutyMapper">


    <!--向值夜人员信息表中插入数据-->
    <insert id="insertChectPerson">
        insert into [APPGCL_Process].[dbo].[ZGCY_PlanList] (factory,building,ZY_Date,ZY_UserID,ZY_UserName,Ins_Date)
        values ('寶科',#{building},#{date},#{workId},#{name},#{time})
    </insert>


    <!--创建APP端的账号-->
    <insert id="createAccount">
        insert into [APPGCL_Process].[dbo].[USER_Table] (userid,username,password,SBU,factory,functions,section,Rank,WebRank,tel_url,ins_date)
        values (#{workId},#{name},'123456',#{SBU},'BK',#{functions},#{section},'0','0','http://10.196.5.228:812/Userpicture/tox.png',#{time})
    </insert>


    <!--新增值夜人员-->
    <insert id="insertPerson">
        insert into [APPGCL_Process].[dbo].[ZGCY_PlanList] (factory,building,ZY_Date,ZY_UserID,ZY_UserName,Ins_Date)
        values ('寶科',#{building},#{date},#{workId},#{name},#{time})
    </insert>




    <!--更新值夜人员信息-->
    <update id="updatePerson">
        update [APPGCL_Process].[dbo].[ZGCY_PlanList]
        <set>
           <if test="factory != null and factory != ''">
               factory = #{factory},
           </if>
            <if test="building != null and building != ''">
                building = #{building},
            </if>
            <if test="date != null and date != ''">
                ZY_Date = #{date},
            </if>
            <if test="workId != null and workId != ''">
                ZY_UserID = #{workId},
            </if>
            <if test="name != null and name != ''">
                ZY_UserName = #{name},
            </if>
            <if test="time != null and time != ''">
                Ins_Date = #{time}
            </if>
        </set>
        where id = #{id}
    </update>


    <!--批量删除人员信息-->
    <delete id="deletePerson">
        delete from[APPGCL_Process].[dbo].[ZGCY_PlanList]
        <where>
            <if test="id != null">
                id = #{id}
            </if>
        </where>
    </delete>


    <!--查询值夜人员中是否有-->
    <select id="queryChechPerson" resultType="java.lang.Integer">
        select count(*) from [APPGCL_Process].[dbo].[ZGCY_PlanList]
        <where>
            <if test="building != null and building != ''">
                building = #{building}
            </if>
            <if test="workId != null and workId != ''">
                and ZY_UserId = #{workId}
            </if>
            <if test="name != null and name != ''">
                and ZY_UserName = #{name}
            </if>
        </where>
    </select>


    <!--查询有無账号-->
    <select id="queryAccount" resultType="java.lang.Integer">
        select count(*) from [APPGCL_Process].[dbo].[USER_Table]
        <where>
                 userid = #{workId}
                and username = #{name}
        </where>
    </select>


    <!--查询值夜人员信息-->
    <select id="selectPerson" resultType="cc.mrbird.febs.nightDuty.entity.NightDutyPerson">
        select id,factory,building,ZY_Date date,ZY_UserID workId,ZY_UserName name ,Ins_Date time
        from [APPGCL_Process].[dbo].[ZGCY_PlanList]
        <where>
            <if test="factory != null and factory != ''">factory = #{factory}</if>
            <if test="building != null and building != ''">and building like concat ('%',#{building},'%')</if>
            <if test="date != null and date != ''">and ZY_Date like concat ('%',#{date},'%') </if>
        </where>
        order by date desc
    </select>


    <!--excel导出-->
    <select id="exportPerson" resultType="cc.mrbird.febs.nightDuty.entity.ExportPerson">
        select factory, building, ZY_Date date, ZY_UserID workId, ZY_UserName name from [APPGCL_Process].[dbo].[ZGCY_PlanList]
        <where>
            ZY_Date like concat ('%',#{time},'%')
        </where>
    </select>


    <select id="exportPersonTest">
        select factory, building, ZY_Date date, ZY_UserID workId, ZY_UserName name from [APPGCL_Process].[dbo].[ZGCY_PlanList]
        <where>
            ZY_Date like concat ('%',#{time},'%')
        </where>
    </select>

    <!--判斷該員工有無賬號-->
    <select id="selectStauts" resultType="java.lang.Integer">
        select count(*) from [APPGCL_Process].[dbo].[USER_Table]
        where userid = #{workId}
    </select>

    <!--查詢廠區-->
    <select id="queryFactory" resultType="java.lang.String">
        select factory from [APPGCL_Process].[dbo].[ZGCY_PlanList]
    </select>

    <!--查詢樓棟-->
    <select id="queryBuilding" resultType="java.lang.String">
        select building from [APPGCL_Process].[dbo].[ZGCY_PlanList]
    </select>


    <!--通过id查询信息-->
    <select id="queryInfo" resultType="cc.mrbird.febs.nightDuty.entity.vo.NightDutyPersonVO">
        select factory, building, ZY_Date date, ZY_UserID workId, ZY_UserName name from [APPGCL_Process].[dbo].[ZGCY_PlanList]
        where id = #{id}
    </select>


    <!--查询机能-->
    <select id="queryFunctions" resultType="java.lang.String">
        select functions from [APPGCL_Process].[dbo].[USER_Table]
    </select>


    <!--查询课别-->
    <select id="querySection" resultType="java.lang.String">
        select section from [APPGCL_Process].[dbo].[USER_Table]
    </select>


</mapper>