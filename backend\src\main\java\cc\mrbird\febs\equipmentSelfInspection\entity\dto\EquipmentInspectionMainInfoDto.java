package cc.mrbird.febs.equipmentSelfInspection.entity.dto;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 李囯斌
 * Date: 2024/9/10
 * Time: 9:59
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class EquipmentInspectionMainInfoDto {
    private Integer id;
    private String pl;
    private String enginery;
    private String discern;
    private String production;
    private String productionLine;
    private String inspectionType;
    private String equipmentId;
    private String equipmentName;
    private String date;
    private String startDate;
    private String endDate;
    private String status;
    private String notes;
    private String djrWorkId;//點檢人工號
    private String djrName;
    private String qdrWorkId;//確定人工號
    private String qdrName;
    private String duration;//時長

}
