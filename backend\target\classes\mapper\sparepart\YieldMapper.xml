<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.sparepart.dao.YieldMapper">

    <update id="UpdateMethodBy123">
     <!--   <![CDATA[
        DECLARE @production NVARCHAR (255),@productionLine varchar(255), @sparePartName NVARCHAR (255),
            @equipmentName NVARCHAR (255),@eqid varchar(255), @equipmentUpdateTime DATETIME;
        DECLARE cur CURSOR FOR
            SELECT  production,productionLine, sparePartName, equipmentName, equipmentUpdateTime ,eqid
            FROM t_configure_equipmentAndYield; OPEN cur; FETCH NEXT FROM cur
            INTO @production,@productionLine, @sparePartName, @equipmentName, @equipmentUpdateTime,@eqid;
        WHILE @@FETCH_STATUS = 0 BEGIN WITH Temp AS (
            SELECT b.eqid,SUM(passqty) AS passqty
            FROM Tj_OEE AS a Right JOIN t_configure_equipmentAndYield AS b ON a.EqID =b.eqid
            WHERE ClassDate >=@equipmentUpdateTime AND b.eqid=@eqid
            GROUP BY b.eqid)
                                       UPDATE t_configure_equipmentAndYield SET actualOutput = (SELECT passqty FROM Temp),
                                                                                sumTime=FORMAT(getdate(),'yyyy-MM-dd HH:mm:ss')
                                       WHERE production=@production AND productionLine=@productionLine
                                         AND equipmentName = @equipmentName AND sparePartName = @sparePartName;
        FETCH NEXT FROM cur INTO @production,@productionLine, @sparePartName, @equipmentName, @equipmentUpdateTime,@eqid;
        END; CLOSE cur; DEALLOCATE cur;
        ]]>-->
        UPDATE A
        SET A.actualOutput = B.passqty_sum
        FROM [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndYield] AS A
        INNER JOIN (
        SELECT EqID, SUM(passqty) AS passqty_sum
        FROM [IDS_BK_PL1_02].[dbo].[Tj_OEE]
        WHERE dDate >= (SELECT MAX(equipmentUpdateTime) FROM [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndYield] WHERE eqid = EqID)
        GROUP BY EqID
        ) AS B
        ON A.eqid = B.EqID
        WHERE A.sparePartName IS NOT NULL
    </update>

    <select id="getSharePartIn123" resultType="cc.mrbird.febs.sparepart.entity.dto.EquipmentDataBy123">
        SELECT a.id, a.production,a.productionLine,a.equipmentName,a.sparePartName,a.lifeLimit ,a.actualOutput AS actualOutput,b.personInChargeEmailBy01,b.personInChargeEmailBy02,b.personInChargeEmailByMax,
            a.isSendEmail,a.equipmentUpdateTime,b.personInChargeNameBy01,b.personInChargeNameBy02,b.personInChargeNameByMax
        FROM t_configure_equipmentAndYield AS a
                 LEFT JOIN t_configure_personAndEquipment as b ON a.sparePartName = b.sparePartName
        WHERE ((CAST(actualOutput AS FLOAT) / CAST(a.lifeLimit AS FLOAT)) * 100) >= 95 AND personInChargeEmailBy01 is not null
    </select>

    <select id="querySparePartAndView" resultType="cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield">
        SELECT *
        FROM (
                 SELECT id,pl,enginery,discern,production,productionLine,equipmentName
                      ,sparePartName,lifeLimit,CASE WHEN actualOutput  is null then '0' else  actualOutput end as actualOutput
                      ,CASE WHEN ((CAST(actualOutput AS FLOAT) / CAST(lifeLimit AS FLOAT)) * 100) >= 90 then '預警' else '正常' end AS monitorState
                      ,operationType,CASE WHEN isSendEmail =0 then '無需推送' WHEN isSendEmail='1' OR isSendEmail='2' OR isSendEmail='3' then '已經推送' END AS isSendEmail ,'更換' AS performActions, '維修記錄' AS maintenanceRecord
                 FROM [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndYield]
            <where>
                <if test="pl != null and pl != ''">
                    AND pl=#{pl}
                </if>
                <if test="enginery != null and enginery != ''">
                    AND enginery=#{enginery}
                </if>
                <if test="discern != null and discern != ''">
                    AND discern=#{discern}
                </if>
                <if test="production != null and production != ''">
                    AND production=#{production}
                </if>
                <if test="productionLine != null and productionLine != ''">
                    AND productionLine=#{productionLine}
                </if>

                AND sparePartName IS NOT NULL

            </where>
             ) AS subquery
        ORDER BY CHARINDEX(subquery.monitorState, '預警,正常')
    </select>

    <update id="updateSparePart">
        UPDATE t_configure_equipmentAndYield set
        operationalReason=#{operationalReason},operationType=#{operationType},equipmentUpdateTime=#{operationTime},isSendEmail='0',actualOutput='0'
        WHERE id=#{id}
    </update>

    <insert id="insertSparePart">
        INSERT INTO t_configure_yieldOperation(id,productionLine,equipmentName,sparePartName,lifeLimit, actualOutput,operationalReason, operationType, operationTime,operationUser)
            values(#{id},#{productionLine},#{equipmentName},#{sparePartName},#{lifeLimit},#{actualOutput},#{operationalReason},#{operationType},#{operationTime},#{operationUser})
    </insert>

    <select id="queryYieldOperation" resultType="cc.mrbird.febs.sparepart.entity.dto.TConfigureYieldOperation">
        SELECT id,
        productionLine,
        equipmentName,
        sparePartName,
        lifeLimit,
        actualOutput,
        operationalReason,
        operationType,
        operationTime,
        operationUser
        FROM t_configure_yieldOperation where id=#{id}
        ORDER BY operationTime DESC
    </select>

    <update id="UpdateByEmailSendState">
       UPDATE t_configure_equipmentAndYield set isSendEmail=#{param2} WHERE id=#{param1}
    </update>

    <select id="autoSumYield" resultType="int">
        SELECT  CASE WHEN SUM(passqty) IS  NULL then '0' else  SUM(passqty) end as a
        FROM [IDS_BK_PL1_02].[dbo].[Tj_OEE]
        WHERE ClassDate >=#{param1} AND EqID=#{param2}
    </select>

    <select id="selectTConfigureEquipmentAndYield"
            resultType="cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield">
        SELECT  [id]
                        ,[pl]
                        ,[enginery]
                        ,[discern]
                        ,[production]
                        ,[productionLine]
                        ,[equipmentName]
                        ,[sparePartName]
                        ,[lifeLimit]
                        ,[actualOutput]
                        ,[operationalReason]
                        ,[operationType]
                        ,[eqid]
                        ,[equipmentUpdateTime]
                        ,[sumTime]
                        ,[isSendEmail]
        FROM [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndYield]
        WHERE sparePartName IS NOT NULL
    </select>

    <update id="updateByAutoSum">
        update [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndYield] set actualOutput=#{param2,jdbcType=INTEGER},sumTime=FORMAT(getdate(),'yyyy-MM-dd HH:mm:ss') ,sumType='230Service-backendCount'
        WHERE id=#{param1,jdbcType=INTEGER}
    </update>

    <select id="autoSumYieldByMin" resultType="cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield">
        SELECT
            id,sparePartName,eqid,equipmentUpdateTime
        FROM [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndYield]
    </select>

    <select id="sumYieldByAuto" resultType="int">
        SELECT CASE WHEN sum(passqty) IS NULL then '0' else  sum(passqty) end as yield  FROM Tj_OEE WHERE ClassDate >=#{param1} AND EqID=#{param2}
    </select>

    <update id="updateById">
        UPDATE  [t_configure_equipmentAndYield] set actualOutput =#{param2},sumTime=FORMAT(getdate(),'yyyy-MM-dd HH:mm:ss'),sumType='230Send'
        WHERE id=#{param1}
    </update>

    <select id="selectTConfigureEquipmentAndYieldById"
            resultType="cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield">
        SELECT TOP (1) [id]
                        ,[pl]
                        ,[enginery]
                        ,[discern]
                        ,[production]
                        ,[productionLine]
                        ,[equipmentName]
                        ,[sparePartName]
                        ,[lifeLimit]
                        ,[actualOutput]
                        ,[operationalReason]
                        ,[operationType]
                        ,[eqid]
                        ,[equipmentUpdateTime]
                        ,[sumTime]
                        ,[isSendEmail]
                        ,[sumType]
        FROM [IDS_BK_PL1_02].[dbo].[t_configure_equipmentAndYield] WHERE id =#{id}
    </select>
</mapper>

