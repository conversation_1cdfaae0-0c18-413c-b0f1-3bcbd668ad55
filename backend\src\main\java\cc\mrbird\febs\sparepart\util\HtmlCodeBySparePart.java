package cc.mrbird.febs.sparepart.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class HtmlCodeBySparePart {
    public  String getCode(String production,String productionLine,
                       String    equipmentName,String sparePartName,
                           String lifeLimit,String actualOutput,String personInChargeNameBy01,
                           String equipmentUpdateTime) throws UnsupportedEncodingException {
        String code ="<!DOCTYPE html>\n" +
                "<html lang=\"zh-Hant\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>备件寿命到期预警</title>\n" +
                "</head>\n" +
                "<body style=\"font-family: 'Arial', sans-serif; background-color: #f4f4f9; margin: 0; padding: 20px;\">\n" +
                "<div style=\"background-color: #ffffff; width: 80%; margin: auto; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);\">\n" +
                "    <div style=\"background-color: #0073e6; color: #ffffff; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;\">\n" +
                "        <h1 style=\"font-size: 24px; margin: 0;\">"+production+"-"+productionLine+"-"+equipmentName+"【"+sparePartName+"】"+"備件壽命到期預警</h1>\n" +
                "    </div>\n" +
                "    <div style=\"margin-top: 20px; line-height: 1.6; padding: 0 20px;\">\n" +
                "        <p>尊敬的设备管理者，"+personInChargeNameBy01+"</p>\n" +
                "        <p style=\"font-size: 16px;\">您好！您的设备备件已接近寿命上限，请留意以下详情：</p>\n" +
                "        <ul style=\"font-size: 16px; padding-left: 20px;\">\n" +
                "            <li>壽命上限："+lifeLimit+"</li>\n" +
                "            <li>已使用次數："+actualOutput+"</li>\n" +
                "            <li>上次更換時間："+equipmentUpdateTime+"</li>\n" +
                "        </ul>\n" +
                "        <p style=\"font-size: 16px;\">为避免设备出现故障，建议您尽快进行更换处理！</p>\n" +
                "        <p style=\"font-size: 16px;\">請更換備件後前往系统：http://************:220 進行記錄。感谢您的合作！</p>\n" +
                "    </div>\n" +
                "    <div style=\"margin-top: 20px; text-align: center; color: #777777; font-size: 14px;\">\n" +
                "        <p>此邮件为系统自动发送，请勿直接回复。</p>\n" +
                "    </div>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>\n";
        code= URLEncoder.encode(code,String.valueOf(StandardCharsets.UTF_8));
        return   code;
    }
}
