package cc.mrbird.febs.rdProject.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理人員擴展信息表實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectUserExtra {

    @ApiModelProperty(value = "关联t_user表的主键", position = 0)
    private Long userId;

    @ApiModelProperty(value = "全局唯一工号", position = 1)
    private String workerId;

    @ApiModelProperty(value = "用户名", position = 2)
    private String userName;

    @ApiModelProperty(value = "厂区", position = 3)
    private String siteArea;

    @ApiModelProperty(value = "产品处", position = 4)
    private String sbu;

    @ApiModelProperty(value = "机能", position = 5)
    private String functions;

    @ApiModelProperty(value = "课别", position = 6)
    private String section;

    @ApiModelProperty(value = "主归属团队（关联team表）", position = 7)
    private Long belongTeamId;
}
