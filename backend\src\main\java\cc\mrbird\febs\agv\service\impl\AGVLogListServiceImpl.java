package cc.mrbird.febs.agv.service.impl;

import cc.mrbird.febs.agv.dao.AGVLogListMapper;
import cc.mrbird.febs.agv.entity.AGVLogList;
import cc.mrbird.febs.agv.param.AgvLogListQueryParam;
import cc.mrbird.febs.agv.service.AGVLogListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AGVLogListServiceImpl implements AGVLogListService {
    private final AGVLogListMapper agvLogListMapper;

    @Autowired
    public AGVLogListServiceImpl(AGVLogListMapper agvLogListMapper) {
        this.agvLogListMapper = agvLogListMapper;
    }


    @Override
    public List<AGVLogList> queryAGVLogList(AgvLogListQueryParam agvLogQueryParam) {
        return agvLogListMapper.queryAGVLogList(agvLogQueryParam);
    }
}