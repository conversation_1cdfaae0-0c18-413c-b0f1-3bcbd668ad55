package cc.mrbird.febs.equipmentSelfInspection.service;

import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentPersonDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.ProjectMaintenance;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentQueryParam;

import java.util.List;

/**
 * Author: 覃金華
 * Date: 2024-08-12
 * Time: 下午 01:06
 */
public interface EquipmentSelfInspectionService {
    //查詢設備數據
    List<EquipmentInspectionDto> queryEquipmentInspectionData(EquipmentQueryParam equipmentQueryParam);
    //查詢人員數據
    List<EquipmentPersonDto>  queryEquipmentPersonData(EquipmentQueryParam equipmentQueryParam);
    //查詢項目維護數據
    List<ProjectMaintenance> queryProjectMaintenanceData(EquipmentQueryParam equipmentQueryParam);

    List<String>  queryDiscern();
    List<String> queryProduction(String discern);
    List<String> queryProductionLine(String discern,String production);
    List<String> queryEquipmentName(String discern,String production,String productionLine);

    List<String> querySelfInspectionType();

    //下載人員維護信息模板
    String  getEquipmentPersonExportFile();
    //下載設備信息模板
    String getEquipmentDataExportFile();
    //下載點檢項目模板
    String getSelfInspectionDataExportFile();
    //設備數據修改接口
    int updateByEquipment(EquipmentInspectionDto equipmentInspectionDto);
    //點檢項目修改接口
    int updateByProjectMaintenance(ProjectMaintenance projectMaintenance);
    //人員修改接口
    int updateByEquipmentPerson(EquipmentPersonDto equipmentPersonDto);
    //人員數據刪除接口
    int delByEquipmentPerson(Integer id);
    //人員數據新增接口
    int insertByEquipmentPerson(EquipmentPersonDto equipmentPersonDto);
    //設備數據刪除接口
    int delByEquipmentData(Integer id);

    void inertByEquipmentInspection(EquipmentInspectionDto equipmentInspectionDto);
    void insertByProjectMaintenance(ProjectMaintenance projectMaintenance);
    int countByEquipmentInspection(String production,String productionLine,String equipmentName);

    int delByProjectMaintenance(Integer id);

    String selectEquipment(String production, String productionLine, String equipmentName);

    List<EquipmentInspectionDto>  selectExampleData();
    List<ProjectMaintenance>   selectExampleDataByProject();
}
