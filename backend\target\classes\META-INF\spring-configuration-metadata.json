{"groups": [{"name": "febs", "type": "cc.mrbird.febs.common.properties.FebsProperties", "sourceType": "cc.mrbird.febs.common.properties.FebsProperties"}, {"name": "file", "type": "cc.mrbird.febs.backwork.common.FileProperties", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties"}, {"name": "file.linux", "type": "cc.mrbird.febs.backwork.common.FileProperties$EhPath", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties", "sourceMethod": "public cc.mrbird.febs.backwork.common.FileProperties.EhPath getLinux() "}, {"name": "file.mac", "type": "cc.mrbird.febs.backwork.common.FileProperties$EhPath", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties", "sourceMethod": "public cc.mrbird.febs.backwork.common.FileProperties.EhPath getMac() "}, {"name": "file.path", "type": "cc.mrbird.febs.backwork.common.FileProperties$EhPath", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties", "sourceMethod": "public cc.mrbird.febs.backwork.common.FileProperties.EhPath getPath() "}, {"name": "file.windows", "type": "cc.mrbird.febs.backwork.common.FileProperties$EhPath", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties", "sourceMethod": "public cc.mrbird.febs.backwork.common.FileProperties.EhPath getWindows() "}], "properties": [{"name": "febs.open-aop-log", "type": "java.lang.Bo<PERSON>an", "sourceType": "cc.mrbird.febs.common.properties.FebsProperties"}, {"name": "febs.shiro", "type": "cc.mrbird.febs.common.properties.ShiroProperties", "sourceType": "cc.mrbird.febs.common.properties.FebsProperties"}, {"name": "febs.swagger", "type": "cc.mrbird.febs.common.properties.SwaggerProperties", "sourceType": "cc.mrbird.febs.common.properties.FebsProperties"}, {"name": "file.avatar-max-size", "type": "java.lang.Long", "description": "头像大小限制", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties"}, {"name": "file.linux.avatar", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}, {"name": "file.linux.path", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}, {"name": "file.mac.avatar", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}, {"name": "file.mac.path", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}, {"name": "file.max-size", "type": "java.lang.Long", "description": "文件大小限制", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties"}, {"name": "file.path.avatar", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}, {"name": "file.path.path", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}, {"name": "file.windows.avatar", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}, {"name": "file.windows.path", "type": "java.lang.String", "sourceType": "cc.mrbird.febs.backwork.common.FileProperties$EhPath"}], "hints": []}