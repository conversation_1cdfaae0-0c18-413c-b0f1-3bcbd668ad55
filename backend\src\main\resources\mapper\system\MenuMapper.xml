<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.system.dao.MenuMapper">
    <resultMap id="menu" type="cc.mrbird.febs.system.domain.Menu">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="MENU_ID" jdbcType="DECIMAL" property="menuId"/>
        <result column="PARENT_ID" jdbcType="DECIMAL" property="parentId"/>
        <result column="MENU_NAME" jdbcType="VARCHAR" property="menuName"/>
        <result column="PATH" jdbcType="VARCHAR" property="path"/>
        <result column="COMPONENT" jdbcType="VARCHAR" property="component"/>
        <result column="PERMS" jdbcType="VARCHAR" property="perms"/>
        <result column="ICON" jdbcType="VARCHAR" property="icon"/>
        <result column="TYPE" jdbcType="CHAR" property="type"/>
        <result column="ORDER_NUM" jdbcType="DOUBLE" property="orderNum"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <select id="findUserPermissions" resultMap="menu">
        select distinct m.perms
        from t_role r
                 left join t_user_role ur on (r.role_id = ur.role_id)
                 left join t_user u on (u.user_id = ur.user_id)
                 left join t_role_menu rm on (rm.role_id = r.role_id)
                 left join t_menu m on (m.menu_id = rm.menu_id)
        where u.username = #{userName}
          and m.perms is not null
          and m.perms &lt;&gt; ''
    </select>

    <select id="findUserMenus" resultMap="menu">
        select m.*
        from t_menu m
        where m.type &lt;&gt; 1
          and m.MENU_ID in
              (select distinct rm.menu_id
               from t_role_menu rm
                        left join t_role r on (rm.role_id = r.role_id)
                        left join t_user_role ur on (ur.role_id = r.role_id)
                        left join t_user u on (u.user_id = ur.user_id)
               where u.username = #{userName})
        order by m.order_num
    </select>

    <select id="findUserIdsByMenuId" parameterType="string" resultType="string">
        SELECT
            user_id
        FROM
            t_user_role
        WHERE
            role_id IN ( SELECT rm.role_id FROM t_role_menu rm WHERE rm.menu_id = #{menuId} )
    </select>
</mapper>