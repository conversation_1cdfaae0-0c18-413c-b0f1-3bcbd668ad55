package cc.mrbird.febs.produceInspection.entity;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表主表信息
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionMainInfo {
    private Integer id;
    private String sbu;
    private String functions;
    private String section;
    private String line;
    private String series;
    private String partNumber;
    private String produceClass;
    private String date;
    private String remark;
    private Integer status;
    private String examinerWorkId;
    private String examinerName;
    private String reviewerName;
    private String reviewerWorkId;
    private String reviewerSignDate;
    private String resultValue;
    private String startTime;
    private String endTime;
    private String projectRemark;
}