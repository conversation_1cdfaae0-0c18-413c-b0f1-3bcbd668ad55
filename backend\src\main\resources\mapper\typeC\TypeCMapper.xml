<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.typeC.dao.TypeCMapper">

 <select id="selectAllTable" resultType="cc.mrbird.febs.typeC.entity.dto.TableNameDto">
  USE AOI_DATA_A_TYPEC
  SELECT name
  FROM sys.tables;
 </select>

 <select id="selectAllViews" resultType="cc.mrbird.febs.typeC.entity.dto.TableNameDto">
  USE AOI_DATA_A_TYPEC
  SELECT name
  FROM sys.views;
    </select>


 <select id="selectAllProduction" resultType="java.lang.String">
  USE AOI_DATA_A_TYPEC
  SELECT DISTINCT production  FROM AOI_DATA_CONFIGURE  WHERE customerName !='非A客戶'
 </select>

 <select id="selectAllLineByProduction" resultType="java.lang.String">
  USE AOI_DATA_A_TYPEC
  SELECT DISTINCT productionLine  FROM AOI_DATA_CONFIGURE WHERE production=#{param1} AND customerName=#{param2}
 </select>

 <select id="selectAllSheetName" resultType="java.lang.String">
  USE AOI_DATA_A_TYPEC
  SELECT sheetName  FROM AOI_DATA_CONFIGURE
  WHERE production=#{production} AND productionLine=#{productionLine} AND customerName=#{customerName}
 </select>

 <select id="selectCustomerName" resultType="java.lang.String">
  USE AOI_DATA_A_TYPEC
  SELECT DISTINCT customerName  FROM AOI_DATA_CONFIGURE
  WHERE production=#{production}  <!--AND customerName !='非A客戶'-->
 </select>

 <select id="selectColumns" resultType="java.lang.String">
  SELECT NAME FROM SYSCOLUMNS WHERE ID=OBJECT_ID(#{tableName}) ORDER BY colid
    </select>
</mapper>