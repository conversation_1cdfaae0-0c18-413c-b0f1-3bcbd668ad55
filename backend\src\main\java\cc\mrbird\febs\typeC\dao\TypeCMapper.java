package cc.mrbird.febs.typeC.dao;


import cc.mrbird.febs.typeC.entity.dto.TableNameDto;
import cc.mrbird.febs.typeC.entity.qo.ProductionAndProductionLineQo;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("150")
public interface TypeCMapper {
    List<TableNameDto> selectAllTable();
    List<TableNameDto> selectAllViews();

    List<String> selectAllProduction();
    List<String> selectAllLineByProduction(String production,String customerName);
    List<String> selectAllSheetName(ProductionAndProductionLineQo qo);
    List<String> selectCustomerName(String production);

    List<String> selectColumns(String tableName);

}
