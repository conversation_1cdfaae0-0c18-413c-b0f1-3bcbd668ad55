package cc.mrbird.febs.agv.service.impl;

import cc.mrbird.febs.agv.dao.AGVMapper;
import cc.mrbird.febs.agv.entity.dto.AgvDto;
import cc.mrbird.febs.agv.param.AgvQueryParam;
import cc.mrbird.febs.agv.service.AGVService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/2/25
 * @Time: 11:12
 */
@Service
public class AGVServiceImpl implements AGVService {
    private final AGVMapper emapper;

    @Autowired
    public AGVServiceImpl(AGVMapper emapper) {
        this.emapper = emapper;
    }

    @Override
    public List<AgvDto> queryAgv(AgvQueryParam agvQueryParam) {
        //查出agv信息
        List<AgvDto> list=emapper.queryAgv(agvQueryParam);
        //过滤agv小车中的path和detectStepList的换行符/n和回车符/r
        list.stream().forEach(item->{
            String path = item.getPath();
            String tempString = path.replaceAll("\n", "");
            tempString=tempString.replaceAll("\r","");
            item.setPath(tempString);

            String detectStepList = item.getDetectStepList();
            tempString=detectStepList.replaceAll("\n","");
            tempString=tempString.replaceAll("\r","");
            item.setDetectStepList(tempString);
        });
        return list;
    }
}
