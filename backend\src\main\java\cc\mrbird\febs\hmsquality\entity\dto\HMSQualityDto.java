package cc.mrbird.febs.hmsquality.entity.dto;

import cc.mrbird.febs.hmsquality.entity.HmsPicture;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * Author: 李囯斌
 * Date: 2024/11/7
 * Time: 10:34
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class HMSQualityDto {
    @ApiModelProperty(value = "紅墨水品質ID",position = 0)
    private Integer id;
    @ApiModelProperty(value = "系列",example = "1700SKT",position = 1)
    private String production;
    @ApiModelProperty(value = "線體",example = "V1",position = 2)
    private String productionLine;
    @ApiModelProperty(value = "日期",example = "2024-11-06",position = 3)
    private String date;
    @ApiModelProperty(value = "日期代碼",example = "4B137",position = 4)
    private String dateCode;
    @ApiModelProperty(value = "料號",example = "PE41897-01NK5-1H",position = 5)
    private String pfn;
    @ApiModelProperty(value = "工號",example = "F0841747",position = 6)
    private String userID;
    @ApiModelProperty(value = "用戶名",example = "覃金華",position = 7)
    private String userName;
    @ApiModelProperty(value = "上傳日期",example = "2024-11-06 18:53:50",position = 7)
    private String insDate;
    @ApiModelProperty(value = "紅墨水圖片總數",example = "0",position = 7)
    private Integer sum;
    @ApiModelProperty(value = "紅墨水圖片地址",example = "",position = 8)
    private List<String> HmsPictureList;
}
