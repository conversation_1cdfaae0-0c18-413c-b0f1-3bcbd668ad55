<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.lessDaysheet.dao.workStationMapper">
 <select id="selectWorkStation" resultType="java.lang.String">
     USE Schedule
     select Equipname_auto from t_configure_equipmentData
    where ProductRange= #{production} AND ProductionLine =#{productionLine}
 </select>

 <select id="selectProductionAndProductionLine" resultType="java.lang.String">
     select distinct ProductRange AS production FROM Schedule.dbo.t_configure_equipmentData
     WHERE
         Section =#{section} AND pl=#{pl}
 </select>

 <select id="selectProductionLine" resultType="java.lang.String">
     select distinct ProductionLine AS productionLine FROM Schedule.dbo.t_configure_equipmentData
     WHERE
     ProductRange=#{production} AND Section=#{discern}
 </select>

 <select id="selectOrNullByProductioon" resultType="java.lang.String">
     select distinct ProductRange  FROM Schedule.dbo.T_Data_Group_Eq
     WHERE
     pl=#{pl} AND discern=#{section}
    </select>

 <select id="selectNULLByProductionLine" resultType="java.lang.String">
     select distinct ProductionLine  FROM Schedule.dbo.T_Data_Group_Eq
     WHERE
     ProductRange=#{production} AND discern=#{discern}
    </select>

 <select id="selectWorkStationByPl2AndPl3" resultType="java.lang.String">
     select distinct Equipment_Name  FROM Schedule.dbo.T_Data_Group_Eq
     WHERE
     ProductRange=#{production} AND ProductionLine=#{productionLine}
    </select>
</mapper>