package cc.mrbird.febs.rdProject.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的團隊表實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectTeam {

    @ApiModelProperty(value = "团队ID（0表示无归属）", position = 0)
    private Long teamId;

    @ApiModelProperty(value = "团队名称", position = 1)
    private String teamName;

    @ApiModelProperty(value = "厂区", position = 2)
    private String siteArea;

    @ApiModelProperty(value = "产品处", position = 3)
    private String sbu;

    @ApiModelProperty(value = "机能", position = 4)
    private String functions;

    @ApiModelProperty(value = "课别", position = 5)
    private String section;
}
