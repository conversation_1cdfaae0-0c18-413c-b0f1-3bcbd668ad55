package cc.mrbird.febs.wxby.contorller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.wxby.dao.SelectMapper;
import cc.mrbird.febs.wxby.dao.WXBYMapper;
import cc.mrbird.febs.wxby.entity.*;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
*
* <AUTHOR> @date
*/
@RestController
@RequestMapping("/wxby")
@ResponseBody
@Api(tags = "设备维修保养表")
public class WXBYController {
    @Autowired
    private WXBYMapper wxbyMapper;

    @Autowired
    private SelectMapper selectMapper;

    /**
     *查询系列线体设备名称
     */
    @GetMapping("/queryEquipment")
    @ApiOperation(value = "查询系列",notes = "查询系列")
    public FebsResponse queryEquipment(@RequestParam("pl") String pl,
                                       @RequestParam("discern")String discern){
        if(discern.equals("undefined")){
            discern=null;
        }
        return new FebsResponse().data(wxbyMapper.queryProductRange(pl,discern));
    }

        @GetMapping("/queryProductionLine")
        @ApiOperation(value = "根据系列查询线体")
        public FebsResponse getProductionLine(@RequestParam("ProductRange") String ProductRange,
                                              @RequestParam("discern") String discern){
        if(discern.equals("undefined")){
            discern=null;
        }
        return new FebsResponse().data(wxbyMapper.queryProductionLine(ProductRange,discern));
        }

    /**
     * 根据系列线体获取设备数据*/
    @ApiOperation(value = "根据系列线体获取设备数据",notes = "根据系列线体获取设备数据")
    @GetMapping("/getEquipment")
     public FebsResponse getEquipment(
             @RequestParam("ProductRange") String ProductRange,
             @RequestParam("ProductionLine") String ProductionLine) {
       // System.out.println(ProductionLine+"线体是---");
         return new FebsResponse().data(wxbyMapper.queryEquipment(ProductRange,ProductionLine));
     }
     @ApiOperation(value = "根据设备Id获取设备的异常和解决对策和改善原因等",notes = "根据设备Id获取设备的异常和解决对策和改善原因等")
     @GetMapping("/getCauation")
     public FebsResponse getCausation(@RequestParam("EquipmentId") String EquipmentId){
        List<ErrorCause> list =wxbyMapper.queryCause(EquipmentId);
        return new FebsResponse().data(list);
     }




     @ApiOperation(value = "根据EquipmentId获取组名",notes = "根据EquipmentId获取组名")
     @GetMapping("/getTSR")
     public FebsResponse getTSR(@RequestParam("EquipmentId") String EquipmentId){
        //根据Equipment_id获取组名
        //根据前端传来的EquipmentId做处理
        return new FebsResponse().data(wxbyMapper.queryUser(EquipmentId));
     }

     /**
      * 选择生技
      * */
     @ApiOperation(value = "根据PL、課别获取要推送人的名单",notes = "根据PL、課别获取推送人的名单")
     @GetMapping("/getTSUserByGroup")
     public FebsResponse getTSUserByGroup(@RequestParam("discern") String discern,
                                          @RequestParam("pl") String pl){
         if(discern ==null||discern.equals("undefined") ){
             discern=null;
         }
         if (pl.equals("IDS2")){
             List<String> list1 = wxbyMapper.queryUserFirst(pl,null);
             return new FebsResponse().data(list1);
         }
         List<String> list =wxbyMapper.queryUserFirst(pl,discern);
         return new FebsResponse().data(list);
     }

     //推送接口
     @ApiOperation(value = "线长推送",notes = "线长推送接口")
     @PostMapping("/getTS")
     @ApiResponses({
             @ApiResponse(code = 200,message = "OK",response = MaintenanceLog.class)
     })
    public FebsResponse TSWS(@RequestBody MaintenanceLog maintenanceLog) {
         maintenanceLog.setDataType("表單係統填寫");
         wxbyMapper.insertwxby(maintenanceLog);
         return new FebsResponse().code("200").message("推送成功");
    }

    @ApiOperation(value = "获取签核记录数据",notes = "获取签核记录数据")
    @PostMapping("/queryAllEquipmentList")
    public FebsResponse queryAllEquipmentList(@RequestBody TStatus wxbyDto) {
        List<MaintenanceLog> list =null;
        int count =wxbyMapper.countByDataPerms(wxbyDto.getLoginer());
        String roleName =wxbyDto.getRoleName();
        if (roleName==null){
            List<MaintenanceLog> map =wxbyMapper.selectDataByPermsVo(wxbyDto);
            return new FebsResponse().code("200").data(map);
        }
        if(wxbyDto.getDiscern() ==null||wxbyDto.getDiscern().equals("undefined") ){
            wxbyDto.setDiscern(null);
        }
        switch (roleName) {
            case "生技組長":
                list = wxbyMapper.queryEquipmentListByWXHZ(wxbyDto);
                break;
            case "線長":
                list = wxbyMapper.queryEquipmentListByFqr(wxbyDto);
                break;
            case "責任生技":
                list = wxbyMapper.queryEquipmentListByZRSJ(wxbyDto);
                break;
            case "生技代班":
                list = wxbyMapper.queryEquipmentListByWXSH(wxbyDto);
                break;
            case "生產組長":
                list = wxbyMapper.queryEquipmentListBySCZZ(wxbyDto);
                break;
            case "品管確認人":
                if (wxbyDto.getPl().equals("IDS2")){
                    list = wxbyMapper.queryEquipmentListByPGQRRAndIDS2(wxbyDto);
                }else {
                    list = wxbyMapper.queryEquipmentListByPGQRR(wxbyDto);
                }

                break;
        }
        if (count==1){
            List<MaintenanceLog> map =wxbyMapper.selectDataByPermsVo(wxbyDto);
            return new FebsResponse().code("200").data(map);
        }else {
            return new FebsResponse().code("200").data(list);
        }
    }
    // 模糊搜索
    @ApiOperation(value = "异常原因模糊搜索",notes = "异常原因模糊搜索")
    @GetMapping("/queryCausation")
    public FebsResponse queryCausation(@RequestParam("Causation") String Causation){
        if (Causation == null || Causation.length()==0 || Causation =="") {
            return null;
        }else {
            return new FebsResponse().data(wxbyMapper.queryCausation(Causation));
        }
    }
/*    @ApiOperation(value = "查询维修/保养人",notes = "查询维修保养人")
    @GetMapping("/queryUserByList")
    public  FebsResponse queryUseByList(@RequestParam("pl") String pl,@RequestParam("discern") String discern){
         return new FebsResponse().data(wxbyMapper.queryUserByList(pl,discern));
    }*/

    @ApiOperation(value = "查询解决对策",notes = "解决对策")
    @GetMapping("/queryCountermeasures")
    public FebsResponse queryCountermeasures(@RequestParam("Causation") String Causation){
         return new FebsResponse().data(wxbyMapper.queryCountermeasures(Causation));
    }
     //查询未签核数据
    @ApiOperation(value = "查询所有未签核数据",notes = "查询所有未签核数据")
    @GetMapping("/queryQHList")
    public FebsResponse queryQHList(){
         return  new FebsResponse().data(wxbyMapper.queryQHList());
    }
    //QH2接口
    @ApiOperation(value = "责任生技签核接口",notes = "责任生技签核接口")
    @PostMapping("/qh2")
    public  FebsResponse qh2( @RequestBody MaintenanceLog maintenanceLog){
         wxbyMapper.updateMaintenance(maintenanceLog);
         return new FebsResponse().code("200").message("推送成功");
    }
    //根据工号识别身份
    @ApiOperation(value = "根据工号查询角色身份",notes = "根据工号查询角色身份")
    @GetMapping("/queryRole")
    public  FebsResponse queryROle(@RequestParam("CardId") String CardId){

         return new FebsResponse().data(wxbyMapper.queryRole(CardId));
    }
    //查询责任生技推送带班生技
    @ApiOperation(value = "查询带班生技人员",notes = "根据身份查询带班生技")
    @GetMapping("/queryZRSJDB")
    public FebsResponse queryZRSJDB(@RequestParam("pl") String pl,
                                    @RequestParam("discern")String discern){
        if(discern ==null||discern.equals("undefined") ){
            discern=null;
        }
         return new FebsResponse().data(wxbyMapper.queryZRSJDB(pl,discern));
    }
    //查询生技组长接口
    @ApiOperation(value = "查询生技组长",notes = "查询生技组长")
    @GetMapping("/QueryDBZZ")
    public FebsResponse QueryDBZZ(@RequestParam("pl")String pl,
                                  @RequestParam("discern")String discern){
        if(discern ==null||discern.equals("undefined") ){
            discern=null;
        }
        if (pl.equals("IDS1")){
            return new FebsResponse().data(wxbyMapper.querySJZZByUpdate(pl,discern));
        }
         return new FebsResponse().data(wxbyMapper.querySJZZ(pl,discern));
    }
    //带班生技推送接口
    @ApiOperation(value = "带班生技推送接口",notes = "带班生技推送接口")
    @PostMapping("/TSByZRSJ")
    public FebsResponse TSByZRSJ(@RequestBody MaintenanceLog maintenanceLog){
         wxbyMapper.updataTS(maintenanceLog);
        //System.out.println(maintenanceLog.getWxhz());
         return new FebsResponse().code("200").message("推送成功");
    }

    //生技组长推送接口
    @ApiOperation(value = "生技组长推送就接口",notes = "生技组长推送接口")
    @PostMapping("/TSByZJZZ")
    public FebsResponse TSByZJZZ(@RequestBody MaintenanceLog maintenanceLog){
         wxbyMapper.updataTSBySJzz(maintenanceLog);
         return new FebsResponse().code("200").message("推送成功");
    }
    //查询品管确认人
    @ApiOperation(value = "查询品管确认人",notes = "查询品管确认人")
    @GetMapping("/qryPGQRR")
    public  FebsResponse queryPGQRR(@RequestParam("pl")String pl){
         //List<String> list  = wxbyMapper.queryPGQRR();
         return new FebsResponse().data(wxbyMapper.queryPGQRR(pl));
    }
    //品管确认人签核
    @ApiOperation(value = "品管确认人意见推送接口",notes = "品管确认人意见推送接口")
    @PostMapping("/TSByPgqrr")
    public  FebsResponse TSByPgqrr(@RequestBody MaintenanceLog maintenanceLog){
        wxbyMapper.updataTSByPgqrr(maintenanceLog);
        return  new FebsResponse().code("200").message("推送成功");
    }
    //签核数据回显给代签生技
    @ApiOperation(value = "发起人数据给责任生技",notes = "责任生技审核数据内容")
    @GetMapping(value = "selectByqh2")
    public  FebsResponse selectByqh2(@RequestParam("Id") Integer Id ){
         return new FebsResponse().data(wxbyMapper.selectByqh2(Id));
    }
    @GetMapping("/selectSCXZ")
    @ApiOperation(value = "根據系列線體查詢線長名稱",notes = "根據系列線體查詢線長名稱")
    public FebsResponse selectSCXZ(@RequestParam("Production")String Production,
                                 @RequestParam("ProductionLine") String ProductionLine,
                                 @RequestParam("EquipmentId")String EquipmentId){
        return new FebsResponse().data(wxbyMapper.selectBySCXZ(Production,ProductionLine,EquipmentId));
    }
    @PostMapping("/TSByLineLeader")
    @ApiModelProperty(value = "生技組長推送給生產線長接口",notes = "生技組長推送給生產線長接口")
    public  FebsResponse  TSByLineLeader(@RequestBody MaintenanceLog maintenanceLog){
            wxbyMapper.updateByLineLeader(maintenanceLog);
        return new FebsResponse().code("200").message("推送成功");
    }
    //查詢生產組長
    @GetMapping("/selectBYGroupLeader")
    @ApiOperation(value = "查詢生產組長",notes = "查詢生產組長")
    public FebsResponse selectBYGroupLeader(@RequestParam("Production") String Production,
                                            @RequestParam("ProductionLine") String ProductionLine){
        return new FebsResponse().data(wxbyMapper.selectBYGroupLeader(Production,ProductionLine));
    }
    //生產線長推送給生產組長
    @PostMapping("/TSByGroupLeader")
    @ApiOperation(value = "生產線長推送給生產組長",notes ="生產線長推送給生產組長")
    public FebsResponse TSByGroupLeader(@RequestBody MaintenanceLog maintenanceLog){
         wxbyMapper.updateByGroupLeader(maintenanceLog);
        return new FebsResponse().code("200").message("推送成功");
    }
    @PostMapping("/TSByQC")
    @ApiOperation(value = "品管無追溯簽核",notes = "品管無追溯簽核")
    public FebsResponse TSByQC(@RequestBody MaintenanceLog maintenanceLog){
            wxbyMapper.updateByQC(maintenanceLog);
        return new FebsResponse().code("200").message("推送成功");
    }
    //有追溯簽核
    @PostMapping("/TSByZs")
    @ApiOperation(value = "品管有追溯簽核推送給生產線長",notes = "品管有追溯簽核推送給生產線長")
    public  FebsResponse TSByZs(@RequestBody MaintenanceLog maintenanceLog){
            wxbyMapper.TSByZs(maintenanceLog);
         return  new FebsResponse().code("200").message("推送成功");
    }

    @GetMapping("/queryTest")
    @ApiOperation(value = "叫修APP数据接口",notes = "叫修APP数据接口")
    public FebsResponse queryTest(){
        // List<MaintenanceLog> list1  =wxbyMapper.queryEquipmentList(Production,ProductionLine,zrsj,classInfo,recordDate,endDate,startDate);
         List<EquipmentNewError> list = selectMapper.selectAllByLine();
         Map<String,Object> map = new HashMap<>();
         map.put("list",list);
         return new FebsResponse().data(map);
    }
    @GetMapping("/queryMaintenanceLog")
    public FebsResponse queryMaintenanceLog()  {
        List<MaintenanceLog> list = selectMapper.queryALlEquipmentErrorLog();
        List<MaintenanceLog> list2 = wxbyMapper.selectAllByGetDate();
        list.removeAll(list2);
        System.out.println(list2);
        List<MaintenanceLog> errorList = new ArrayList<>();
      //  int  count = wxbyMapper.countMaintenance();
            for (MaintenanceLog maintenanceLog : list) {
                String production = maintenanceLog.getProduction();
                String productionLine = maintenanceLog.getProductionLine();
                String recordDate = maintenanceLog.getRecordDate();
                String equipmentName = maintenanceLog.getEquipmentName();
                String equipmentId = maintenanceLog.getEquipmentId();
                String startTime = maintenanceLog.getStartTime();
                String faultType = maintenanceLog.getFaultType();
                String status = maintenanceLog.getStatus();
                String QH1 = maintenanceLog.getQh1();
                String QH2 = maintenanceLog.getQh2();
                String QH3 = maintenanceLog.getQh3();
                String QH4 = maintenanceLog.getQh4();
                String QH5 = maintenanceLog.getQh5();
                String fqr = maintenanceLog.getFqr();
                String zrsj = maintenanceLog.getZrsj();
                String classInfo = maintenanceLog.getClassInfo();
                MaintenanceLog m1 = new MaintenanceLog();
                m1.setProduction(production);
                m1.setProductionLine(productionLine);
                m1.setRecordDate(recordDate);
                m1.setEquipmentName(equipmentName);
                m1.setEquipmentId(equipmentId);
                m1.setStartTime(startTime);
                m1.setFaultCause(faultType);
                m1.setStatus(status);
                m1.setQh1(QH1);
                m1.setQh2(QH2);
                m1.setQh3(QH3);
                m1.setQh4(QH4);
                m1.setQh5(QH5);
                m1.setFqr(fqr);
                m1.setZrsj(zrsj);
                m1.setClassInfo(classInfo);
                System.out.println(m1);
                errorList.add(m1);
                errorList.forEach(System.out::println);
                }
                wxbyMapper.insertMaintenanceLog(errorList);
        return new FebsResponse().data(selectMapper.queryALlEquipmentErrorLog());
    }
    @GetMapping("/testUpdate")
    public FebsResponse testUpdate(){
        List<MaintenanceLog> list = selectMapper.queryALlEquipmentErrorLog();
        List<MaintenanceLog> list2 = wxbyMapper.selectAllByGetDate();
        list.removeAll(list2);
        System.out.println(list2);
        wxbyMapper.insertMaintenanceLog(list);
        return new FebsResponse().message("插入成功").code("200");
    }
    @ApiOperation(value = "品管确认人查询待签核记录数据")
    @PostMapping("selectDataByQc")
    public FebsResponse selectDataByQC(){
      List<MaintenanceLog>list=   wxbyMapper.selectDataByQC();
         return new FebsResponse().code("200").data(list);
    }

}














































































































































































































































/**
 *
 *  目标:    自由且欢乐
 *
 * */