package cc.mrbird.febs.customerCondition.service;

import cc.mrbird.febs.customerCondition.entity.*;
import cc.mrbird.febs.customerCondition.entity.dto.CustomerConditionDto;
import cc.mrbird.febs.customerCondition.param.CustomerConditionQueryParam;
import cc.mrbird.febs.customerCondition.param.CustomerConditionUserQueryParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄服務接口
 */
public interface CustomerConditionService {

    // 查詢客戶列表
    List<CustomerConditionCustomerData> queryCustomerList();

    // 根據ID查詢客戶
    CustomerConditionCustomerData queryCustomerById(Integer customerId);

    // 查詢廠區列表
    List<String> querySiteAreaList();

    // 根據廠區查詢產品処列表
    List<String> querySbuBySiteArea(String siteArea);

    // 根據廠區和產品処查詢機能列表
    List<String> queryFunctionsBySiteAreaAndSbu(String siteArea, String sbu);

    // 根據廠區、產品処和機能查詢課別列表
    List<String> querySectionBySiteAreaAndSbuAndFunctions(String siteArea, String sbu, String functions);

    // 查詢客戶條件記錄主要信息
    List<CustomerConditionMainInfo> queryCustomerConditionMainInfo(CustomerConditionQueryParam queryParam);

    // 查詢客戶條件記錄詳細信息
    List<CustomerConditionDto> queryCustomerConditionDto(CustomerConditionQueryParam queryParam);

    // 查詢客戶條件記錄文件
    CustomerConditionFile queryCustomerConditionFileById(Integer fileId);

    // 查詢客戶條件記錄用戶
    List<CustomerConditionUser> queryCustomerConditionUsers();

    // 查詢客戶條件記錄郵件
    List<CustomerConditionMail> queryCustomerConditionMails(Integer customerId);

    // 上傳客戶條件記錄文件
    CustomerConditionMainInfo uploadCustomerConditionFile(MultipartFile file, CustomerConditionMainInfo mainInfo) throws IOException;

    // 查詢客戶條件記錄詳情
    CustomerConditionDto queryCustomerConditionDetail(Integer mainInfoId);

    // 根據多條件查詢用戶信息
    List<CustomerConditionUser> queryCustomerConditionUsersByCondition(CustomerConditionUserQueryParam queryParam);

    // 查詢客戶條件記錄上傳次數
    int queryCustomerConditionUploadCount(String siteArea, String sbu, String functions, String section, Integer customerId);

    // 根據文件ID訪問文件
    void accessFileById(Integer fileId, javax.servlet.http.HttpServletResponse response) throws IOException;

    // 根據文件名訪問文件
    void accessFileByName(String fileName, javax.servlet.http.HttpServletResponse response) throws IOException;

    // 根據年月和文件名訪問文件
    void accessFileByYearMonthAndName(String year, String month, String fileName, javax.servlet.http.HttpServletResponse response) throws IOException;

    // 上傳客戶條件記錄summary文件
    CustomerConditionSummaryFile uploadCustomerConditionSummaryFile(MultipartFile file) throws IOException;

    // 下載最新的客戶條件記錄summary文件
    void downloadLatestCustomerConditionSummaryFile(javax.servlet.http.HttpServletResponse response) throws IOException;
}
