package cc.mrbird.febs.other.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelectDto {
    @ApiModelProperty(value = "维修组名",position=0)
    private String groupName;
    @ApiModelProperty(value = "系列",position=1)
    private String production;
    @ApiModelProperty(value = "线体",position=2)
    private String productionLine;
    @ApiModelProperty(value = "设备名称",position=3)
    private String equipmentName;
}
