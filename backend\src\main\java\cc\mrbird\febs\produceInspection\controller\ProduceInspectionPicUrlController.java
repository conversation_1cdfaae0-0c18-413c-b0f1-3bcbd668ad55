package cc.mrbird.febs.produceInspection.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.produceInspection.entity.ProduceInspectionPicUrl;
import cc.mrbird.febs.produceInspection.service.IProduceInspectionPicUrlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/20
 * @Time: 8:28
 */
@Api(tags = "生產自主檢查")
@RestController
@RequestMapping("/api/produceInspection")
public class ProduceInspectionPicUrlController {
    @Autowired
    private IProduceInspectionPicUrlService produceInspectionPicUrlService;

//    @ApiOperation(value = "上传单个图片，傳入點檢的ID参数", httpMethod = "POST")
//    @PostMapping(value = "/uploadPic")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "file", value = "图片文件", required = true, dataType = "__file"),
//            @ApiImplicitParam(name = "mainInfoId", value = "关联ID", required = true, dataType = "String")
//    })
//    public FebsResponse uploadPic(@RequestParam("file") MultipartFile file,
//                                  @RequestParam(value = "mainInfoId", required = false) String mainInfoId) {
//        try {
//            ProduceInspectionPicUrl data = produceInspectionPicUrlService.uploadPic(file, mainInfoId);
//            return new FebsResponse().code("200").data(data);
//        } catch (Exception e) {
//            return new FebsResponse().code("500").message(e.getMessage());
//        }
//    }



}
