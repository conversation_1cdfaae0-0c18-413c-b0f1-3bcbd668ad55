<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.wxby.dao.WXBYMapper">
    <insert id="insertwxby" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
    USE Schedule
  INSERT INTO T_Equipment_Maintenance(Production,ProductionLine,RecordDate,ClassInfo,EquipmentName,EquipmentId
  ,SCLH,StartTime,EndTime,LossTime,MaintenanceNature,ChangeStation,ChangeNumber,FaultType,FaultCause,CounterPlan,STATUS,QH1,QH2,QH3,QH4,QH5,FQR,ZRSJ,WXSH,WXHZ,PGQRR,QH11,QH22,QH33,pl,discern
        ,wxny,glfs,ssbw,th1,th2,th3,th4,th5,th6,th7,th8,th9,th10,th1Text,th2Text,th3Text,
        th4Text,th5Text,th6Text,th7Text,th8Text,th9Text,th10Text,other,insertId,dataType)
  VALUES(#{production},#{productionLine},#{recordDate},#{classInfo},#{equipmentName},#{equipmentId},#{sclh},#{startTime},#{endTime},#{lossTime},
    #{maintenanceNature},#{changeStation},#{changeNumber},#{faultType},#{faultCause},#{counterPlan},#{status},#{qh1},#{qh2},#{qh3},#{qh4},#{qh5},#{fqr},#{zrsj},#{wxsh},#{wxhz},#{pgqrr},#{qh11},#{qh22},#{qh33},#{pl},#{discern},
       #{wxny},#{glfs},#{ssbw},#{th1},#{th2},#{th3},#{th4},#{th5},#{th6},#{th7},#{th8},#{th9},#{th10},#{th1Text},#{th2Text}
        ,#{th3Text},#{th4Text},#{th5Text},#{th6Text},#{th7Text},#{th8Text},#{th9Text},#{th10Text},#{other},#{insertId},#{dataType})
    </insert>
    <insert id="insertMaintenanceLog">
        USE Schedule
  INSERT INTO T_Equipment_Maintenance(Production,ProductionLine,RecordDate,ClassInfo,EquipmentName,EquipmentId
  ,SCLH,StartTime,EndTime,LossTime,MaintenanceNature,ChangeStation,ChangeNumber,FaultType,FaultCause,CounterPlan,
            STATUS,QH1,QH2,QH3,QH4,QH5,FQR,ZRSJ,WXSH,WXHZ,PGQRR,QH11,QH22)
  VALUES
        <foreach collection="list"  item="item" separator=",">
            (#{item.production},
            #{item.productionLine},
            #{item.recordDate},
            #{item.classInfo},
            #{item.equipmentName},
            #{item.equipmentId},
            #{item.sclh},
            #{item.startTime},
            #{item.endTime},
            #{item.lossTime},
            #{item.maintenanceNature},
            #{item.changeStation},
            #{item.changeNumber},
            #{item.faultType},
            #{item.faultCause},
            #{item.counterPlan},
            #{item.status},
            #{item.qh1},
            #{item.qh2},
            #{item.qh3},
            #{item.qh4},
            #{item.qh5},
            #{item.fqr},
            #{item.zrsj},
            #{item.wxsh},
            #{item.wxhz},
            #{item.pgqrr},
            #{item.qh11},
            #{item.qh22})
        </foreach>
    </insert>
    <update id="updateMaintenance" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        update T_Equipment_Maintenance
<set>
      Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},ChangeStation=#{changeStation},
            EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
            MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan}<!--,pl=#{pl},discern=#{discern}-->,
            STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},ChangeNumber=#{changeNumber},QH33=#{qh33},
    wxny=#{wxny},glfs=#{glfs},ssbw=#{ssbw},th1=#{th1},th2=#{th2},th3=#{th3},th4=#{th4},th5=#{th5},th6=#{th6},th7=#{th7},th8=#{th8},th9=#{th9},th10=#{th10},th1Text=#{th1Text},th2Text=#{th2Text}
    ,th3Text=#{th3Text},th4Text=#{th4Text},th5Text=#{th5Text},th6Text=#{th6Text},th7Text=#{th7Text},th8Text=#{th8Text},th9Text=#{th9Text},th10Text=#{th10Text},other=#{other}
</set>
     WHERE
        Id=#{id}
    </update>
    <update id="updataTS" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        update  T_Equipment_Maintenance
        <set>
            Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},
            EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
            MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan},
            STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},ChangeNumber=#{changeNumber},ChangeStation=#{changeStation},QH33=#{qh33}<!--,pl=#{pl},discern=#{discern}-->,
            wxny=#{wxny},glfs=#{glfs},ssbw=#{ssbw},th1=#{th1},th2=#{th2},th3=#{th3},th4=#{th4},th5=#{th5},th6=#{th6},th7=#{th7},th8=#{th8},th9=#{th9},th10=#{th10},th1Text=#{th1Text},th2Text=#{th2Text}
            ,th3Text=#{th3Text},th4Text=#{th4Text},th5Text=#{th5Text},th6Text=#{th6Text},th7Text=#{th7Text},th8Text=#{th8Text},th9Text=#{th9Text},th10Text=#{th10Text},other=#{other}
        </set>
            WHERE
        Id=#{id}
    </update>
    <update id="updataTSBySJzz" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
    UPDATE  T_Equipment_Maintenance
        <set>
            Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},
            EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
            MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan},
            STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},ChangeNumber=#{changeNumber},ChangeStation=#{changeStation},QH33=#{qh33}<!--,pl=#{pl},discern=#{discern}-->
        </set>
        WHERE
        Id=#{id}
    </update>
    <update id="updataTSByPgqrr" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        update  T_Equipment_Maintenance
        <set>
            Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},
            EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
            MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan},
            STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},
            ChangeNumber=#{changeNumber},ChangeStation=#{changeStation},PGOpinion=#{pgopinion},TraceStartTime=#{traceStartTime},TraceEndTime=#{traceEndTime},
            QualityIdea=#{qualityIdea},ProductionDC=#{productionDC},ProductionNumber =#{productionNumber},NProductionDC=#{nproductionDC},
            NProductionNumber=#{nproductionNumber},ProductionMessage=#{productionMessage},QH11=#{qh11},QH22=#{qh22},QH33=#{qh33}<!--,pl=#{pl},discern=#{discern}-->
        </set>
    WHERE   Id=#{id}
    </update>
    <update id="updateByLineLeader" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        update  T_Equipment_Maintenance
            <set>
                Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},
                EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
                MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan},
                STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},
                ChangeNumber=#{changeNumber},ChangeStation=#{changeStation},PGOpinion=#{pgopinion},TraceStartTime=#{traceStartTime},TraceEndTime=#{traceEndTime},
                QualityIdea=#{qualityIdea},ProductionDC=#{productionDC},ProductionNumber =#{productionNumber},NProductionDC=#{nproductionDC},
                NProductionNumber=#{nproductionNumber},ProductionMessage=#{productionMessage},QH11=#{qh11},QH22=#{qh22},QH33=#{qh33}<!--,pl=#{pl},discern=#{discern}-->
            </set>
WHERE     Id=#{id}
    </update>
    <update id="updateByGroupLeader" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
            USE Schedule
            update T_Equipment_Maintenance
            <set>
                Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},
                EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
                MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan},
                STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},
                ChangeNumber=#{changeNumber},ChangeStation=#{changeStation},PGOpinion=#{pgopinion},TraceStartTime=#{traceStartTime},TraceEndTime=#{traceEndTime},
                QualityIdea=#{qualityIdea},ProductionDC=#{productionDC},ProductionNumber =#{productionNumber},NProductionDC=#{nproductionDC},
                NProductionNumber=#{nproductionNumber},ProductionMessage=#{productionMessage},QH11=#{qh11},QH22=#{qh22},GroupLeader=#{groupLeader},QH33=#{qh33}<!--,pl=#{pl},discern=#{discern}-->
            </set>
    WHERE   Id=#{id}
    </update>
    <update id="updateByQC" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        update T_Equipment_Maintenance
        <set>
            Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},
            EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
            MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan},
            STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},
            ChangeNumber=#{changeNumber},ChangeStation=#{changeStation},PGOpinion=#{pgopinion},TraceStartTime=#{traceStartTime},TraceEndTime=#{traceEndTime},
            QualityIdea=#{qualityIdea},ProductionDC=#{productionDC},ProductionNumber =#{productionNumber},NProductionDC=#{nproductionDC},
            NProductionNumber=#{nproductionNumber},ProductionMessage=#{productionMessage},QH11=#{qh11},QH22=#{qh22},QH33=#{qh33}<!--,pl=#{pl},discern=#{discern}-->
        </set>
WHERE    Id=#{id}
    </update>
    <update id="TSByZs" parameterType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        update T_Equipment_Maintenance
        <set>
            Production = #{production},ProductionLine = #{productionLine},RecordDate=#{recordDate},ClassInfo = #{classInfo},EquipmentName =#{equipmentName},
            EquipmentId = #{equipmentId},SCLH=#{sclh},LossTime =#{lossTime},
            MaintenanceNature =#{maintenanceNature},FaultType = #{faultType},FaultCause = #{faultCause},CounterPlan = #{counterPlan},
            STATUS = #{status},QH1 =#{qh1},QH2=#{qh2},QH3=#{qh3},QH4=#{qh4},QH5=#{qh5},FQR=#{fqr},ZRSJ=#{zrsj},WXSH=#{wxsh},WXHZ = #{wxhz},PGQRR=#{pgqrr},
            ChangeNumber=#{changeNumber},ChangeStation=#{changeStation},PGOpinion=#{pgopinion},TraceStartTime=#{traceStartTime},TraceEndTime=#{traceEndTime},
            QualityIdea=#{qualityIdea},ProductionDC=#{productionDC},ProductionNumber =#{productionNumber},NProductionDC=#{nproductionDC},
            NProductionNumber=#{nproductionNumber},ProductionMessage=#{productionMessage},QH11=#{qh11},QH22=#{qh22},QH33=#{qh33}<!--,pl=#{pl},discern=#{discern}-->
        </set>
        WHERE    Id=#{id}
    </update>
    <select id="queryProductionLine" resultType="java.lang.String">
       <!-- USE Schedule
SELECT DISTINCT ProductionLine FROM T_Data_Group_Eq WHERE ProductRange=#{ProductRange} AND discern =#{discern}-->
        USE Schedule
        SELECT DISTINCT ProductionLine FROM T_Data_Group_Eq
        <where>
            <if test="param2 != null and param2 != ''">
                AND discern=#{param2}
            </if>
            AND  ProductRange=#{param1}
        </where>
</select>
    <select id="queryProductRange" resultType="java.lang.String">
        USE Schedule
   <!-- SELECT DISTINCT ProductRange FROM T_Data_Group_Eq WHERE pl=#{pl} AND discern=#{discern}-->
        SELECT DISTINCT ProductRange FROM T_Data_Group_Eq
        <where>
            <if test="param2 != null and param2 != ''">
                AND discern=#{param2}
            </if>
            AND pl=#{param1}
        </where>
</select>
    <select id="queryEquipment" resultType="cc.mrbird.febs.wxby.entity.GroupEq">
        USE Schedule
        SELECT DISTINCT Equipment_Name AS EquipmentName,Equipment_id  AS EquipmentId FROM T_Data_Group_Eq WHERE ProductRange =#{param1} AND ProductionLine = #{param2}
    </select>
    <select id="queryCause" resultType="cc.mrbird.febs.wxby.entity.ErrorCause">
         USE Schedule
            SELECT ErrorType,Causation,Countermeasures FROM T_Data_ErrorCausation WHERE Equipment_id = #{EquipmentId}
    </select>
    <select id="queryUser" resultType="cc.mrbird.febs.wxby.entity.GroupEq">
     USE Schedule
    SELECT  GroupName  FROM T_Data_Group_Eq   WHERE Equipment_id = #{EquipmentId}
    </select>
    <select id="queryUserFirst" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT  name
        FROM t_configure_sjwx
        <where>
            <if test="param2 != null and param2 != ''">
             AND   discern =#{param2}
            </if>
          AND  pl=#{param1}
        </where>

    </select>
    <!--生技組長查詢SQL-->
    <select id="queryEquipmentList" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
       <where>

            <if test="production !=null and production !=''">
             AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>

            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
           AND discern=#{discern} AND pl=#{pl} AND WXHZ =#{loginer}
        </where>
    </select>






    <select id="queryCausation" resultType="java.lang.String">
        USE Schedule
       SELECT DISTINCT Causation FROM T_Data_ErrorCausation  WHERE Causation like concat('%','${_parameter}','%')
    </select>
    <select id="queryCountermeasures" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT Countermeasures FROM T_Data_ErrorCausation WHERE Causation = #{Causation}
    </select>
    <select id="queryUserByList" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT ZRSJ FROM T_Equipment_Maintenance WHERE pl=#{param1} AND discern=#{param2}
    </select>
    <select id="queryQHList" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
      USE Schedule
        SELECT Id,Production,ProductionLine,EquipmentName,EquipmentId,SCLH,StartTime,EndTime,LossTime,FQR,ZRSJ,RecordDate
        FROM T_Equipment_Maintenance WHERE STATUS = 'false'
    </select>
    <select id="queryRole" resultType="cc.mrbird.febs.wxby.entity.TGroupDate">
        USE Schedule
        SELECT RoleName FROM T_Data_Group WHERE CardId = #{CardId}
    </select>
    <select id="queryZRSJDB" resultType="java.lang.String">
        USE Schedule
        SELECT UserName FROM T_Data_Group
        <where>
            <if test="param2 != null and param2 != ''">
                AND discern=#{param2}
            </if>
           AND RoleName ='生技代班' AND pl=#{param1}
        </where>
    </select>
    <select id="querySJZZ" resultType="java.lang.String">
        USE Schedule
        SELECT UserName  FROM T_Data_Group
        <where>
            <if test="param2 != null and param2 != ''">
                AND discern=#{param2}
            </if>
            AND RoleName ='生技組長' AND pl=#{param1}
        </where>
       <!-- RoleName = '生技組長' AND pl=#{param1} AND discern=#{param2}-->
    </select>
    <select id="queryPGQRR" resultType="java.lang.String">
        USE Schedule
        SELECT  UserName FROM T_Data_Group WHERE RoleId='6' AND pl=#{pl}
    </select>
    <select id="queryFQR" resultType="java.lang.String">


    </select>
    <select id="selectByqh2" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        select *from T_Equipment_Maintenance WHERE Id =#{ID}
    </select>
    <select id="selectBySCXZ" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT FQR FROM T_Equipment_Maintenance WHERE Production =#{param1} AND ProductionLine = #{param2} AND EquipmentId = #{param3} AND STATUS ='false'
    </select>
    <select id="selectBYGroupLeader" resultType="java.lang.String">
       USE Schedule
       SELECT DISTINCT GroupLeaderName FROM T_Configure_Equipment   WHERE Production =#{param1} AND ProductionLine = #{param2}
    </select>
    <select id="countMaintenance" resultType="java.lang.String">
        USE Schedule
        SELECT COUNT(*) count From T_Equipment_Maintenance
        WHERE RecordDate = CONVERT(date,getdate()) GROUP BY EquipmentId HAVING COUNT(*) >1
    </select>
    <select id="selectAllByGetDate" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT
            Production,ProductionLine,RecordDate,EquipmentName,EquipmentId,SCLH,StartTime,EndTime,
            LossTime AS lossTime,MaintenanceNature,ChangeStation,FaultType,FaultCause,CounterPlan,
            STATUS,QH1,QH2,QH3,QH4,QH5,FQR,ZRSJ,ClassInfo
        FROM T_Equipment_Maintenance WHERE RecordDate =CONVERT(date,getdate())
    </select>

    <select id="selectDataByQC" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        SELECT * FROM T_Equipment_Maintenance WHERE STATUS='false' AND  QH4='true' AND QH5='false'
    </select>
    <!--BY 发起人-->
    <select id="queryEquipmentListByFqr" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>

            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>

            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            AND discern=#{discern} AND pl=#{pl} AND FQR =#{loginer}
        </where>
        ORDER BY RecordDate DESC
    </select>
    <!--BY 责任生技-->
    <select id="queryEquipmentListByZRSJ" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance <where>

        <if test="production !=null and production !=''">
            AND  Production =#{production}
        </if>
        <if test="productionLine !=null and productionLine !=''">
            AND ProductionLine =#{productionLine}
        </if>
        <if test="zrsj !=null and zrsj !=''">
            AND ZRSJ=#{loginer}
        </if>
        <if test="classInfo !=null and classInfo !=''">
            AND ClassInfo = #{classInfo}
        </if>
        <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
            AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
        </if>
        <if test="discern !=null and discern !=''">
            AND discern=#{discern}
        </if>
          AND pl=#{pl} AND ZRSJ =#{loginer}
    </where>
        ORDER BY RecordDate DESC
    </select>
<!--BY 生技代班-->
    <select id="queryEquipmentListByWXSH" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>
            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>

            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            <if test="discern !=null and discern !=''">
                AND discern=#{discern}
            </if>
              AND pl=#{pl} AND WXSH =#{loginer}
        </where>
        ORDER BY RecordDate DESC
    </select>
<!--BY 生技组长-->
    <select id="queryEquipmentListByWXHZ" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>
            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            <if test="discern !=null and discern !=''">
                AND discern=#{discern}
            </if>
            AND pl=#{pl}
        </where>
        ORDER BY STATUS ASC,QH2 ASC ,QH3 ASC, QH4 ASC, QH5 ASC,RecordDate DESC
    </select>
    <!--BY  品管确认人-->
    <select id="queryEquipmentListByPGQRR" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>

            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            <if test="discern != null and discern != ''">
                AND discern=#{discern}
            </if>
           AND pl=#{pl} AND PGQRR =#{loginer}
        </where>
    </select>
    <select id="queryEquipmentListBySCZZ" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>
            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            AND discern=#{discern} AND pl=#{pl}
        </where>

    </select>

    <select id="countByDataPerms" resultType="int">
        USE IDS_BK_PaperLessSystem
        select COUNT (perms) FROM t_config_dataPerms where userName=#{name}
    </select>

    <select id="selectDataByPermsVo" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>
            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            <if test="pl != null and pl != ''">
                AND pl=#{pl}
            </if>
            <if test="discern != null and discern != ''">
                AND discern=#{discern}
            </if>
        </where>
        ORDER BY STATUS ASC,QH2 ASC ,QH3 ASC, QH4 ASC, QH5 ASC,RecordDate DESC
    </select>

    <select id="queryInsertId" resultType="java.lang.Integer">
        USE Schedule
        SELECT insertId FROM T_Equipment_Maintenance WHERE insertId=#{id}
    </select>

    <select id="querySJZZByUpdate" resultType="java.lang.String">
        USE Schedule
        SELECT UserName  FROM T_Data_Group
        <where>
            <if test="param2 != null and param2 != ''">
                AND discern=#{param2}
            </if>
            AND RoleName ='生技組長' AND pl=#{param1}  AND RoleId='4'
        </where>

    </select>

    <select id="queryEquipmentListByPGQRRAndIDS2" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>

            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="zrsj !=null and zrsj !=''">
                AND ZRSJ=#{zrsj}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            <if test="discern != null and discern != ''">
                AND discern=#{discern}
            </if>
            AND pl=#{pl} <!--AND PGQRR =#{loginer}-->
        </where>
    </select>

    <select id="queryEquipmentListByWXHZANDIDS2" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE Schedule
        SELECT *
        FROM T_Equipment_Maintenance
        <where>
            <if test="production !=null and production !=''">
                AND  Production =#{production}
            </if>
            <if test="productionLine !=null and productionLine !=''">
                AND ProductionLine =#{productionLine}
            </if>
            <if test="classInfo !=null and classInfo !=''">
                AND ClassInfo = #{classInfo}
            </if>
            <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
                AND RecordDate &gt;= #{startDate} AND RecordDate &lt;= #{endDate}
            </if>
            <if test="discern !=null and discern !=''">
                AND discern=#{discern}
            </if>
            AND pl=#{pl}
        </where>
        ORDER BY STATUS ASC,QH2 ASC ,QH3 ASC, QH4 ASC, QH5 ASC,RecordDate DESC
    </select>
</mapper>