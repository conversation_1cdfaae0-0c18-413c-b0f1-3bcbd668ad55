package cc.mrbird.febs.comparison.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.comparison.dao.TDataComparisonMapper;
import cc.mrbird.febs.comparison.entity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@RestController
@ResponseBody
@Api(tags = "136比對")
@RequestMapping("/comparison")
public class DataComparisonController {
    @Autowired
    private TDataComparisonMapper tDataComparisonMapper;

    @PostMapping("/queryComparison")
    @ApiOperation(value = "136比對錶查詢頁面接口", notes = "136按條件查詢")
    public FebsResponse queryComparison(@RequestBody QueryDto queryDto) {
        List<TDataComparison> list = tDataComparisonMapper.selectComparison(queryDto);
        return new FebsResponse().data(list);
    }

    @PostMapping("/insertComparison")
    @ApiOperation(value = "136比對彈出框插入接口", notes = "136比對彈出框插入接口")
    public FebsResponse inertComparison(@RequestBody InsertDto insertDto) {
        // 定义时间范围
        LocalTime startTime = LocalTime.of(0, 0);
        LocalTime endTime = LocalTime.of(7, 50);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(new Date());
        String saveDate = sdf.format(new Date());
        // 输入的日期时间字符串
        String adjustedDateTimeString = adjustDate(formattedDate, startTime, endTime);
        adjustedDateTimeString=adjustedDateTimeString.substring(0,10);
        // 输出结果
       // System.out.println("调整后的日期时间: " + adjustedDateTimeString);
        insertDto.setRecordDate(adjustedDateTimeString);
        insertDto.setSaveDate(saveDate);
        boolean b = tDataComparisonMapper.insertComparison(insertDto);
        //  String b1 =(String) b;
        return new FebsResponse().insertMessage(b).code("200");
    }

    @GetMapping("/selectComparisonByGetDate")
    @ApiOperation(value = "136比對表只查詢當天新建接口", notes = "136比對表只查詢當天新建接口")
    public FebsResponse selectComparisonByGetDate(@RequestParam("production") String production,
                                                  @RequestParam("productionLine") String productionLine,
                                                  @RequestParam("pl") String pl) {
        return new FebsResponse().data(tDataComparisonMapper.selectComparisonByGetDate(production, productionLine, pl));
    }

    //生產組長批量確認接口
    @PostMapping("/insertByGroupLeader")
    @ApiOperation(value = "136比對錶組長確認接口", notes = "136比對錶組長批量確認接口")
    public FebsResponse insertByGroupLeader(@RequestBody List<GroupLeader> groupLeaders) {
        List<GroupLeader> gl = groupLeaders;
        GroupLeader gr = new GroupLeader();
        int b = 0;
        for (GroupLeader gg : groupLeaders) {
            Integer Id = gg.getId();
            String groupLeader = gg.getGroupLeader();
            gr.setId(Id);
            gr.setGroupLeader(groupLeader);
            b++;
        }
        gl.add(gr);
        tDataComparisonMapper.updateByGroupLeader(gl);
        return new FebsResponse().code("200");
    }

    //
    @PostMapping("/countByAbnormal")
    @ApiOperation(value = "統計線體狀態是否有異常", notes = "統計線體狀態是否有異常")
    public FebsResponse countByAbnormal(@RequestBody SelectAbnormalDto selectAbnormalDto) {
        List<TDataComparison> list = tDataComparisonMapper.selectComparisonByAbnormal(selectAbnormalDto);
                /*if (list.size()>0){
                    return  new FebsResponse().data(list);
                }else {
                    return new FebsResponse().data("0");
                }
*/
        return new FebsResponse().data(list);
    }

    @GetMapping("/selectAchievementRate")
    @ApiOperation(value = "查询达成率", notes = "查询达成率")
    public FebsResponse selectAchievementRate(@RequestParam("production") String production,
                                              @RequestParam("productionLine") String productionLine,
                                              @RequestParam("recordDate") String recordDate,
                                              @RequestParam("industry") String industry,
                                              @RequestParam("classInfo") String classInfo
    ) {
        List<String> list = tDataComparisonMapper.selectAchievementRate(production, productionLine, recordDate, industry, classInfo);
        return new FebsResponse().data(list);
    }

    @ApiOperation(value = "136比对表删除接口", notes = "根据ID删除")
    @PostMapping("/delete")
    public FebsResponse delete(@RequestParam("id") Integer id) {
        boolean b = tDataComparisonMapper.delete(id);
        if (b) {
            return new FebsResponse().code("200").message("删除成功");
        }
        return new FebsResponse().code("500").message("删除失败");
    }

    @ApiOperation(value = "136比對錶根據PL、系列查詢料號",notes = "請傳PL、課別、系列、線體")
    @PostMapping("/getPartNumberByPL")
    public FebsResponse getPartNumberByPL(@RequestBody PartNumberDto partNumberDto){
        List<String> list =tDataComparisonMapper.selectPartNumberByPL(partNumberDto);
        return new FebsResponse().code("200").data(list);
    }


    //根据班别时间 日期减去一天
    public static String adjustDate(String dateTimeString, LocalTime startTime, LocalTime endTime) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将字符串解析为 LocalDateTime
        LocalDateTime dateTime = LocalDateTime.parse(dateTimeString, formatter);
        LocalTime currentTime = dateTime.toLocalTime();
        LocalDateTime adjustedDateTime;
        // 判断当前时间是否在指定范围内
        if (currentTime.isAfter(startTime) && currentTime.isBefore(endTime)) {
            // 在范围内，日期减一天
            adjustedDateTime = dateTime.minusDays(1);
        } else {
            // 不在范围内，返回原日期
            adjustedDateTime = dateTime;
        }

        // 将调整后的日期时间转换为字符串
        return adjustedDateTime.format(formatter);
    }


}
