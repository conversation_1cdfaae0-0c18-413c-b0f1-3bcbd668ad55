package cc.mrbird.febs.wxby.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "T_Data_ErrorCausation")
public class TCausation {

    @TableField(value = "EquipmentId")
    private String EquipmentId;
    @TableField(value = "ErrorCod")
    private String ErrorCod;
    @TableField(value = "ErrorType")
    private String ErrorType;
    @TableField(value = "Causation")
    private String Causation;
    @TableField(value = "Countermeasures")
    private String Countermeasures;
    @TableField(value = "Editor")
    private String Editor;
    @TableField(value = "EditTime")
    private String EditTime;

}
