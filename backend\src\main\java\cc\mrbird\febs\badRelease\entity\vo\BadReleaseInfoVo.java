package cc.mrbird.febs.badRelease.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BadReleaseInfoVo {

    private Integer id;

    //課別
    private String section;

    //線體
    private String line;

    //料號
    private String materialNumber;

    //不良釋放日期
    private String badReleaseDate;

    //不良釋放人
    private String name;

    //Ng狀態
    private String NgStatus;

    //Ng原因
    private String NgCause;

    //AI檢測圖片
    private String AIPicture;

    //CCD檢測圖片
    private String CCDPicture;
}
