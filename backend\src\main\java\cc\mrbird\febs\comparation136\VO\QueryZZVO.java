package cc.mrbird.febs.comparation136.VO;

import cc.mrbird.febs.comparation136.entity.QueryIPQC;
import cc.mrbird.febs.comparation136.entity.QueryZZ;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryZZVO implements Serializable {

    private List<QueryZZ> queryIPQCS = new ArrayList<>();
}
