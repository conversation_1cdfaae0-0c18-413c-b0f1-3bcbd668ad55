package cc.mrbird.febs.equipmentSelfInspection.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.equipmentSelfInspection.entity.EquipmentInspectionProjectData;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionMainInfoDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionProjectDto;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentInspectionQueryParam;
import cc.mrbird.febs.equipmentSelfInspection.service.EquipmentInspectionMaintenanceService;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * Author: 李国斌
 * Date: 2024-09-9
 * Time: 下午 16:36
 */
@Api(tags = "設備點檢保養")
@RequestMapping("/equipmentInspectionMaintenance")
@Slf4j
@RestController
@ResponseBody
public class EquipmentInspectionMaintenanceController {
    private final EquipmentInspectionMaintenanceService equipmentInspectionMaintenanceService;

    /**
     * 點檢類型字典
     */
    private final HashMap<String,String> inspectionTypeDictionary=new HashMap<String,String>(){
        {
            put("1","日常点检");
            put("2","周保养");
            put("3","月保养");
            put("4","季保养");
        }
    };

    /**
     * 課別對應SBU字典
     */
    private final HashMap<String,String> discern2Pl=new HashMap<String,String>(){
        {
            put("一課","IDS1");
            put("二課","IDS1");
            put("A4三課","IDS3");
        }
    };

    /**
     * SBU對應課別字典
     */
    private List<String>  pl2Discern(String pl){
        List<String> ids1 = new ArrayList<String>();
        ids1.add("一課");
        ids1.add("二課");

        List<String> ids2 = new ArrayList<String>();

        List<String> ids3 = new ArrayList<String>();
        ids3.add("A4三課");

        switch (pl){
            case "IDS1":
                return ids1;
            case "IDS2":
                return ids2;
            case "IDS3":
                return ids3;
            default:
                return null;
        }
    }



    @Autowired
    public EquipmentInspectionMaintenanceController(EquipmentInspectionMaintenanceService equipmentInspectionMaintenanceService) {
        this.equipmentInspectionMaintenanceService = equipmentInspectionMaintenanceService;
    }


    //根據產品処和機能查詢課別
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU",value="產品処,IDS1|IDS2|IDS3",example = "IDS1"),
            @ApiImplicitParam(name = "functions",value="機能,裝配|成型|電鍍|衝壓",example = "裝配")
    })
    @GetMapping("/queryDiscern")
    @ApiOperation("/獲取課別")
    public FebsResponse queryDiscern(@RequestParam(value = "SBU",required = false)String SBU,
                                     @RequestParam(value = "functions",required = false)String functions){
        List<String> list;
        try {
            //數據庫中的SBU均為IDS，暫時先根據SBU獲取課別
            list=pl2Discern(SBU);
//            list = equipmentInspectionMaintenanceService.queryDiscern("IDS", functions);
        }
        catch (Exception e){
                return new FebsResponse().code("500").message("錯誤參數SBU："+SBU+"和functions："+functions);
            }
        return  new FebsResponse().code("200").data(list);
    }

    //查詢產品処
    @GetMapping("/querySBU")
    @ApiOperation("/獲取產品処")
    public FebsResponse querySBU(){
        List<String> list;
        try {
            list = equipmentInspectionMaintenanceService.querySBU();
            if(list.contains("IDS")){
                list.remove("IDS");
                for (int i = 0; i < 3; i++) {
                    list.add("IDS"+(i+1));//暫時返回ids1\ids2\ids3
                }
            }
        }
        catch (Exception e){
            return new FebsResponse().code("500").message("錯誤");
        }
        return  new FebsResponse().code("200").data(list);
    }

    //根據產品処查詢機能
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU",value="產品処,IDS1|IDS2|IDS3",example = "IDS1"),
    })
    @GetMapping("/queryFunctions")
    @ApiOperation("/根據產品処查詢機能")
    public FebsResponse queryFunctions(@RequestParam(value = "SBU",required = false)String SBU){
        List<String> list;
        try {
            //數據庫中的SBU均為IDS，暫時先將其固定爲IDS
            list = equipmentInspectionMaintenanceService.queryFunctions("IDS");
        }
        catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數SBU："+SBU);
        }
        return  new FebsResponse().code("200").data(list);
    }

    //根據工號查詢產品処、機能和課別
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SJ_UserID",value="工號,G5347608|F0842364|F0529150",example = "F0842364")
    })
    @GetMapping("/queryByUserId")
    @ApiOperation("/根據工號查詢產品処、機能和課別")
    public FebsResponse queryByUserId(@RequestParam(value = "SJ_UserID",required = false)String userID){
        EquipmentInspectionProjectDto dto;
        try {
            dto = equipmentInspectionMaintenanceService.queryByUserId(userID);
            //數據庫中的SBU均為IDS,根據課別選擇ids
            if(dto!=null){
                String pl = discern2Pl.get(dto.getDiscern());
                dto.setPl(pl);
            }
        }
        catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數userID："+userID);
        }
        return  new FebsResponse().code("200").data(dto);
    }

    //根據產品処、機能和課別查詢系列
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU",value="產品処,IDS1|IDS2|IDS3",example = "IDS1"),
            @ApiImplicitParam(name = "functions",value="機能,裝配|成型|電鍍|衝壓",example = "裝配"),
            @ApiImplicitParam(name = "discern",value="課別,一課|二課|三課",example = "一課")
    })
    @GetMapping("/queryProduction")
    @ApiOperation("/獲取系列")
    public FebsResponse queryProduction(@RequestParam(value = "SBU",required = false)String SBU,
                                        @RequestParam(value = "functions",required = false)String functions,
                                        @RequestParam(value = "discern",required = false)String discern){
        List<String> list ;
        try {
            //數據庫中的SBU均為IDS，暫時先將其固定爲IDS
            list = equipmentInspectionMaintenanceService.queryProduction("IDS", functions, discern);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數SBU："+SBU+"和functions："+functions+"和discern："+discern);
        }
        return new FebsResponse().code("200").data(list);
    }

    //根據產品処、機能、課別和系列查詢線體
    @ApiImplicitParams({
            @ApiImplicitParam(name = "SBU",value="產品処,IDS1|IDS2|IDS3",example = "IDS1"),
            @ApiImplicitParam(name = "functions",value="機能,裝配|成型|電鍍|衝壓",example = "裝配"),
            @ApiImplicitParam(name = "discern",value="課別,一課|二課|三課",example = "一課"),
            @ApiImplicitParam(name = "production",value="系列,1700SKT|4186SKT|4677SKT",example = "1700SKT")
    })
    @GetMapping("/queryProductionLine")
    @ApiOperation("獲取線體")
    public FebsResponse queryProductionLine(@RequestParam(value = "SBU",required = false)String SBU,
                                            @RequestParam(value = "functions",required = false)String functions,
                                            @RequestParam(value = "discern",required = false)String discern,
                                            @RequestParam(value = "production",required = false)String production){
        List<String> list ;
        try {
            //數據庫中的SBU均為IDS，暫時先將其固定爲IDS
            list = equipmentInspectionMaintenanceService.queryProductionLine("IDS", functions, discern,production);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+discern+production);
        }
        return new FebsResponse().code("200").data(list);
    }

    //查詢點檢類型
    @ApiOperation(value = "獲取點檢類型")
    @GetMapping("/querySelfInspectionType")
    public  FebsResponse querySelfInspectionType(){
        List<String> list = equipmentInspectionMaintenanceService.querySelfInspectionType();
        return new FebsResponse().code("200").data(list);
    }



    //根據SBU、机能、課別、系列、綫體、點檢類型、日期查詢點檢主要信息
    @ApiOperation(value = "根據SBU、机能、課別、系列、綫體、點檢類型、日期查詢點檢主要信息",httpMethod = "POST")
    @PostMapping("/queryEquipmentMainInfo")
    public FebsResponse queryEquipmentMainInfo(@RequestBody EquipmentInspectionQueryParam equipmentInspectionQueryParam){
        List<EquipmentInspectionMainInfoDto> list= new ArrayList<>();
        try {
            //數據庫中的SBU均為IDS，數據暫時默認爲IDS
//            String sbu = equipmentInspectionQueryParam.getSbu();
//            if(sbu!=null){
//                sbu="IDS";
//                equipmentInspectionQueryParam.setSbu(sbu);
//            }

            //檢查查詢的綫體狀態是否為不點檢
            if(isNotDJ(equipmentInspectionQueryParam)){
                String msg=equipmentInspectionQueryParam.getProductionLine()+"的"+equipmentInspectionQueryParam.getDate();//XX綫體的日期
                msg=msg+inspectionTypeDictionary.get(equipmentInspectionQueryParam.getSelfInspectionType())+"已设置为不点检";
                return new FebsResponse().code("200").message(msg).data(null);
            }else {
                List<String> discerns= new ArrayList<String>();
                String discern = equipmentInspectionQueryParam.getDiscern();
                if(StringUtils.isEmpty(discern)){
                    //課別為null，查詢sbu對應的Discern
                    discerns=equipmentInspectionMaintenanceService.queryDiscernBySBU(equipmentInspectionQueryParam.getSbu());

                }else {
                    //課別不為null，添加Discern查詢條件
                    discerns.add(discern);
                }

                if(discerns==null||discerns.size()==0){
                    if(!StringUtils.isEmpty(equipmentInspectionQueryParam.getSbu()))return new FebsResponse().code("200").message("當前SBU："+equipmentInspectionQueryParam.getSbu()+"無課別");
                    //sbu為null，全部查詢
                    list=equipmentInspectionMaintenanceService.queryEquipmentInspectionMainInfo(equipmentInspectionQueryParam);
                }

                for (String temp : discerns) {
                    //設置Discern查詢條件
                    equipmentInspectionQueryParam.setDiscern(temp);
                    list.addAll(equipmentInspectionMaintenanceService.queryEquipmentInspectionMainInfo(equipmentInspectionQueryParam));
                }

                //返回狀態值對應的狀態信息
                for (EquipmentInspectionMainInfoDto equipmentInspectionMainInfoDto : list) {
                    equipmentInspectionMainInfoDto.setStatus(getInspectionStatusString(equipmentInspectionMainInfoDto));
                }
            }
        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+equipmentInspectionQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    //查詢某條綫體是否設置為不點檢
    private boolean isNotDJ(EquipmentInspectionQueryParam equipmentInspectionQueryParam) {
        //如果綫體查詢條件爲空則説明使用了無條件全部查詢，直接放過
        String productionLine = equipmentInspectionQueryParam.getProductionLine();
        if(StringUtils.isEmpty(productionLine)){
            return false;
        }

        EquipmentInspectionMainInfoDto dto=equipmentInspectionMaintenanceService.queryIsNotDJ(equipmentInspectionQueryParam);
        if(dto==null){
            //該綫體不在不點檢表中
            return false;
        }else{
            //該綫體在不點檢表中
            return true;
        }
    }

    //根據日期、點檢類型和設備ID查詢點檢項目信息
    @ApiOperation(value = "根據日期、點檢類型和設備ID查詢查詢點檢項目信息",httpMethod = "POST")
    @PostMapping("/queryEquipmentData")
    public FebsResponse queryEquipmentData(@RequestBody EquipmentInspectionQueryParam equipmentInspectionQueryParam){
        List<EquipmentInspectionProjectDto> list;
        try {
            String equipmentID = equipmentInspectionQueryParam.getEquipmentID();
            String selfInspectionType = equipmentInspectionQueryParam.getSelfInspectionType();
            //根據設備ID和點檢類型獲取設備的點檢項目列表
            list=equipmentInspectionMaintenanceService.queryEquipmentInspectionProjectsByEquipmentIDByEquipmentIDAndType(equipmentID,selfInspectionType);

            //根據日期、點檢類型和設備ID獲取獲取equipmentInspectionProjectData，其保存著點檢項目的點檢值和結果
            String date = equipmentInspectionQueryParam.getDate();
            EquipmentInspectionProjectData equipmentInspectionProjectData = equipmentInspectionMaintenanceService
                    .queryEquipmentInspectionProjectData(date, selfInspectionType, equipmentID);
            if(equipmentInspectionProjectData==null){
                return new FebsResponse().code("200").data(list);
            }

            //解析equipmentInspectionProjectData的點檢值和結果，裏面為json數組數據
            String djValueList = equipmentInspectionProjectData.getDjValueList();
            String djResultList = equipmentInspectionProjectData.getDjResultList();
            JSONArray djValueJsonArray = JSONArray.parseArray(djValueList);
            JSONArray djResultJsonArray = JSONArray.parseArray(djResultList);
            // 從json數組中提取出json數據
            JSONObject djValueJSONObject = new JSONObject();
            JSONObject djResultJSONObject = new JSONObject();
            if(djValueJsonArray!=null){
                djValueJSONObject = djValueJsonArray.getJSONObject(0);
            }
            if(djResultJsonArray!=null){
                djResultJSONObject = djResultJsonArray.getJSONObject(0);
            }

            //將數據賦值給list中的item
            for (EquipmentInspectionProjectDto item : list) {
                Integer projectId = item.getProjectId();
                item.setDjValue(djValueJSONObject.getString(String.valueOf(projectId)));
                item.setDjResult(djResultJSONObject.getString(String.valueOf(projectId)));
//                item.setDjNotes(equipmentInspectionProjectData.getDjNotes());
            }
        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+equipmentInspectionQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    //設備點檢數據信息導出
    @ApiOperation("設備點檢數據信息導出")
    @PostMapping("/getEquipmentMainInfoDataFile")
    public void getEquipmentDataExampleFile(@RequestBody EquipmentInspectionQueryParam equipmentInspectionQueryParam, HttpServletResponse response){
        try{
            //String discern="";
            //把最新的数据写进Excel里,然后再下载
//            EquipmentQueryParam equipmentInspectionDto  = new EquipmentQueryParam();
//            List<EquipmentInspectionDto> list =equipmentSelfInspectionService.selectExampleData();

            //數據庫中的SBU均為IDS，數據暫時默認爲IDS
            String sbu = equipmentInspectionQueryParam.getSbu();
            if(sbu!=null){
                sbu="IDS";
                equipmentInspectionQueryParam.setSbu(sbu);
            }
            List<EquipmentInspectionMainInfoDto> list = equipmentInspectionMaintenanceService.queryEquipmentInspectionMainInfo(equipmentInspectionQueryParam);
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            XSSFSheet sheet = xssfWorkbook.createSheet("sheet1");
            //設置表頭
            XSSFRow headerRow  = sheet.createRow(0);
            headerRow .createCell(0).setCellValue("SBU");
            headerRow .createCell(1).setCellValue("機能");
            headerRow .createCell(2).setCellValue("課別");
            headerRow .createCell(3).setCellValue("系列");
            headerRow .createCell(4).setCellValue("線體");
//            headerRow .createCell(5).setCellValue("設備ID");
            headerRow .createCell(5).setCellValue("設備名稱");
            headerRow .createCell(6).setCellValue("點檢類型");
            headerRow .createCell(7).setCellValue("日期");
            headerRow .createCell(8).setCellValue("開始時間");
            headerRow .createCell(9).setCellValue("結束時間");
            headerRow .createCell(10).setCellValue("時長");
            headerRow .createCell(11).setCellValue("點檢狀態");
            headerRow .createCell(12).setCellValue("備注");
            int rowNum=1;
            for (EquipmentInspectionMainInfoDto param :list){
                XSSFRow row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(param.getPl());
                row.createCell(1).setCellValue(param.getEnginery());
                row.createCell(2).setCellValue(param.getDiscern());
                row.createCell(3).setCellValue(param.getProduction());
                row.createCell(4).setCellValue(param.getProductionLine());
//                row.createCell(5).setCellValue(param.getEquipmentId());
                row.createCell(5).setCellValue(param.getEquipmentName());
                row.createCell(6).setCellValue(inspectionTypeDictionary.getOrDefault(param.getInspectionType(),""));
                row.createCell(7).setCellValue(param.getDate());
                row.createCell(8).setCellValue(param.getStartDate());
                row.createCell(9).setCellValue(param.getEndDate());
                row.createCell(10).setCellValue(param.getDuration());
                row.createCell(11).setCellValue(getInspectionStatusString(param));
                row.createCell(12).setCellValue(param.getNotes());

            }
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode("20225sadas.xlsx", "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            /*  response.addHeader("content-Length", "");*/
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/octet-stream");
            response.setHeader("Custom-Status", "200");
            try(OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())){
                xssfWorkbook.write(outputStream);
                outputStream.flush();
            }
            xssfWorkbook.close();
        }catch (Exception e){
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

    }

    /**
     * 返回點檢狀態值相應的信息字符串
     * @param dto 點擊主要數據信息
     * @return 點檢狀態值相應的信息字符串
     */
    private String getInspectionStatusString(EquipmentInspectionMainInfoDto dto) {
        String status = dto.getStatus();
        if("0".equals(status)){
            return "已刪除";
        }else if("1".equals(status)){
            return "["+dto.getDjrName()+"]"+"點檢中";
        }else if("2".equals(status)){
            return "待["+dto.getQdrName()+"]"+"確認";
        }else if("3".equals(status)){
            return "點檢完成";
        }else if("4".equals(status)){
            return "駁回中";
        }else if("5".equals(status)){
            return "不點檢";
        }
        return null;
    }

}
