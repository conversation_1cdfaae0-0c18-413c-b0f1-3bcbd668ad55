package cc.mrbird.febs.backwork.service.impl;

import cc.mrbird.febs.backwork.dao.TDataBackworkMapper;
import cc.mrbird.febs.backwork.entity.*;
import cc.mrbird.febs.backwork.service.CheckService;
import cc.mrbird.febs.backwork.service.Email;
import cc.mrbird.febs.backwork.service.TDataBackworkService;
import cc.mrbird.febs.backwork.util.ArtMessage;
import cc.mrbird.febs.backwork.util.HtmlCode;
import cc.mrbird.febs.common.annotation.NotBlank;
import cc.mrbird.febs.common.domain.FebsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@Service
public class CheckServiceImpl implements CheckService {
    @Autowired
    private TDataBackworkMapper tDataBackworkMapper;
    @Autowired
    private Email emailMapper;
    @Autowired
    private TDataBackworkService tDataBackworkService;
    ArtMessage artMessage =new ArtMessage();
    HtmlCode htmlCode= new HtmlCode();
    final    String tips ="请使用谷歌浏览器打开：";
    @Override
    public FebsResponse CheckByPl1(CheckDto checkDto) {
        String workId= tDataBackworkMapper.selectWorkIdByQh1( checkDto.getName());
        checkDto.setShowId(workId);
        try{
            if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("1") &&tDataBackworkMapper.countUnit(checkDto.getUuid())==0) {
                tDataBackworkMapper.QC1(checkDto);   //課長簽核------>stNo:2   qhStatus:1
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String urlName=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)--->"+checkDto.getName()+"(等待您簽核)"+"<br>";
                String body=URLEncoder.encode(urlName,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("2")&&tDataBackworkMapper.countUnit(checkDto.getUuid())==0) {
                if (checkDto.getUnit().equals("IDS-BK-PL1-電鍍一課") || checkDto.getUnit().equals("IDS-BK-PL1-電鍍二課")){
                    checkDto.setWorkId(1);
                    tDataBackworkMapper.QC2(checkDto);
                }else if(checkDto.getUnit().equals("IDS-BK-PL1-裝配一課") || checkDto.getUnit().equals("IDS-BK-PL1-裝配二課")) {
                    BackWorkSameUnit backWorkSameUnit = new BackWorkSameUnit();
                    backWorkSameUnit.setBackWorkUnit(checkDto.getUnit());
                    backWorkSameUnit.setQh1(selectQHByQh1(checkDto.getUuid()));
                    backWorkSameUnit.setQh3(backWorkSameUnit.getQh1());
                    backWorkSameUnit.setQh5(tDataBackworkMapper.selectQHByZRQC(checkDto.getUuid()));
                    backWorkSameUnit.setUuid(checkDto.getUuid());
                    tDataBackworkMapper.QHByPL1SameUnit(backWorkSameUnit);
                    String email=  tDataBackworkMapper.findEmail(backWorkSameUnit.getQh1());
                    email=email+","+tDataBackworkMapper.findEmail(selectLineLeaderByUUID(checkDto.getUuid()));
                    String subject="<<<表單无纸化专案----->重工记录表结案通知";
                    String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh5(checkDto.getUuid())+"已做最終簽核確認"+"該筆記錄已結案"+"<br>";
                    body=URLEncoder.encode(body,"UTF-8");
                    body=body+artMessage.getHtmlCode();
                    String cc="<EMAIL>";
                    cc=URLEncoder.encode(cc,"UTF-8");
                    subject=URLEncoder.encode(subject,"UTF-8");
                    emailMapper.selectEmail(email,subject,body,cc);
                    System.out.println("############此处运行了#########");
                }else {
                    tDataBackworkMapper.QC2(checkDto); //品保第一次簽核並確定重工單位   -----> stNo:3   qhStatus:2
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"等待您簽核"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
                }
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("3")&&tDataBackworkMapper.countUnit(checkDto.getUuid())==0) {
                tDataBackworkMapper.QCNotSP(checkDto);  //重工單位簽核-------->stNo:4             qhStatus:3
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"請您做最終確認"+"--->"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("4")&&tDataBackworkMapper.countUnit(checkDto.getUuid())==0) {
                boolean b= tDataBackworkMapper.QC5(checkDto);  //品保最終簽核確認 --------------->stNo:6      qhStatus:5
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表结案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh5(checkDto.getUuid())+"已做最終簽核確認"+"該筆記錄已結案"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh5(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            }else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("3")&&tDataBackworkMapper.countUnit(checkDto.getUuid())==1) {
                boolean b= tDataBackworkMapper.QC3(checkDto);  //电镀内部审核----------------- >stNo:4         qhStatus:3
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"請您簽核"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("4")&&tDataBackworkMapper.countUnit(checkDto.getUuid())==1) {
                boolean b=tDataBackworkMapper.QC4(checkDto);  //电镀内部审核 ----------------->stNo:5              qhStatus:4
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());//tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已签核)"+"--->"+checkDto.getName()+"請您做最終確認"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("5")&&tDataBackworkMapper.countUnit(checkDto.getUuid())==1) {
                boolean b=tDataBackworkMapper.QC5(checkDto);  //品保最終簽核------------->stNo:6                  qhStatus:5
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());//tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表结案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh5(checkDto.getUuid())+"已做最終簽核確認"+"該筆記錄已結案"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh5(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            }
            else {
                return new FebsResponse().code("401").message("失败");
            }
        }catch (Exception e){
            String message = String.valueOf(e);
        }
        return new FebsResponse().code("200").message("推送成功"+"传的参数值为:"+checkDto);
    }
    /*
    * 1 品保
    * 2 QE
    * 3 裝配課長
    * 4 品保課長(最終確認)
    * 5 裝配部長
    * 6 品保部長(最終確認)
    * 重工类型:1是客诉类0是其他
    * */
    @Override
    public FebsResponse CheckByPl2(CheckDto checkDto) {
        String workId= tDataBackworkMapper.selectWorkIdByQh1( checkDto.getName());
        checkDto.setShowId(workId);
        try{
            //责任品保填写抽检记录------>stNo:2   qhStatus:1
            if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("1")) {
                Integer b=  tDataBackworkMapper.QH1ByPL2AndPL3(checkDto);
                String    email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String    subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String    urlName=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)--->"+checkDto.getName()+"(等待您簽核)"+"<br>";
                String    body=URLEncoder.encode(urlName,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            //QE簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("2")) {
                tDataBackworkMapper.QH2ByPL2AndPL3(checkDto);
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"等待您簽核"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            //裝配課長簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("3")) {
                tDataBackworkMapper.QH3ByPL2AndPL3(checkDto);  //重工單位簽核-------->stNo:4             qhStatus:3
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());//tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"等待您簽核"+"--->"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            //其他原因重工----->品保課長簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("4")&&checkDto.getIsOther().equals("0")) {
                Integer b= tDataBackworkMapper.QH4ByPL2AndPL3(checkDto);  //品保最終簽核確認 --------------->stNo:6      qhStatus:5
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表结案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"已做最終簽核確認"+"該筆記錄已結案"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
                //客诉原因重工
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("4")&&checkDto.getIsOther().equals("1")) {
                Integer b= tDataBackworkMapper.QH4ByPL2AndPL3ToKs(checkDto);  //品保最終簽核確認 --------------->stNo:6      qhStatus:5
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表结案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已签核)"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            }
            //裝配部長簽核
           else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("5")&&checkDto.getIsOther().equals("1")) {
                Integer b= tDataBackworkMapper.QH5ByPL2AndPL3(checkDto);
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh5(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"請您做最終審核"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            //品保部長簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("6")&&checkDto.getIsOther().equals("1")) {
                Integer b=tDataBackworkMapper.QH6ByPL2AndPL3(checkDto);
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                //String email="<EMAIL>";
                String subject="<<<表單无纸化专案----->重工记录表記錄結案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已签核)"+"--->"+tDataBackworkMapper.findQh5(checkDto.getUuid())+"(已签核)"+"--->"+checkDto.getName()+"已做最終審核，該筆記錄已結案"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                //String cc="<EMAIL>";
                String cc="<EMAIL>,<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh5(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh6(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                //String cc="<EMAIL>,<EMAIL>";
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            }
            else {
                return new FebsResponse().code("401").message("失败");
            }
        }catch (Exception e){
            String message = String.valueOf(e);
            System.out.println(message);
        }
        return new FebsResponse().code("200").message("推送成功");
    }

    @Override
    public FebsResponse CheckByPl3(CheckDto checkDto) {
        String workId= tDataBackworkMapper.selectWorkIdByQh1( checkDto.getName());
        checkDto.setShowId(workId);
        try{
            //责任品保填写抽检记录------>stNo:2   qhStatus:1
            if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("1")) {
                Integer b= tDataBackworkMapper.QH1ByPL2AndPL3(checkDto);
                String    email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String    subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String    urlName=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)--->"+checkDto.getName()+"(等待您簽核)"+"<br>";
                String    body=URLEncoder.encode(urlName,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
                //QE簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("2")) {
                tDataBackworkMapper.QH2ByPL2AndPL3(checkDto);
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"等待您簽核"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
                //裝配課長簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("3")) {
                tDataBackworkMapper.QH3ByPL2AndPL3(checkDto);  //重工單位簽核-------->stNo:4             qhStatus:3
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());//tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"等待您簽核"+"--->"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
                //其他原因重工----->品保課長簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("4")&&checkDto.getIsOther().equals("0")) {
                Integer b= tDataBackworkMapper.QH4ByPL2AndPL3(checkDto);  //品保最終簽核確認 --------------->stNo:6      qhStatus:5
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表结案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已簽核)"+checkDto.getName()+"已做最終簽核確認"+"該筆記錄已結案"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
                //客诉原因重工
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("4")&&checkDto.getIsOther().equals("1")) {
                Integer b= tDataBackworkMapper.QH4ByPL2AndPL3ToKs(checkDto);  //品保最終簽核確認 --------------->stNo:6      qhStatus:5
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表结案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已签核)"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            }
            //裝配部長簽核
            else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("5")&&checkDto.getIsOther().equals("1")) {
                Integer b= tDataBackworkMapper.QH5ByPL2AndPL3(checkDto);
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                String subject="<<<表單无纸化专案----->重工记录表需要您签核";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh5(checkDto.getUuid())+"(已簽核)"+"--->"+checkDto.getName()+"請您做最終審核"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                String cc="<EMAIL>,<EMAIL>";
                cc=URLEncoder.encode(cc,"UTF-8");
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
                //品保部長簽核
            } else if (tDataBackworkMapper.findStNo(checkDto.getUuid()).equals("6")&&checkDto.getIsOther().equals("1")) {
                Integer b=tDataBackworkMapper.QH6ByPL2AndPL3(checkDto);
                String email=  tDataBackworkMapper.findEmail(checkDto.getName());
                //String email="<EMAIL>";
                String subject="<<<表單无纸化专案----->重工记录表記錄結案通知";
                String body=tips+"http://************:220"+"<br><br>当前流程为："+tDataBackworkMapper.findLeaderName(checkDto.getUuid())+"(承辦)"+"--->"+tDataBackworkMapper.findQh1(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh2(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh3(checkDto.getUuid())+"(已簽核)"+"--->"+tDataBackworkMapper.findQh4(checkDto.getUuid())+"(已签核)"+"--->"+tDataBackworkMapper.findQh5(checkDto.getUuid())+"(已签核)"+"--->"+checkDto.getName()+"已做最終審核，該筆記錄已結案"+"<br>";
                body=URLEncoder.encode(body,"UTF-8");
                body=body+artMessage.getHtmlCode();
                //String cc="<EMAIL>";
                String cc="<EMAIL>,<EMAIL>";
                cc=cc+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findLeaderName(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh1(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh2(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh3(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh4(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh5(checkDto.getUuid()))+","+tDataBackworkMapper.findEmail(tDataBackworkMapper.findQh6(checkDto.getUuid()));
                cc=URLEncoder.encode(cc,"UTF-8");
                //String cc="<EMAIL>,<EMAIL>";
                subject=URLEncoder.encode(subject,"UTF-8");
                emailMapper.selectEmail(email,subject,body,cc);
            }
            else {
                return new FebsResponse().code("401").message("失败");
            }
        }catch (Exception e){
            String message = String.valueOf(e);
            System.out.println(message);
        }
        return new FebsResponse().code("200").message("推送成功");
    }

    @Override
    public boolean insertNotNull(TDataBackwork tDataBackwork) {
        boolean b = true; // 默认设置为true
        String isOther = tDataBackwork.getIsOther();
        String qh1 = tDataBackwork.getQh1();
        String qh2 = tDataBackwork.getQh2();
        String qh3 = tDataBackwork.getQh3();
        String qh4 = tDataBackwork.getQh4();
        String qh5 = tDataBackwork.getQh5();
        String qh6 = tDataBackwork.getQh6();
        System.out.println(tDataBackwork);
        // 判断参数是否为空，根据前置条件进行判断
        if (isOther == null && isOther.equals("") ||
                qh1 == null && qh1.equals("")
                || qh2 == null && qh2.equals("")
                || qh3 == null && qh3.equals("")
                || qh4 == null && qh4.equals("")
                || qh5 == null && qh5.equals("")) {
            b = false;
        } else if (isOther.equals("0") ) {
            b = true;
        } else if (isOther.equals("1") && qh6 != null && qh6 !="") {
            b = true;
        } else {
            b = false;
        }
        return b;
    }

    @Override
    public FebsResponse goBackCheckByPl1(GoBackVo goBackVo) throws IOException {
        //查找承辦人郵箱，有人退簽時發送郵件
        String name=    tDataBackworkMapper.findLeaderName(goBackVo.getUuid());
        String email=  tDataBackworkMapper.findEmail(name);
        String why= goBackVo.getGoBackWhy();
        String partNumber=selectPartNumber(goBackVo.getUuid());
        String body=htmlCode.getBackHtmlCode(name,why,partNumber);
        String subject ="重工記錄表退签提示》》》";
        subject=URLEncoder.encode(subject,"UTF-8");
        String showId=tDataBackworkMapper.selectWorkIdByName(name);
        GoBackCheck goBackCheck =new GoBackCheck();
        goBackCheck.setUuid(goBackVo.getUuid());
        goBackCheck.setStNo(1);
        goBackCheck.setWhy(goBackVo.getGoBackWhy());
        goBackCheck.setShowId(showId);
        boolean b=  tDataBackworkMapper.updateByGoBackCheck(goBackCheck);
        if (b){
            emailMapper.selectEmail(email,subject,body,"<EMAIL>");
            return new FebsResponse().code("200").message("退簽成功");
        }else {
            return new FebsResponse().code("500").message("退簽失敗，請聯繫開發人員");
        }

    }

    @Override
    public FebsResponse goBackCheckByPl2(GoBackVo goBackVo) throws IOException {
        //查找承辦人郵箱，有人退簽時發送郵件
        String name=    tDataBackworkMapper.findLeaderName(goBackVo.getUuid());
        String email=  tDataBackworkMapper.findEmail(name);
        String why= goBackVo.getGoBackWhy();
        String body="退簽原因為:"+why;
        body =URLEncoder.encode(body,"UTF-8");
        String subject ="重工記錄表退單提示》》》";
        subject=URLEncoder.encode(subject,"UTF-8");
        String showId=tDataBackworkMapper.selectWorkIdByName(name);
        GoBackCheck goBackCheck =new GoBackCheck();
        goBackCheck.setUuid(goBackVo.getUuid());
        goBackCheck.setStNo(1);
        goBackCheck.setWhy(goBackVo.getGoBackWhy());
        goBackCheck.setShowId(showId);
        boolean b=  tDataBackworkMapper.updateByGoBackCheck(goBackCheck);
        if (b){
            emailMapper.selectEmail(email,subject,body,"<EMAIL>");
            return new FebsResponse().code("200").message("退簽成功");
        }else {
            return new FebsResponse().code("500").message("退簽失敗，請聯繫開發人員");
        }
    }

    @Override
    public FebsResponse goBackCheckByPl3(GoBackVo goBackVo) throws IOException {
        //查找承辦人郵箱，有人退簽時發送郵件
        String name=    tDataBackworkMapper.findLeaderName(goBackVo.getUuid());
        String email=  tDataBackworkMapper.findEmail(name);
        String why= goBackVo.getGoBackWhy();
        String body="退簽原因為:"+why;
        body =URLEncoder.encode(body,"UTF-8");
        String subject ="重工記錄表退單提示》》》";
        subject=URLEncoder.encode(subject,"UTF-8");
        String showId=tDataBackworkMapper.selectWorkIdByName(name);
        GoBackCheck goBackCheck =new GoBackCheck();
        goBackCheck.setUuid(goBackVo.getUuid());
        goBackCheck.setStNo(1);
        goBackCheck.setWhy(goBackVo.getGoBackWhy());
        goBackCheck.setShowId(showId);
        boolean b=  tDataBackworkMapper.updateByGoBackCheck(goBackCheck);
        if (b){
            emailMapper.selectEmail(email,subject,body,"<EMAIL>");
            return new FebsResponse().code("200").message("退簽成功");
        }else {
            return new FebsResponse().code("500").message("退簽失敗，請聯繫開發人員");
        }
    }
    //当PL1的责任单位为装配自己本身时，自动签核
    private String pl1AICheck(PLOneAICheck plOneAICheck){
        //确定重工单位为装配自身，则 QH3 为课长  QH5为品保

        //修改对应的qhStatus  stNo







        return "OK";
    }

    private String selectQHByQh1(String uuid){
        return tDataBackworkMapper.selectQHByQh1(uuid);
    }

    private String selectQHByQh3(String uuid){
        return tDataBackworkMapper.selectQHByQh3(uuid);
    }

    private String selectLineLeaderByUUID(String uuid){
        return tDataBackworkMapper.selectLineLeaderByUUID(uuid);
    }

    private String selectQHByZRQC(String uuid){
        return tDataBackworkMapper.selectQHByZRQC(uuid);
    }

    private String selectPartNumber(String uuid){
        return tDataBackworkMapper.selectPartnumber(uuid);
    }
    //

}
