<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.agv.dao.AGVMapper">
    <select id="queryAgv" resultType="cc.mrbird.febs.agv.entity.dto.AgvDto">
        SELECT
        [id]              as id,
        [factory]          as factory,
        [building]            as building,
        [functions]      as functions           ,
        [RobotId]            as robotId,
        [RobotIP]            as robotIP,
        [RobotName]           as robotName ,
        [RobotPicUrl]         as robotPicUrl ,
        [WorkStatus] as workStatus,
        [ChargeState]              as chargeState,
        [OnlineStatus]          as onlineStatus,
        [JackState]            as jackState,
        [ErrorCode]      as errorCode           ,
        [ErrorMsg]            as errorMsg,
        [EnergyLevel]            as energyLevel,
        [CurrentStation]           as currentStation ,
        [CurrentTaskId]         as currentTaskId ,
        [posX] as posX ,
        [posY]              as posY,
        [angle]          as angle,
        [path]            as path,
        [currentLock]      as currentLock           ,
        [unfinishedPath]            as unfinishedPath,
        [detectStepList]            as detectStepList,
        [UPTime]           as uPTime
        FROM [APPGCL_Process].[dbo].[AGV_RobotInfo]
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="factory != null and factory != ''">
                AND factory = #{factory}
            </if>
            <if test="building != null and building != ''">
                AND building = #{building}
            </if>
            <if test="functions != null and functions != ''">
                AND functions = #{functions}
            </if>
            <if test="robotId != null and robotId != ''">
                AND RobotId=#{robotId}
            </if>
            <if test="robotName != null and robotName != ''">
                AND RobotName=#{robotName}
            </if>
        </where>
    </select>
</mapper>