package cc.mrbird.febs.newo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("T_Equipment_Maintenance")
public class WXBY {
    @TableId(value ="Id" ,type = IdType.AUTO)
    @TableField(value = "Id")
    private Integer Id;
    @TableField(value = "Production")
    private String Production;
    @TableField(value = "ProductionLine")
    private String ProductionLine;
    @TableField(value = "RecordDate")
    private String RecordDate;
    @TableField(value = "ClassInfo")
    private String ClassInfo;
    @TableField(value = "EquipmentName")
    private String EquipmentName;
    @TableField(value = "EquipmentId")
    private String EquipmentId;
    @TableField(value = "SCLH")
    private String SCLH;
    @TableField(value = "StartTime")
    private String StartTime;
    @TableField(value = "EndTime")
    private String EndTime;
    @TableField(value = "LossTime")
    private String LossTime;
    @TableField(value = "MaintenanceNature")
    private String MaintenanceNature;
    @TableField(value = "ChangeStation")
    private String ChangeStation;
    @TableField(value = "FaultType")
    private String FaultType;
    @TableField(value = "FaultCause")
    private String FaultCause;
    @TableField(value = "CounterPlan")
    private String CounterPlan;
    @TableField(value = "STATUS")
    private String STATUS;
    @TableField(value = "QH1")
    private String QH1;
    @TableField(value = "QH2")
    private String QH2;
    @TableField(value = "QH3")
    private String QH3;
    @TableField(value = "QH4")
    private String QH4;
}
