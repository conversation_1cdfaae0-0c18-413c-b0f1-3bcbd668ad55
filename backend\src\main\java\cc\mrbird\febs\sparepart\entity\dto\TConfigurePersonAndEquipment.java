package cc.mrbird.febs.sparepart.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TConfigurePersonAndEquipment {
    private Integer id;
    @ApiModelProperty
    private String pl;
    private String enginery;
    private String discern;
    private String sparePartName;
    private String sparePartCode;
    private String personInChargeNameBy01;
    private String personInChargeEmailBy01;
    private String personInChargeWorkIdBy01;
    private String personInChargeNameBy02;
    private String personInChargeEmailBy02;
    private String personInChargeWorkIdBy02;
    private String personInChargeNameByMax;
    private String personInChargeEmailByMax;
    private String personInChargeWorkIdByMax;
    private String isDelete;
    private String lifeLimit;
    public TConfigurePersonAndEquipment(String pl, String enginery, String discern, String sparePartName, String sparePartCode, String personInChargeNameBy01, String personInChargeEmailBy01, String personInChargeWorkIdBy01, String personInChargeNameBy02, String personInChargeEmailBy02, String personInChargeWorkIdBy02, String personInChargeNameByMax, String personInChargeEmailByMax, String personInChargeWorkIdByMax) {
        this.pl = pl;
        this.enginery = enginery;
        this.discern = discern;
        this.sparePartName = sparePartName;
        this.sparePartCode = sparePartCode;
        this.personInChargeNameBy01 = personInChargeNameBy01;
        this.personInChargeEmailBy01 = personInChargeEmailBy01;
        this.personInChargeWorkIdBy01 = personInChargeWorkIdBy01;
        this.personInChargeNameBy02 = personInChargeNameBy02;
        this.personInChargeEmailBy02 = personInChargeEmailBy02;
        this.personInChargeWorkIdBy02 = personInChargeWorkIdBy02;
        this.personInChargeNameByMax = personInChargeNameByMax;
        this.personInChargeEmailByMax = personInChargeEmailByMax;
        this.personInChargeWorkIdByMax = personInChargeWorkIdByMax;
    }
}

