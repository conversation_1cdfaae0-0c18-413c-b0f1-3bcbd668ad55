package cc.mrbird.febs.productionday.controller;
import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.common.exception.FebsException;
import cc.mrbird.febs.line.entity.ConfigureConnectDto;
import cc.mrbird.febs.opeationalLogs.entity.TOpeationalLogsProduction;
import cc.mrbird.febs.opeationalLogs.service.ITOpeationalLogsService;
import cc.mrbird.febs.productionday.dao.TConfigureProductiondayMapper;
import cc.mrbird.febs.productionday.entity.*;
import cc.mrbird.febs.productionday.service.ITConfigureProductiondayService;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@ResponseBody
@RequestMapping("/productionday/t-configure-productionday")
@RequiresPermissions("zyc")
public class TConfigureProductiondayController {
     private String message;
     @Autowired
     private TConfigureProductiondayMapper tConfigureProductiondayMapper;
     @Autowired
     private ITOpeationalLogsService itOpeationalLogsService;
     @Autowired
     private ITConfigureProductiondayService itConfigureProductiondayService;
    @ApiOperation(value = "保存产量",notes = "保存产量")
    @ApiResponses({
            @ApiResponse(code = 200,message = "ok",response = ConfigureConnectDto.class)
    })
     @PostMapping("/insert")
     public FebsResponse insert(@RequestBody TConfigureProductiondayDto tConfigureProductionday) throws FebsException {
        try{
             String discern=tConfigureProductionday.getTConfigureProductionday().getDiscern();
             String production=tConfigureProductionday.getTConfigureProductionday().getProduction();
             String line=tConfigureProductionday.getTConfigureProductionday().getProductionLine();
             String classInfo=tConfigureProductionday.getTConfigureProductionday().getClassInfo();
             int count=tConfigureProductiondayMapper.queryCount(discern,production,line,classInfo);

             TOpeationalLogsProduction tOpeationalLogsProduction =new TOpeationalLogsProduction(null, tConfigureProductionday.getName(),"保存表单",new Date());
             itOpeationalLogsService.save(tOpeationalLogsProduction);
             if(count>0){
                 tConfigureProductiondayMapper.updateTConfigureProductionDay(tConfigureProductionday.getTConfigureProductionday());
                 return new FebsResponse().message("修改产量成功");

             }else{
                 tConfigureProductiondayMapper.insertConfig(tConfigureProductionday.getTConfigureProductionday());
                 return new FebsResponse().message("新增产量成功");
             }
         }catch (Exception e){
             message = "新增产量失败";
             log.error(message, e);
             throw new FebsException(message);
         }
     }

    @PostMapping("/lock")
    public FebsResponse lock(@RequestBody TConfigureProductiondayDto tConfigureProductionday) throws FebsException {
        try{
      /*    Date date=new Date();
            Date beginDate= DateUtil.beginOfDay(date);
            String beginDate1=DateUtil.format(beginDate,"yyyy-MM-dd HH:mm:ss");
            Date endDate=DateUtil.endOfDay(date);
            String endDate1=DateUtil.format(endDate,"yyyy-MM-dd HH:mm:ss");

            Date dayBeginDate=DateUtil.offsetHour(beginDate,8);
            Date dayEndDate=DateUtil.offsetHour(beginDate,20);

            Date nightBeginDate=DateUtil.
            if(tConfigureProductionday.getTConfigureProductionday().getClassInfo().equals("白班")){

            }
            */
            //白班时间
            Date time=tConfigureProductionday.getTime();
            Date beginDate=DateUtil.beginOfDay(time);

            Date dayBeginDate=DateUtil.offsetHour(beginDate,8);
            Date dayEndDate=DateUtil.offsetHour(beginDate,20);
            String dayBeginDateFormat=DateUtil.format(dayBeginDate,"yyyy-MM-dd HH:mm:ss");
            String dayEndDateFormat=DateUtil.format(dayEndDate,"yyyy-MM-dd HH:mm:ss");
            //晚班时间在20:00到明天7:00
            String beginNightDateFormat;
            String endNightDateFormat;
            if(DateUtil.hour(time,true)<=7){
                Date nightDate=DateUtil.beginOfDay(time);
                Date  beginNightDate=DateUtil.offsetHour(nightDate,-4);
                Date endNightDate=DateUtil.offsetHour(nightDate,7);
                 beginNightDateFormat=DateUtil.format(beginNightDate,"yyyy-MM-dd HH:mm:ss");
                 endNightDateFormat=DateUtil.format(endNightDate,"yyyy-MM-dd HH:mm:ss");
            }else{
                Date nightDate=DateUtil.beginOfDay(time);
                Date  beginNightDate=DateUtil.offsetHour(nightDate,20);
                Date endNightDate=DateUtil.offsetHour(nightDate,31);
                 beginNightDateFormat=DateUtil.format(beginNightDate,"yyyy-MM-dd HH:mm:ss");
                 endNightDateFormat=DateUtil.format(endNightDate,"yyyy-MM-dd HH:mm:ss");
            }
            String discern=tConfigureProductionday.getTConfigureProductionday().getDiscern();
            String production=tConfigureProductionday.getTConfigureProductionday().getProduction();
            String line=tConfigureProductionday.getTConfigureProductionday().getProductionLine();
            String classInfo=tConfigureProductionday.getTConfigureProductionday().getClassInfo();

            if(classInfo.equals("白班")){
                int count=tConfigureProductiondayMapper.queryCountLock(discern,production,line,classInfo,dayBeginDateFormat,dayEndDateFormat);
                TOpeationalLogsProduction tOpeationalLogsProduction =new TOpeationalLogsProduction(null, tConfigureProductionday.getName(),"鎖定表单",new Date());
                itOpeationalLogsService.save(tOpeationalLogsProduction);
                if(count>0){
                    return new FebsResponse().status("401").message("已经锁定");
                }else{
                    tConfigureProductiondayMapper.lockConfig(tConfigureProductionday.getTConfigureProductionday());

                    TConfigureProductionday configureProductionday=tConfigureProductionday.getTConfigureProductionday();
                    configureProductionday.setTimeRate(configureProductionday.getTimeRate()+"%");
                    configureProductionday.setPerformanceRate(configureProductionday.getPerformanceRate()+"%");
                    configureProductionday.setYieldRate(configureProductionday.getYieldRate()+"%");
                    configureProductionday.setOee(configureProductionday.getOee()+"%");
                    configureProductionday.setOee1(configureProductionday.getOee1()+"%");
                    configureProductionday.setUph(configureProductionday.getUph());
                    configureProductionday.setUpph(configureProductionday.getUpph());
                    configureProductionday.setWorkOrder(configureProductionday.getWorkOrder()+","+configureProductionday.getScsl());
                    tConfigureProductiondayMapper.insertDBCL_OndutyYield(configureProductionday);
                    return new FebsResponse().status("200").message("鎖定成功");
                }
            }else{
                int count=tConfigureProductiondayMapper.queryCountLock(discern,production,line,classInfo,beginNightDateFormat,endNightDateFormat);
                TOpeationalLogsProduction tOpeationalLogsProduction =new TOpeationalLogsProduction(null, tConfigureProductionday.getName(),"鎖定表单",new Date());
                itOpeationalLogsService.save(tOpeationalLogsProduction);
                if(count>0){
                    return new FebsResponse().status("401").message("已经锁定");
                }else{
                    tConfigureProductiondayMapper.lockConfig(tConfigureProductionday.getTConfigureProductionday());

                    TConfigureProductionday configureProductionday=tConfigureProductionday.getTConfigureProductionday();
                    configureProductionday.setTimeRate(configureProductionday.getTimeRate()+"%");
                    configureProductionday.setPerformanceRate(configureProductionday.getPerformanceRate()+"%");
                    configureProductionday.setYieldRate(configureProductionday.getYieldRate()+"%");
                    configureProductionday.setOee(configureProductionday.getOee()+"%");
                    configureProductionday.setOee1(configureProductionday.getOee1()+"%");
                    configureProductionday.setUph(configureProductionday.getUph());
                    configureProductionday.setUpph(configureProductionday.getUpph());
                    configureProductionday.setWorkOrder(configureProductionday.getWorkOrder()+","+configureProductionday.getScsl());
                    tConfigureProductiondayMapper.insertDBCL_OndutyYield(configureProductionday);
                    return new FebsResponse().status("200").message("鎖定成功");
                }
            }

        }catch (Exception e){
            message = "新增产量失败";
            log.error(message, e);
            throw new FebsException(message);
        }
    }
     @PostMapping("/update")
     public FebsResponse update(@RequestBody TConfigureProductionday tConfigureProductionday){
           return null;
     }

    @ApiOperation(value = "查詢人員信息",notes = "查詢人員信息")
     @GetMapping("/queryLineInfo")
     public FebsResponse queryLineInfo(@RequestParam("cardId")String cardId) throws FebsException {
         try{
             List<ProductDayInfo> list=tConfigureProductiondayMapper.queryLinesInfo(cardId);
             return new FebsResponse().data(list);
         }catch (Exception e){
             message = "查詢人員信息失敗";
             log.error(message, e);
             throw new FebsException(message);
         }
     }

    @ApiOperation(value = "生產日報數據回顯",notes = "生產日報數據回顯")
    @PostMapping("/queryProductionDay")
    public FebsResponse queryProductionDay(@RequestBody ProductionDayVo productionDayVo){
        String discern=productionDayVo.getDiscern();
        String production=productionDayVo.getProduction();
        String line=productionDayVo.getLine();
        String classInfo=productionDayVo.getClassInfo();
           List list = tConfigureProductiondayMapper.queryProductionDay(discern,production,line,classInfo);
//           list.forEach(System.out::println);
        return new FebsResponse().data(list);
    }


     @ApiOperation(value ="查詢產能趨勢",notes = "查詢產能趨勢")
    @GetMapping("/queryCapacityTrend")
    public FebsResponse queryCapacityTrend(@RequestParam("discern")String discern,@RequestParam("dateTime")String date,@RequestParam("name")String name){
         TOpeationalLogsProduction tOpeationalLogsProduction =new TOpeationalLogsProduction(null, name,"查看生产趋势",new Date());
         itOpeationalLogsService.save(tOpeationalLogsProduction);
         CapacityTrend capacityTrend=itConfigureProductiondayService.queryCapacity(discern,date,name);
        return new FebsResponse().data(capacityTrend);
     }
     @ApiOperation(value = "报表填写相关信息",notes = "报表填写相关信息")
    @GetMapping("/queryCapacityTrendInfo")
    public FebsResponse queryCapacityTrendInfo(@RequestParam("line")String line,@RequestParam("cardId")String cardId,@RequestParam("discern")String discern,@RequestParam("production")String production,@RequestParam("classInfo")String classInfo,@RequestParam("time")String time){
         CapacityTrendInfo capacityTrendInfo=new CapacityTrendInfo();
         capacityTrendInfo.setGroupLeader(tConfigureProductiondayMapper.queryGroupLeader(discern,production));
         capacityTrendInfo.setRollLeader(tConfigureProductiondayMapper.queryRoleLeader(discern,production));
         List<String> workOrder=tConfigureProductiondayMapper.queryWorkOrder(line,classInfo,production);
         List<String> cplp=tConfigureProductiondayMapper.queryCplh(line);
         List<CustomerScslQop> scslQops=tConfigureProductiondayMapper.queryScslQops(cardId,discern,production,line);
         capacityTrendInfo.setWorkOrder(workOrder);
         capacityTrendInfo.setCustomerScslQops(scslQops);
         capacityTrendInfo.setCplh(cplp);
         return new FebsResponse().data(capacityTrendInfo);
     }



    @ApiOperation(value = "生产线开线趋势",notes = "生产线开线趋势")
    @GetMapping("/trendOfTheLineOpening")
    public FebsResponse trendOfTheLineOpening(@RequestParam("discern")String discern,@RequestParam("dateTime")String dateTime){
        Date date= DateUtil.parseDate(dateTime);
        Date beginDate=DateUtil.beginOfMonth(date);
        String beginDate1=DateUtil.format(beginDate,"yyyy-MM-dd HH:mm:ss");
        Date endDate=DateUtil.endOfMonth(date);
        String endDate1=DateUtil.format(endDate,"yyyy-MM-dd HH:mm:ss");
         List<String> productions=tConfigureProductiondayMapper.queryProduction(discern);
         List<TrendOfTheLineOpening> list=new ArrayList<>();
         for(String production:productions){
                List<DayCountDto> dayCountDtos=tConfigureProductiondayMapper.queryDayCount(production,beginDate1,endDate1);
                TrendOfTheLineOpening trendOfTheLineOpening=new TrendOfTheLineOpening(production,dayCountDtos);
                list.add(trendOfTheLineOpening);
         }
         return new FebsResponse().data(list);
    }

    @PostMapping("/queryProductionReportData")
    @ApiOperation(value = "根据时间查询产量表",notes = "根据时间查询产量表")
    public FebsResponse queryProductionReportData(@RequestBody ProductionReportDataVo productionReportDataDto){
        String beginDate=DateUtil.format(productionReportDataDto.getBeginDate(),"yyyy-MM-dd HH:mm:ss");
        String endDate=DateUtil.format(productionReportDataDto.getEndDate(),"yyyy-MM-dd HH:mm:ss");
        String discern=productionReportDataDto.getDiscern();
        String production=productionReportDataDto.getProduction();
        String productionline=productionReportDataDto.getProductionLine();
        String classInfo=productionReportDataDto.getClassInfo();
        List<TConfigureProductionday> list=tConfigureProductiondayMapper.queryTConfigureProductionDayByTime(beginDate,endDate,discern,production,productionline,classInfo);
            return new FebsResponse().data(list);
    }
    //已经提交的数据
    @ApiOperation(value ="日历查询提交情况",notes = "日历查询提交情况")
    @GetMapping("/queryCalender")
    public FebsResponse queryCalender(@RequestParam("discern")String discern,
                                      @RequestParam("production")String production,
                                      @RequestParam("line")String line,
                                      @RequestParam("classInfo")String classInfo,
                                      @RequestParam("dateTime")String date
                                      ){
        Date date1=DateUtil.parseDate(date);
        Date beginMonthDate=DateUtil.beginOfMonth(date1);
        Date endMonthDate=DateUtil.endOfMonth(date1);
        int day=DateUtil.dayOfMonth(endMonthDate);
        List<Integer> list=new ArrayList<>();
        for(int i=0;i<day;i++){
            Date beginDate=DateUtil.offsetDay(beginMonthDate,i);
            Date beginOfDay=DateUtil.beginOfDay(beginDate);
            String beginDateTime=DateUtil.format(beginOfDay,"yyyy-MM-dd HH:mm:ss");
            Date endOfDay=DateUtil.endOfDay(beginDate);
            String endDateTime=DateUtil.format(endOfDay,"yyyy-MM-dd HH:mm:ss");
            int count=tConfigureProductiondayMapper.queryCountLockByTime(discern,production,line,classInfo,beginDateTime,endDateTime);
            if(count>0){
                list.add(1);
            }else{
                list.add(0);
            }
        }
        return new FebsResponse().data(list);
    }
    @ApiOperation(value ="生产报表资料导出",notes = "生产报表资料导出")
    @PostMapping("/export1")
    public FebsResponse export1(HttpServletResponse response,@RequestParam("discern")String discern,@RequestParam("beginTime")String beginTime,@RequestParam("endTime")String endTime,@RequestParam("production")String production,
                               @RequestParam("productionline")String productionline,@RequestParam("classInfo")String classInfo) throws IOException {
        Date beginDate1=DateUtil.parseDate(beginTime);
        Date endDate1=DateUtil.parseDate(endTime);
        String beginDate=DateUtil.format(beginDate1,"yyyy-MM-dd HH:mm:ss");
        String endDate=DateUtil.format(endDate1,"yyyy-MM-dd HH:mm:ss");
        List<TConfigureProductionday> list=tConfigureProductiondayMapper.queryTConfigureProductionDayByTime(beginDate,endDate,discern,production,productionline,classInfo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition","attachment;filename=demo.xlsx");
        EasyExcel.write(response.getOutputStream(),TConfigureProductionday.class).sheet("日报").doWrite(list);
        return new FebsResponse().message("ok");
    }
    @ApiOperation(value ="产能趋势excel导出",notes = "产能趋势excel导出")
    @GetMapping("/export2")
    public FebsResponse export1(HttpServletResponse response,@RequestParam("discern")String discern,@RequestParam("dateTime")String date,@RequestParam("name")String name) throws IOException {
        CapacityTrend capacityTrend=itConfigureProductiondayService.queryCapacity(discern,date,name);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition","attachment;filename=demo.xlsx");
        return new FebsResponse().message("ok");
    }



}

