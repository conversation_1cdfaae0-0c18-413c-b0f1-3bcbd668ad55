package cc.mrbird.febs.comparation136.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryInformation implements Serializable {

    //課別
    private String section;

    //線體
    private String line;

    //班別
    private String ProductClass;

}
