package cc.mrbird.febs.badRelease.entity.vo;

import cc.mrbird.febs.badRelease.entity.dto.AdditionalInfoDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BadReleaseDetailVo {

    private Integer id;

    //產品處
    private String SBU;

    //機能
    private String functions;

    //課別
    private String section;

    //線體
    private String line;

    //料號
    private String materialNumber;

    //不良釋放日期
    private String badReleaseDate;

    //不良釋放人
    private String name;

    //Ng狀態
    private String NgStatus;

    //Ng原因
    private String NgCause;

    //AI檢測圖片
    private String AIPicture;

    //CCD檢測圖片
    private String CCDPicture;

    //點檢狀態
    private Integer status;

    //檢測結果
    private List<AdditionalInfoDto> result = new ArrayList<>();

}
