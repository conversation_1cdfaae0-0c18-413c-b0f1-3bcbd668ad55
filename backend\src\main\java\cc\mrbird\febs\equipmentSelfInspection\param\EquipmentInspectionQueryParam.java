package cc.mrbird.febs.equipmentSelfInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 李囯斌
 * Date: 2024/9/10
 * Time: 9:47
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class EquipmentInspectionQueryParam {

    @ApiModelProperty(value = "產品処,IDS1|IDS2|IDS3",example = "IDS1",position = 1)
    private String sbu;
    @ApiModelProperty(value = "机能,裝配|成型|電鍍|衝壓",example = "裝配",position = 2)
    private String functions;
    @ApiModelProperty(value = "課別,一課|二課",example = "一課",position = 3)
    private String discern;
    @ApiModelProperty(value = "系列,1700SKT|4189SKT",example = "1700SKT",position = 4)
    private String production;
    @ApiModelProperty(value = "線體,V1|V2",example = "V1",position = 5)
    private String productionLine;
//    @ApiModelProperty(value = "設備名稱,1#插針機|2#插針機|壓平機",example = "2#插針機",position = 3)
//    private String equipmentName;
    @ApiModelProperty(value = "點檢類型,1|2|3",example = "1",position = 6)
    private String selfInspectionType;
    @ApiModelProperty(value = "開始時間,2024-08-15|2024-08-16|2024-09-10",example = "2024-08-15",position = 7)
    private String startDate;
    @ApiModelProperty(value = "結束時間,2024-08-15|2024-08-16|2024-09-10",example = "2024-08-15",position = 8)
    private String endDate;
//    @ApiModelProperty(value = "狀態,null|1|2|3|4|5",example = "3",position = 6)
//    private String status;//null待點檢，1[XXX]點檢中，2待[XXX]確認，3點擊完成，4駁回中，5不點檢
    @ApiModelProperty(value = "設備ID",required = false,example = "P5AA1700V2-01",position = 9)
    private String equipmentID;
    @ApiModelProperty(value = "時間,2024-08-15|2024-08-16|2024-09-10",example = "2024-08-15",position = 10)
    private String date;
}
