package cc.mrbird.febs.comparation136.result;

import cc.mrbird.febs.backwork.common.R;
import lombok.Data;

import java.io.Serializable;

/**
 * 后端统一返回结果
 * @param <T>
 */
@Data
public class Result<T> implements Serializable {

    private Integer code; //编码：200代表成功，500和其他數字代表失敗
    private String msg; //错误信息
    private T data; //数据

    public static <T> Result<T> success() {
        Result<T> result = new Result<T>();
        result.code = 200;
        return result;
    }

    public static <T> Result<T> success(T object) {
        Result<T> result = new Result<T>();
        result.data = object;
        result.code = 200;
        return result;
    }

    public static <T> Result<T> error(String msg) {
        Result result = new Result();
        result.msg = msg;
        result.code = 500;
        return result;
    }

    public static <T> Result<T> data(Object data) {
        Result result = new Result<>();
        result.data = data;
        result.code = 200;
        return result;
    }

}
