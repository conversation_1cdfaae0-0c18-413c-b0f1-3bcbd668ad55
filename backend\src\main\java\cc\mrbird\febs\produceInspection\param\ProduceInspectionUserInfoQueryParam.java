package cc.mrbird.febs.produceInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表人员信息查询参数
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionUserInfoQueryParam {

    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;

    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;

    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;

    @ApiModelProperty(value = "工號", example = "EMP001", position = 4)
    private String workId;

    @ApiModelProperty(value = "名字", example = "張三", position = 5)
    private String name;

    @ApiModelProperty(value = "人員類型 1：點檢人 2：確認人", example = "1", position = 6)
    private String type;

    @ApiModelProperty(value = "系列", example = "1700SKT", position = 7)
    private String series;

    @ApiModelProperty(value = "綫體", example = "V1", position = 8)
    private String line;
}