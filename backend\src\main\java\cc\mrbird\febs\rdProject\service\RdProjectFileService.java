package cc.mrbird.febs.rdProject.service;

import cc.mrbird.febs.rdProject.entity.RdProjectFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/22
 * Time: 10:49
 * 研究項目協同管理的任務附件服務接口
 */
public interface RdProjectFileService {

    /**
     * 批量保存附件文件
     * @param taskId 任務ID
     * @param attachments 附件列表
     * @throws IOException 文件操作異常
     */
    void saveAttachments(Long taskId, List<MultipartFile> attachments) throws IOException;

    /**
     * 根據任務ID查詢文件列表
     * @param taskId 任務ID
     * @return 文件列表
     */
    List<RdProjectFile> getFilesByTaskId(Long taskId);

    /**
     * 根據文件ID刪除文件
     * @param fileId 文件ID
     * @throws IOException 文件操作異常
     */
    void deleteFileById(Long fileId) throws IOException;

    /**
     * 根據任務ID刪除所有相關文件
     * @param taskId 任務ID
     * @throws IOException 文件操作異常
     */
    void deleteFilesByTaskId(Long taskId) throws IOException;
}
