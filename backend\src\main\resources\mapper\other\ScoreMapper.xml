<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.other.dao.ScoreMapper">
    <select id="selectScoreData" resultType="cc.mrbird.febs.other.entity.ScoreDto">
    </select>

    <select id="selectAllCompetition" resultType="java.lang.String">
        USE APPGCL_Process
        SELECT SS_Name
        FROM [PF_SSManage]
    </select>

    <select id="selectCompetitionId" resultType="java.lang.Integer">
        USE APPGCL_Process
        SELECT id
        FROM [PF_SSManage]
        WHERE SS_Name = #{competitionName}
    </select>

    <select id="selectAllDataById" resultType="cc.mrbird.febs.other.entity.SelectScoreVo">
        USE APPGCL_Process
        SELECT Prize AS prize, Project AS projectName, PlayerName AS userName, AdjustedAverageFraction AS score,AllJudgesAndScores
        FROM [APPGCL_Process].[dbo].[RankedPlayers]
        WHERE Name = #{name}
    </select>

    <select id="getCompetitionData" resultType="cc.mrbird.febs.other.entity.CompetitionConfVo">
        USE APPGCL_Process
        SELECT SS_Name AS project, ZBDW_Organizer AS sponsor, Ins_Date AS startTime
        FROM [APPGCL_Process].[dbo].[PF_SSManage]
        WHERE SS_Name = #{competitionName}
    </select>

    <select id="getScoreBYSort" resultType="cc.mrbird.febs.other.entity.ScoreBySortVo">
        USE APPGCL_Process
        SELECT PlayerName AS userName, Project AS projectName
        FROM [APPGCL_Process].[dbo].[RankedPlayers]
        WHERE Prize = #{param1} AND Name=#{param2}
    </select>

    <select id="getCompetitionConfig" resultType="cc.mrbird.febs.other.entity.CompetitionConfigVo">
        USE APPGCL_Process
        SELECT [SS_PFBZ] AS markStandard
             ,[SS_PFGZ] AS ScoreRule FROM [PF_SSManage] WHERE SS_Name=#{competitionName}
    </select>
</mapper>