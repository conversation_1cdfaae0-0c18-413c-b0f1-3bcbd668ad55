package cc.mrbird.febs.line.dao;

import cc.mrbird.febs.line.entity.LineInfo;
import cc.mrbird.febs.line.entity.LineInfos;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DS("mssql")
@Mapper
public interface LineMapper {
    List<String> queryProducitonLine(String production,String cardId);
    List<String> queryDiscern(String cardId);

    List<String> queryProduction(String cardId,String discern);

    @DS("primary")
    String queryName(String cardId);

    @DS("primary")
    String queryRoleId(String cardId);

    List<String> queryDiscernBySection(String cardId);

    List<String> queryDiscernByGroup(String cardId);

    List<String> queryProductionBySection(String cardId, String discern);

    List<String> queryProductionByGroup(String cardId, String discern);

    List<String> queryProducitonLineBySection(String production, String cardId);

    List<String> queryProducitonLineByGroup(String production, String cardId);

}
