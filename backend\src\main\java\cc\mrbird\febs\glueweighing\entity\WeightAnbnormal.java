package cc.mrbird.febs.glueweighing.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeightAnbnormal {
    private String uuid;
    @ApiModelProperty(value = "量測指標",position = 0)
    private String measurementIndex;
    @ApiModelProperty(value = "量測序列號",position = 1)
    private String measurementNum;
    @ApiModelProperty(value = "規格最小值",position = 2)
    private String specificationMin;
    @ApiModelProperty(value = "規格最大值",position = 3)
    private String specificationMax;
    @ApiModelProperty(value = "點膠前重量",position = 4)
    private String oldWeight;
    @ApiModelProperty(value = "點膠後重量",position = 5)
    private String newWeight;
    @ApiModelProperty(value = "膠重",position = 6)
    private String glueWeight;
    @ApiModelProperty(value = "判定結果",position=7)
    private String judgementResult;
}
