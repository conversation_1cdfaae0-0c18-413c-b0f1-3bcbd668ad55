<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectTeamMapper">

    <!-- 結果映射 -->
    <resultMap id="RdProjectTeamResultMap" type="cc.mrbird.febs.rdProject.entity.RdProjectTeam">
        <id column="team_id" property="teamId"/>
        <result column="team_name" property="teamName"/>
        <result column="site_area" property="siteArea"/>
        <result column="sbu" property="sbu"/>
        <result column="functions" property="functions"/>
        <result column="section" property="section"/>
    </resultMap>

    <!-- 插入團隊信息 -->
    <insert id="insertRdProjectTeam" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTeam" useGeneratedKeys="true" keyProperty="teamId">
        INSERT INTO rd_project_team (team_name, site_area, sbu, functions, section)
        VALUES (#{teamName}, #{siteArea}, #{sbu}, #{functions}, #{section})
    </insert>

    <!-- 根據團隊ID刪除團隊信息 -->
    <delete id="deleteRdProjectTeamById">
        DELETE FROM rd_project_team WHERE team_id = #{teamId}
    </delete>

    <!-- 更新團隊信息 -->
    <update id="updateRdProjectTeam" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTeam">
        UPDATE rd_project_team
        SET team_name = #{teamName},
            site_area = #{siteArea},
            sbu = #{sbu},
            functions = #{functions},
            section = #{section}
        WHERE team_id = #{teamId}
    </update>

    <!-- 根據團隊ID查詢團隊信息 -->
    <select id="selectRdProjectTeamById" resultMap="RdProjectTeamResultMap">
        SELECT team_id, team_name, site_area, sbu, functions, section
        FROM rd_project_team
        WHERE team_id = #{teamId}
    </select>

    <!-- 根據團隊名稱查詢團隊信息 -->
    <select id="selectRdProjectTeamByName" resultMap="RdProjectTeamResultMap">
        SELECT team_id, team_name, site_area, sbu, functions, section
        FROM rd_project_team
        WHERE team_name = #{teamName}
    </select>

    <!-- 根據廠區查詢團隊列表 -->
    <select id="selectRdProjectTeamBySiteArea" resultMap="RdProjectTeamResultMap">
        SELECT team_id, team_name, site_area, sbu, functions, section
        FROM rd_project_team
        WHERE site_area = #{siteArea}
    </select>

    <!-- 根據產品処查詢團隊列表 -->
    <select id="selectRdProjectTeamBySbu" resultMap="RdProjectTeamResultMap">
        SELECT team_id, team_name, site_area, sbu, functions, section
        FROM rd_project_team
        WHERE sbu = #{sbu}
    </select>

    <!-- 根據機能查詢團隊列表 -->
    <select id="selectRdProjectTeamByFunctions" resultMap="RdProjectTeamResultMap">
        SELECT team_id, team_name, site_area, sbu, functions, section
        FROM rd_project_team
        WHERE functions = #{functions}
    </select>

    <!-- 根據課別查詢團隊列表 -->
    <select id="selectRdProjectTeamBySection" resultMap="RdProjectTeamResultMap">
        SELECT team_id, team_name, site_area, sbu, functions, section
        FROM rd_project_team
        WHERE section = #{section}
    </select>

    <!-- 查詢所有團隊信息 -->
    <select id="selectAllRdProjectTeam" resultMap="RdProjectTeamResultMap">
        SELECT team_id, team_name, site_area, sbu, functions, section
        FROM rd_project_team
    </select>

</mapper>
