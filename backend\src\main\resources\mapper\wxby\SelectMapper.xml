<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.wxby.dao.SelectMapper">

    <select id="selectAllByLine" resultType="cc.mrbird.febs.wxby.entity.EquipmentNewError">
    USE IDS_BK_PL1_01
    SELECT
    ProductionLine,ProductionLineCode,Equipment_Name,Equipment_id,classId,classdate,ErrorCode,ErrorDesc,Begin_DT,End_DT,
    breakdown,ErrorType,GroupName,transactor,transactorId,Causation,Countermeasures,status
    FROM V_Data_Equipment_NewError
    WHERE DepartmentCode = 'AS'
 </select>

    <select id="queryALlEquipmentErrorLog" resultType="cc.mrbird.febs.wxby.entity.MaintenanceLog">
        USE IDS_BK_PL1_01
        SELECT * FROM V_Data_Equipment_MaintenanceLog  WHERE state = '已處理'
    </select>

    <select id="selectCountByMaintenance" resultType="java.lang.Integer">
        USE IDS_BK_PL1_01
        SELECT COUNT(*) FROM V_Data_Equipment_MaintenanceLog
    </select>

    <select id="queryProductionAndProductionLine" resultType="cc.mrbird.febs.wxby.entity.ProductionAndProductionLine">
        USE IDS_BK_PL1_01
        SELECT ProductRange AS production,ProductionLine AS productionLine  FROM T_Data_Group_Eq WHERE Equipment_id=#{equipmentId}
    </select>

    <select id="queryCallErrorEquipmentData" resultType="cc.mrbird.febs.wxby.entity.dto.TDataEquipmentError">
        USE IDS_BK_PL1_01
        SELECT id, Equipment_id AS equipmentId,Equipment_Name AS equipmentName, CASE WHEN classId =1 then '晚班' WHEN classId =2 then '白班' end AS classId,classdate AS classDate,ErrorDesc AS errorDesc,Begin_DT AS startTime,
            End_DT AS endTime,transactor,Causation AS causation,Countermeasures AS countermeasures,breakdown  / 60 AS breakdown,status,ErrorType AS errorType
                ,CASE WHEN CallEmpName IS NULL then transactor else CallEmpName end AS callEmpName
        FROM
            T_Data_Equipment_error1
        WHERE classdate >='2024-01-01' AND Equipment_id like '%BKAS%' AND status='已處理'
    </select>

    <select id="queryWxsh" resultType="cc.mrbird.febs.wxby.entity.dto.TConfigurePersonByError">
        USE IDS_BK_PL1_01
        SELECT pl,discern, wxsh FROM T_Configure_Person WHERE zrsj=#{name}
    </select>
</mapper>