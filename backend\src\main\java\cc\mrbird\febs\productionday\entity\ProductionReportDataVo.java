package cc.mrbird.febs.productionday.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProductionReportDataVo {
    @ApiModelProperty(value = "开始时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date beginDate;
    @ApiModelProperty(value = "结束时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endDate;
    @ApiModelProperty(value = "课别",required = true)
    private String discern;
    @ApiModelProperty(value = "系列",required = true)
    private String production;
    @ApiModelProperty(value = "线体",required = true)
    private String productionLine;
    @ApiModelProperty(value = "班别",required = true)
    private String classInfo;
}
