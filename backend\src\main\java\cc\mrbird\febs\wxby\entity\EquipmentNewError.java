package cc.mrbird.febs.wxby.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_Data_Equipment_error1")
public class EquipmentNewError {
            @TableField(value = "DepartmentCode")
            private String departmentCode;
            @TableField(value = "ProductionLine")
            private String productionLine;
            @TableField(value = "ProductionLineCode")
            private String productionLineCode;
            @TableField(value = "EquipmentName")
            private String equipmentName;
            @TableField(value = "Equipment_id")
            private String equipmentId;
            @TableField(value = "classId")
            private String classId;
            @TableField(value = "classdate")
            private String classDate;
            @TableField(value = "ErrorDesc")
            private String errorDesc;
            @TableField(value = "Begin_DT")
            private String beginDT;
            @TableField(value = "End_DT")
            private String endDT;
            @TableField(value = "breakdown")
            private String breakdown;
            @TableField(value = "ErrorType")
            private String errorType;
            @TableField(value = "GroupName")
            private String groupName;
            @TableField(value = "transactor")
            private String transactor;
            @TableField(value = "transactorId")
            private String transactorId;
            @TableField(value = "Causation")
            private String causation;
            @TableField(value = "Countermeasures")
            private String countermeasures;
            @TableField(value = "status")
            private String status;
}
