package cc.mrbird.febs.indexpage.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VisitPageData {
    @ApiModelProperty(value = "工号",position = 0)
    private String workId;
    @ApiModelProperty(value = "页面名称",position = 1)
    private String pageName;
    @ApiModelProperty(value = "访问时间",position = 2)
    private String visitTime;
    @ApiModelProperty(value = "表主键",position = 3)
    @JsonIgnore
    private Integer id;
    @ApiModelProperty(value = "图标",position = 4)
    private String visitType;
    @ApiModelProperty(value = "url",position = 5)
    private String visitUrl;

}
