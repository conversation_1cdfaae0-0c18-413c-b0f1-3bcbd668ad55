package cc.mrbird.febs.lessDaysheet.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.lessDaysheet.dao.BadStatementMapper;
import cc.mrbird.febs.lessDaysheet.dao.workStationMapper;
import cc.mrbird.febs.lessDaysheet.entity.BadStatementUUID;
import cc.mrbird.febs.lessDaysheet.entity.ConfigureData;
import cc.mrbird.febs.lessDaysheet.entity.LineData;
import cc.mrbird.febs.lessDaysheet.entity.dto.*;
import cc.mrbird.febs.lessDaysheet.entity.vo.BadStatementSelectVo;
import cc.mrbird.febs.lessDaysheet.entity.vo.BadStatementVo;
import cn.hutool.core.lang.UUID;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.sound.sampled.Line;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RestController
@ResponseBody
@Api(tags = "小不良制程日报表")
public class BadStatementController {
    @Autowired
    private BadStatementMapper badStatementMapper;

    @Autowired
    private workStationMapper workStationMapper;

    //insert
    @PostMapping("/insertAll")
    @ApiOperation(value = "插入接口")
    public FebsResponse insertAll(@RequestBody BadStatementDto badStatementDto) {
        UUID uuid = UUID.randomUUID();
        String badStatementId = uuid.toString();
        String badStatementUUID = uuid.toString();
        List<BadStatementUUID> list = badStatementDto.getBadStatementUUID();
        for (BadStatementUUID ll : list) {
            ll.setUuid(badStatementUUID);
        }
        badStatementDto.getBadStatementList().setUuid(badStatementId);
        boolean b = badStatementMapper.insertAll(badStatementDto.getBadStatementList());
        boolean c = badStatementMapper.insertByBadStatementUUID(badStatementDto.getBadStatementUUID());
        if (b) {
            return new FebsResponse().code("200").message("插入成功");
        } else {
            return new FebsResponse().code("401").message("插入失败,请考虑不给checkStatus传值!");
        }
    }
    //select
    @PostMapping("/selectAllByBadStatement")
    @ApiOperation("查询接口")
    public FebsResponse selectAllByBadStatement(@RequestParam("uuid") String uuid) {
        List<BadStatementVo> list = badStatementMapper.selectAllByBadStatement(uuid);
        return new FebsResponse().code("200").data(list);
    }
    //check
    @PostMapping("/checkByLineLeader")
    @ApiOperation("线长签核接口")
    public FebsResponse checkByLineLeader(@RequestBody BadStatementUpdateDto badStatementUpdateDto) {
        boolean b = badStatementMapper.updateByStatementOnLineLeader(badStatementUpdateDto);
        String o = "";
        if (b ) {
            o = "签核成功";
            return new FebsResponse().code("200").message(o);
        } else {
            o = "签核失败";
            return new FebsResponse().code("401").message(o);
        }
    }
    @PostMapping("/checkByQc")
    @ApiOperation("qc签核")
    public FebsResponse checkByQc(@RequestBody List<BadStatementQcDto> badStatementQcDto) {
        boolean b = badStatementMapper.updateByStatementOnQc(badStatementQcDto);
        String o = "";
        if (b) {
            o = "签核成功";
            return new FebsResponse().code("200").message(o);
        } else {
            o = "签核失败";
            return new FebsResponse().code("401").message(o);
        }
    }
    @PostMapping("/selectStation")
    @ApiOperation("根據系列查詢工站")
    public FebsResponse selectStation(@RequestBody WorkStationDto workStationDto) {
        List<String> list = workStationMapper.selectWorkStation(workStationDto);
        List<String> list2 =workStationMapper.selectWorkStationByPl2AndPl3(workStationDto);
        if (list.isEmpty()){
            list.addAll(list2);
        }
        return new FebsResponse().code("200").data(list);
    }
    @PostMapping("/selectVo")
    @ApiOperation(("条件查询页面查询页面"))
    public FebsResponse selectVo(@RequestBody BadStatementSelectDto badStatementSelectDto) {
      /*  List<BadStatement> badStatement = badStatementMapper.selectBadStatementVo(badStatementSelectDto);
        List<BadStatementUUID> list =null;
        if (badStatement !=null){
            for (BadStatement bb : badStatement ){
                int i=0;
                i++;
                badStatement.get(i).getUuid();
                list =badStatementMapper.selectBadItem(bb.getUuid());
            }
            return  new FebsResponse().code("200").data(list);
        }else {
            return  new FebsResponse().code("200").data(null);
        }*/
        List<BadStatementSelectVo> list = badStatementMapper.selectBadStatementVo(badStatementSelectDto);
        return new FebsResponse().code("200").data(list);
    }

    @PostMapping("/update")
    @ApiOperation(value = "编辑接口")
    @DSTransactional()
    public FebsResponse update(@RequestBody BadStatementEdit badStatementEdit) {
        String uuid =badStatementEdit.getUuid();
        List<BadStatementUUID> list =badStatementEdit.getBadStatementUUID();
       /* for (BadStatementUUID ll : list){
            ll.setUuid(uuid);
        }*/
        list.forEach(ll->{
            ll.setUuid(uuid);
        });
        boolean b = badStatementMapper.badStatementByEdit(badStatementEdit);
        boolean a =badStatementMapper.delByBadStatementUUID(badStatementEdit.getUuid());
        boolean c =badStatementMapper.insertByBadStatementUUID(badStatementEdit.getBadStatementUUID());
        return new FebsResponse().code("200").message("修改成功");
    }

    @PostMapping("/selectSection")
    @ApiOperation(value = "根據事業處、課別查詢系列")
    public FebsResponse selectSection(@RequestBody ConfigureData configureData){
          List<String> list =  workStationMapper.selectProductionAndProductionLine(configureData);
          List<String> list2 = workStationMapper.selectOrNullByProductioon(configureData);
          if (list.isEmpty()){
              list.addAll(list2);
          }
              return  new FebsResponse().code("200").data(list);
    }
    @PostMapping("/selectLine")
    @ApiOperation(value = "根據課別、系列查詢线体")
    public FebsResponse selectLine(@RequestBody LineData lineData){
        List<String> list =workStationMapper.selectProductionLine(lineData);
        List<String>list2 =workStationMapper.selectNULLByProductionLine(lineData);
        if (list.isEmpty()){
            list.addAll(list2);
        }
        return new FebsResponse().code("200").data(list);
    }


    /**
    *
    * <AUTHOR>
    * @date 2023-06-17
     * 根据工号获取角色  给前端返回  ： 0线长 1 QC
    */
    @ApiModelProperty("根据工号获取角色身份   0线长  1qc")
    @GetMapping("/getRole")
    public FebsResponse getRole(@RequestParam("workId") String workId){
        int a = badStatementMapper.getRole(workId);
        return new FebsResponse().code("200").data(a);
    }



}