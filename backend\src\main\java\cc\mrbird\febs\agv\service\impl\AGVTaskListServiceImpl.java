package cc.mrbird.febs.agv.service.impl;

import cc.mrbird.febs.agv.dao.AGVTaskListMapper;
import cc.mrbird.febs.agv.entity.AGVLogList;
import cc.mrbird.febs.agv.entity.AGVTaskList;
import cc.mrbird.febs.agv.param.AGVTaskListQueryParam;
import cc.mrbird.febs.agv.service.AGVTaskListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AGVTaskListServiceImpl implements AGVTaskListService {
    private final AGVTaskListMapper agvTaskListMapper;

    @Autowired
    public AGVTaskListServiceImpl(AGVTaskListMapper agvTaskListMapper) {
        this.agvTaskListMapper = agvTaskListMapper;
    }


    @Override
    public List<AGVTaskList> queryAGVTaskList(AGVTaskListQueryParam agvTaskListQueryParam) {
        return agvTaskListMapper.queryAGVTaskList(agvTaskListQueryParam);
    }
}