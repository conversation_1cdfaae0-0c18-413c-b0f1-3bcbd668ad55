package cc.mrbird.febs.newo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SelectByDC {
    private String planOutput;
    private String realOutput;
    private String productQuantity;
    private String customerName;
    private String customerQuantity;
    private String ticker;
    private String dc;
    private String inboundOrderNumber;
    private String person;
    private String dateTime;
    private String classInfo;
    private String tdw;
    private String processingTalk;
    private String connectTalk;
    private String otherTalk;
    private String production;
    private String productionLine;
}
