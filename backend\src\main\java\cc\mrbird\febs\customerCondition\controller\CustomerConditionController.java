package cc.mrbird.febs.customerCondition.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.customerCondition.entity.*;
import cc.mrbird.febs.customerCondition.entity.dto.CustomerConditionDto;
import cc.mrbird.febs.customerCondition.entity.dto.CustomerConditionFileDto;
import cc.mrbird.febs.customerCondition.param.CustomerConditionQueryParam;
import cc.mrbird.febs.customerCondition.param.CustomerConditionUserQueryParam;
import cc.mrbird.febs.customerCondition.service.CustomerConditionService;
import cc.mrbird.febs.customerCondition.task.CustomerConditionScheduledTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/4/11
 * Time: 10:49
 * 客戶條件記錄控制器
 */
@RestController
@RequestMapping("/customerCondition")
@Api(tags = "客戶條件記錄接口")
public class CustomerConditionController {

    private final CustomerConditionService customerConditionService;

    @Autowired
    public CustomerConditionController(CustomerConditionService customerConditionService) {
        this.customerConditionService = customerConditionService;
    }

    @GetMapping("/queryCustomerList")
    @ApiOperation("查詢客戶列表")
    public FebsResponse queryCustomerList() {
        List<CustomerConditionCustomerData> list;
        try {
            list = customerConditionService.queryCustomerList();
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢客戶列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/queryCustomerById")
    @ApiOperation("根據ID查詢客戶")
    @ApiImplicitParam(name = "customerId", value = "客戶ID", required = true, example = "1")
    public FebsResponse queryCustomerById(@RequestParam("customerId") Integer customerId) {
        CustomerConditionCustomerData customer;
        try {
            customer = customerConditionService.queryCustomerById(customerId);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢客戶失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(customer);
    }

    @GetMapping("/querySiteAreaList")
    @ApiOperation("查詢廠區列表")
    public FebsResponse querySiteAreaList() {
        List<String> list;
        try {
            list = customerConditionService.querySiteAreaList();
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢廠區列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/querySbuBySiteArea")
    @ApiOperation("根據廠區查詢產品処列表")
    @ApiImplicitParam(name = "siteArea", value = "廠區", required = true, example = "寳科園區")
    public FebsResponse querySbuBySiteArea(@RequestParam("siteArea") String siteArea) {
        List<String> list;
        try {
            list = customerConditionService.querySbuBySiteArea(siteArea);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢產品処列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/queryFunctionsBySiteAreaAndSbu")
    @ApiOperation("根據廠區和產品処查詢機能列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteArea", value = "廠區", required = true, example = "寳科園區"),
            @ApiImplicitParam(name = "sbu", value = "產品処", required = true, example = "IDS1")
    })
    public FebsResponse queryFunctionsBySiteAreaAndSbu(
            @RequestParam("siteArea") String siteArea,
            @RequestParam("sbu") String sbu) {
        List<String> list;
        try {
            list = customerConditionService.queryFunctionsBySiteAreaAndSbu(siteArea, sbu);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢機能列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/querySectionBySiteAreaAndSbuAndFunctions")
    @ApiOperation("根據廠區、產品処和機能查詢課別列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteArea", value = "廠區", required = true, example = "寳科園區"),
            @ApiImplicitParam(name = "sbu", value = "產品処", required = true, example = "IDS1"),
            @ApiImplicitParam(name = "functions", value = "機能", required = true, example = "裝配")
    })
    public FebsResponse querySectionBySiteAreaAndSbuAndFunctions(
            @RequestParam("siteArea") String siteArea,
            @RequestParam("sbu") String sbu,
            @RequestParam("functions") String functions) {
        List<String> list;
        try {
            list = customerConditionService.querySectionBySiteAreaAndSbuAndFunctions(siteArea, sbu, functions);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢課別列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }


    @PostMapping("/queryCustomerConditionDto")
    @ApiOperation("查詢客戶條件記錄主要信息")
    public FebsResponse queryCustomerConditionDto(@RequestBody CustomerConditionQueryParam queryParam) {
        List<CustomerConditionDto> list = new ArrayList<>();
        try {
            list = customerConditionService.queryCustomerConditionDto(queryParam);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢客戶條件記錄主要信息失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/queryCustomerConditionFileById")
    @ApiOperation("查詢客戶條件記錄文件")
    @ApiImplicitParam(name = "fileId", value = "文件ID", required = true, example = "1")
    public FebsResponse queryCustomerConditionFileById(@RequestParam("fileId") Integer fileId) {
        CustomerConditionFile file;
        try {
            file = customerConditionService.queryCustomerConditionFileById(fileId);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢客戶條件記錄文件失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(file);
    }

    @GetMapping("/queryCustomerConditionUsers")
    @ApiOperation("查詢客戶條件記錄用戶")
    public FebsResponse queryCustomerConditionUsers() {
        List<CustomerConditionUser> list;
        try {
            list = customerConditionService.queryCustomerConditionUsers();
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢客戶條件記錄用戶失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/queryCustomerConditionMails")
    @ApiOperation("查詢客戶條件記錄郵件")
    @ApiImplicitParam(name = "customerId", value = "客戶ID", required = true, example = "1")
    public FebsResponse queryCustomerConditionMails(@RequestParam("customerId") Integer customerId) {
        List<CustomerConditionMail> list;
        try {
            list = customerConditionService.queryCustomerConditionMails(customerId);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢客戶條件記錄郵件失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @PostMapping("/uploadCustomerConditionFile")
    @ApiOperation("上傳客戶條件記錄文件")
    public FebsResponse uploadCustomerConditionFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("sbu") String sbu,
            @RequestParam("functions") String functions,
            @RequestParam("siteArea") String siteArea,
            @RequestParam("section") String section,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("customerName") String customerName,
            @RequestParam("uploadUserWorkId") String uploadUserWorkId,
            @RequestParam("uploadUserName") String uploadUserName,
            @RequestParam(value = "updateRemark", required = false) String updateRemark) {
        try {
            CustomerConditionMainInfo mainInfo = new CustomerConditionMainInfo();
            mainInfo.setSbu(sbu);
            mainInfo.setFunctions(functions);
            mainInfo.setSiteArea(siteArea);
            mainInfo.setSection(section);
            mainInfo.setCustomerId(customerId);
            mainInfo.setCustomerName(customerName);
            mainInfo.setUploadUserWorkId(uploadUserWorkId);
            mainInfo.setUploadUserName(uploadUserName);
            mainInfo.setUpdateRemark(updateRemark);

            CustomerConditionMainInfo result = customerConditionService.uploadCustomerConditionFile(file, mainInfo);
            return new FebsResponse().code("200").message("上傳成功").data(result);
        } catch (IOException e) {
            return new FebsResponse().code("500").message("上傳文件失敗：" + e.getMessage());
        }
    }


    @PostMapping("/queryCustomerConditionUsersByCondition")
    @ApiOperation("根據多條件查詢用戶信息")
    public FebsResponse queryCustomerConditionUsersByCondition(@RequestBody CustomerConditionUserQueryParam queryParam) {
        List<CustomerConditionUser> list;
        try {
            list = customerConditionService.queryCustomerConditionUsersByCondition(queryParam);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("根據多條件查詢用戶信息失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

//    @GetMapping("/queryCustomerConditionUploadCount")
//    @ApiOperation("查詢客戶條件記錄上傳次數")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "siteArea", value = "廠區", required = true, example = "寳科園區"),
//            @ApiImplicitParam(name = "sbu", value = "產品処", required = true, example = "IDS1"),
//            @ApiImplicitParam(name = "functions", value = "機能", required = true, example = "裝配"),
//            @ApiImplicitParam(name = "section", value = "課別", required = true, example = "裝配一課"),
//            @ApiImplicitParam(name = "customerId", value = "客戶ID", required = true, example = "1")
//    })
//    public FebsResponse queryCustomerConditionUploadCount(
//            @RequestParam("siteArea") String siteArea,
//            @RequestParam("sbu") String sbu,
//            @RequestParam("functions") String functions,
//            @RequestParam("section") String section,
//            @RequestParam("customerId") Integer customerId) {
//        int count;
//        try {
//            count = customerConditionService.queryCustomerConditionUploadCount(siteArea, sbu, functions, section, customerId);
//            // 版本號的上傳次數是查詢到的數量加1
//            count = count ;
//        } catch (Exception e) {
//            return new FebsResponse().code("500").message("查詢客戶條件記錄上傳次數失敗：" + e.getMessage());
//        }
//        return new FebsResponse().code("200").data(count);
//    }

    @GetMapping("/customer-condition-file/{fileId}")
    @ApiOperation("根據文件ID訪問/下載文件")
    @ApiImplicitParam(name = "fileId", value = "文件ID", required = true, example = "1")
    @CrossOrigin(origins = "*")
    public void accessFileById(@PathVariable("fileId") Integer fileId, HttpServletResponse response) {
        try {
            customerConditionService.accessFileById(fileId, response);
        } catch (IOException e) {
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("訪問文件失敗：" + e.getMessage());
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (IOException ex) {
                // 忽略
            }
        }
    }
//
//    @GetMapping("/customer-condition-upload/{fileName:.+}")
//    @ApiOperation("根據文件名直接訪問文件")
//    @ApiImplicitParam(name = "fileName", value = "文件名", required = true, example = "example.xlsx")
//    @CrossOrigin(origins = "*")
//    public void accessFileByName(@PathVariable("fileName") String fileName, HttpServletResponse response) {
//        try {
//            customerConditionService.accessFileByName(fileName, response);
//        } catch (IOException e) {
//            try {
//                response.setContentType("text/plain;charset=UTF-8");
//                response.getWriter().write("訪問文件失敗：" + e.getMessage());
//                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
//            } catch (IOException ex) {
//                // 忽略
//            }
//        }
//    }

    @GetMapping("/customer-condition-upload/{year}/{month}/{fileName:.+}")
    @ApiOperation("根據年月和文件名直接訪問客戶記錄條件表文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "year", value = "年份", required = true, example = "2025"),
            @ApiImplicitParam(name = "month", value = "月份", required = true, example = "04"),
            @ApiImplicitParam(name = "fileName", value = "文件名", required = true, example = "9a43d091-d84d-4130-b5f7-e8e21f9a10cf.xlsx")
    })
    @CrossOrigin(origins = "*")
    public void accessFileByYearMonthAndName(
            @PathVariable("year") String year,
            @PathVariable("month") String month,
            @PathVariable("fileName") String fileName,
            HttpServletResponse response) {
        try {
            // 直接調用服務方法
            customerConditionService.accessFileByYearMonthAndName(year, month, fileName, response);
        } catch (IOException e) {
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("訪問文件失敗：" + e.getMessage());
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (IOException ex) {
                // 忽略
            }
        }
    }

    @PostMapping("/uploadCustomerConditionSummaryFile")
    @ApiOperation("上傳客戶條件記錄summary文件")
    public FebsResponse uploadCustomerConditionSummaryFile(@RequestParam("file") MultipartFile file) {
        try {
            CustomerConditionSummaryFile result = customerConditionService.uploadCustomerConditionSummaryFile(file);
            return new FebsResponse().code("200").message("上傳成功").data(result);
        } catch (IOException e) {
            return new FebsResponse().code("500").message("上傳summary文件失敗：" + e.getMessage());
        }
    }

    @GetMapping("/downloadLatestCustomerConditionSummaryFile")
    @ApiOperation("下載最新的客戶條件記錄summary文件")
    @CrossOrigin(origins = "*")
    public void downloadLatestCustomerConditionSummaryFile(HttpServletResponse response) {
        try {
            customerConditionService.downloadLatestCustomerConditionSummaryFile(response);
        } catch (IOException e) {
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("下載最新summary文件失敗：" + e.getMessage());
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (IOException ex) {
                // 忽略
            }
        }
    }

//    @Autowired
//    private CustomerConditionScheduledTask customerConditionScheduledTask;
//
//    @GetMapping("/manualSendLatestSummaryFileToAllEmails")
//    @ApiOperation("手動觸發發送最新summary文件給所有郵箱（僅用於測試）")
//    public FebsResponse manualSendLatestSummaryFileToAllEmails() {
//        try {
//            customerConditionScheduledTask.manualSendLatestSummaryFileToAllEmails();
//            return new FebsResponse().code("200").message("手動觸發發送最新summary文件成功");
//        } catch (Exception e) {
//            return new FebsResponse().code("500").message("手動觸發發送最新summary文件失敗：" + e.getMessage());
//        }
//    }
//
//    @GetMapping("/manualRemindLocalCsForCustomersWithoutUpdate")
//    @ApiOperation("手動觸發提醒本月未更新客戶條件記錄的local cs（僅用於測試）")
//    public FebsResponse manualRemindLocalCsForCustomersWithoutUpdate() {
//        try {
//            customerConditionScheduledTask.manualRemindLocalCsForCustomersWithoutUpdate();
//            return new FebsResponse().code("200").message("手動觸發提醒郵件成功");
//        } catch (Exception e) {
//            return new FebsResponse().code("500").message("手動觸發提醒郵件失敗：" + e.getMessage());
//        }
//    }
}
