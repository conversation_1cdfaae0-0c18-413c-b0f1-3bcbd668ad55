package cc.mrbird.febs.indexpage.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataCommentAndUsers {
    private Integer id;
    private String content;
    private String createdAt;
    private Integer parentId;
    private Integer lever;
    private String userName;
    private String avatar;
    private String workId;
    private Integer isSort;
    private List<TDataCommentAndUsers> replies = new ArrayList<>();
    private List<TDataCommentAndUsers> isHF = new ArrayList<>();

}
