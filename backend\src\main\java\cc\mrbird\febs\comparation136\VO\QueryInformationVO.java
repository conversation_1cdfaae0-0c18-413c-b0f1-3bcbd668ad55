package cc.mrbird.febs.comparation136.VO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.text.DateFormat;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryInformationVO implements Serializable {

    private long id;

    //IDS1
    private String SBU;

    //機能
    private String functions;

    //課別
    private String section;

    //线体
    private String line;

    //班別
    private String ProductClass;

    //產品料號
    private String CPLHPFN;

    //比對日期
    private String BDDate;

    //是否NG
    private String SFNG;

    //NG原因
    private String NGCause;

    //簽核狀態
    private Integer STATUS;

    //建立人名字
    private String JLRName;

    //品保簽核名字
    private String IPQCQHRName;

    //組長簽核名字
    private String ZZQHRName;

    //創建時間
    private String InsDate;

}
