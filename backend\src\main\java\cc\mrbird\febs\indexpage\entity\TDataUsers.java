package cc.mrbird.febs.indexpage.entity;

import io.swagger.annotations.ApiModelProperty;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataUsers {

    private Long id;
    @ApiModelProperty(value = "用户名",notes = "1")
    private String userName;
    @ApiModelProperty(value = "头像",notes="2")
    private String avatar;
    private String workId;
}
