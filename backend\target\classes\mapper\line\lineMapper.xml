<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.line.dao.LineMapper">
<!--    <resultMap id="LineInfo" type="cc.mrbird.febs.line.entity.LineInfo">
        <result column="CPLH_PFN" jdbcType="VARCHAR" property="CPLH_PFN"/>
    </resultMap>
    <resultMap id="LineInfos" type="cc.mrbird.febs.line.entity.LineInfos">
        <result column="Discern" jdbcType="VARCHAR" property="sections"/>
        <result column="Production" jdbcType="VARCHAR" property="series"/>
        <result column="ProductionLine" jdbcType="VARCHAR" property="lines"/>
    </resultMap>-->
    <select id="queryProducitonLine" resultType="java.lang.String">
        use Schedule
        select distinct ProductionLine
        from T_Configure_Equipment
        where LineLeaderNumber=#{cardId}
        and Production=#{production}
    </select>

    <select id="queryDiscern" resultType="java.lang.String">
         use Schedule
        select distinct Discern
        from T_Configure_Equipment
        where LineLeaderNumber=#{cardId}
    </select>
    <select id="queryProduction" resultType="java.lang.String">
         use Schedule
        select distinct Production
        from T_Configure_Equipment
        where LineLeaderNumber=#{cardId}
        and Discern=#{discern}
    </select>
    <select id="queryName" resultType="java.lang.String">
        select USER_NAME
        from fit
        where USER_ID=#{cardId}
    </select>
    <select id="queryRoleId" resultType="java.lang.String">
        select ROLE_ID
        from t_user_role tr
        join t_user tu on tr.USER_ID=tu.USER_ID
        where tu.USERNAME=#{cardId}
    </select>
    <select id="queryDiscernBySection" resultType="java.lang.String">
         use Schedule
        select distinct Discern
        from T_Configure_Equipment
        where SectionManagerNumber=#{cardId}
    </select>
    <select id="queryDiscernByGroup" resultType="java.lang.String">
        use Schedule
        select distinct Discern
        from T_Configure_Equipment
        where GroupLeaderNumber=#{cardId}
    </select>
    <select id="queryProductionBySection" resultType="java.lang.String">
        use Schedule
        select distinct Production
        from T_Configure_Equipment
        where SectionManagerNumber=#{cardId}
        and Discern=#{discern}
    </select>
    <select id="queryProductionByGroup" resultType="java.lang.String">
         use Schedule
        select distinct Production
        from T_Configure_Equipment
        where GroupLeaderNumber=#{cardId}
        and Discern=#{discern}
    </select>
    <select id="queryProducitonLineBySection" resultType="java.lang.String">
         use Schedule
        select distinct ProductionLine
        from T_Configure_Equipment
        where SectionManagerNumber=#{cardId}
        and Production=#{production}
    </select>
    <select id="queryProducitonLineByGroup" resultType="java.lang.String">
        use Schedule
        select distinct ProductionLine
        from T_Configure_Equipment
        where GroupLeaderNumber=#{cardId}
        and Production=#{production}
    </select>

</mapper>
