package cc.mrbird.febs.indexpage.service.impl;

import cc.mrbird.febs.indexpage.dao.CountByAllMapper;
import cc.mrbird.febs.indexpage.dao.ProductionNumMapper;
import cc.mrbird.febs.indexpage.entity.*;
import cc.mrbird.febs.indexpage.entity.qo.SelectLineNumberQo;
import cc.mrbird.febs.indexpage.entity.vo.SheetNameDataVo;
import cc.mrbird.febs.indexpage.service.IndexPagerDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;


@Service
public class IndexPagerDataServiceImpl implements IndexPagerDataService {

    private final   ProductionNumMapper productionNumMapper;

    private final CountByAllMapper countByAllMapper;


    private static final LocalTime START_OF_DAY_SHIFT = LocalTime.of(8, 0);
    private static final LocalTime END_OF_DAY_SHIFT = LocalTime.of(19, 20);
    private static final LocalTime START_OF_NIGHT_SHIFT = LocalTime.of(19, 50);
    private static final LocalTime END_OF_NIGHT_SHIFT = LocalTime.of(7, 20);
    @Autowired
    public IndexPagerDataServiceImpl(CountByAllMapper countByAllMapper,ProductionNumMapper productionNumMapper) {
        this.countByAllMapper = countByAllMapper;
        this.productionNumMapper=productionNumMapper;
    }

    @Override
    public List<TDataByCountProductionNum> getProductionNum(ProductionNum productionNum) {
        List<TDataByCountProductionNum> list = productionNumMapper.selectProductionNum(productionNum);
        return list;
    }

    @Override
    public Map<String, String> getSubState(String workId) {
        String a = productionNumMapper.countByLine(workId);
        if (a.equals("0")) {
            a = "1";
        }
        String b = productionNumMapper.countByDay(workId);
        if (b.equals("0")) {
            b = "1";
        }
        String sum = "";
        if (a.equals("1") && b.equals("1")) {
            sum = "2";
        } else {
            sum = "0";
        }
        Map<String, String> map = new HashMap<>();
        map.put("Line", a);
        map.put("Day", b);
        map.put("sum", sum);
        return map;
    }

    @Override
    public int getQhState(String workId) {
        int num = productionNumMapper.countByBackwork(workId);
        return num;
    }

    @Override
    public int getBackNum(String workId) {
        int num = productionNumMapper.countBackNum(workId);
        return num;
    }

    @Override
    public List<PartNumberByIndex> getPartNumber(DateAndWorkIdQo andWorkIdQo) {
        List<PartNumberByIndex> list = productionNumMapper.getPartNumber(andWorkIdQo);
        return list;
    }


    //根据当前时间获取班别
    public String determineShift(LocalDateTime currentTime) {
        LocalTime localTime = currentTime.toLocalTime();
        if ((localTime.isAfter(START_OF_DAY_SHIFT) || localTime.equals(START_OF_DAY_SHIFT))
                && localTime.isBefore(END_OF_DAY_SHIFT)) {
            return "21:00";
        } else if ((localTime.isAfter(START_OF_NIGHT_SHIFT) || localTime.equals(START_OF_NIGHT_SHIFT))
                || localTime.isBefore(END_OF_NIGHT_SHIFT)) {
            return "8:00";
        } else {
            return "8:00";
        }
    }




    public  Map<String,SheetNameDataVo> getAllDataByOne(SelectLineNumberQo selectLineNumberQo) throws ParseException {
        //设置班别结束时间
        LocalDateTime dateTime =LocalDateTime.now();
        String lastSubTime =determineShift(dateTime);
        //设置前端指定日期加一天
        SimpleDateFormat simpleDateFormat =new SimpleDateFormat("yyyy-MM-dd");
        String date = selectLineNumberQo.getDate();
        Date date1 =simpleDateFormat.parse(date);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date1);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        //+1之后的Date
        Date newDate = calendar.getTime();
        selectLineNumberQo.setDate1(simpleDateFormat.format(newDate));
        //获取标准对比数据
        Integer standard=nullSafeInteger(countByAllMapper.selectLineNumberByOne(selectLineNumberQo)) ;
        Integer standard1=standard * 5;
        //获取生产日表表 提交数
        Integer day =nullSafeInteger(countByAllMapper.selectDayNumberByOne(selectLineNumberQo));
        //获取线长交接表 提交数
        Integer line =nullSafeInteger(countByAllMapper.selectLineSubByOne(selectLineNumberQo));
        //获取136比对表 提交数
        Integer one =nullSafeInteger(countByAllMapper.select136ByOne(selectLineNumberQo));
        double day1;
        double line1;
        double one1;
        //数据比对
        if (standard != 0) {
            DecimalFormat df = new DecimalFormat("#.00");
            day1 = Double.valueOf(df.format(((double)day / standard) * 100));
            line1 = Double.valueOf(df.format( ((double)line / standard) * 100));
            one1 = Double.valueOf(df.format( ((double)one / standard1) * 100));
        }else {
            day1 = 0.0;
            line1 = 0.0;
            one1 = 0.0;
        }
        SheetNameDataVo daysVo = new SheetNameDataVo(standard,day,day1,lastSubTime);
        SheetNameDataVo lineVo = new SheetNameDataVo(standard,line,line1,lastSubTime);
        SheetNameDataVo oneVo = new SheetNameDataVo(standard1,one,one1,lastSubTime);
        //封装数据
        Map<String,SheetNameDataVo> map =new HashMap<>();
        map.put("listByDay",daysVo);
        map.put("listByLine",lineVo);
        map.put("listBy136",oneVo);
        return map;
    }
    public  Map<String,SheetNameDataVo> getAllDataByAll(SelectLineNumberQo selectLineNumberQo) throws ParseException {
        //设置班别结束时间
        LocalDateTime dateTime =LocalDateTime.now();
        String lastSubTime =determineShift(dateTime);
        //设置前端指定日期加一天
        SimpleDateFormat simpleDateFormat =new SimpleDateFormat("yyyy-MM-dd");
        String date = selectLineNumberQo.getDate();
        Date date1 =simpleDateFormat.parse(date);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date1);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        //+1之后的Date
        Date newDate = calendar.getTime();
        selectLineNumberQo.setDate1(simpleDateFormat.format(newDate));
        //获取标准对比数据
        Integer standard=nullSafeInteger(countByAllMapper.selectLineNumberBySection(selectLineNumberQo)) ;
        Integer standard1=standard *5;
        //获取生产日表表 提交数
        Integer day =nullSafeInteger(countByAllMapper.selectDayNumberBySection(selectLineNumberQo));
        //获取线长交接表 提交数
        Integer line =nullSafeInteger(countByAllMapper.selectLineSubBySection(selectLineNumberQo));
        //获取136比对表 提交数
        Integer one =nullSafeInteger(countByAllMapper.select136BySection(selectLineNumberQo));
        double day1;
        double line1;
        double one1;
        //数据比对
        if (standard != 0) {
           /* day1 = ((double)day / standard) * 100 ;
            line1 = ((double)line / standard) * 100 ;
            one1 = ((double)one / standard1) * 100 ;*/
            DecimalFormat df = new DecimalFormat("#.00");
            day1 = Double.valueOf(df.format(((double)day / standard) * 100));
            line1 = Double.valueOf(df.format( ((double)line / standard) * 100));
            one1 = Double.valueOf(df.format( ((double)one / standard1) * 100));
        }else {
            day1 = 0.0;
            line1 = 0.0;
            one1 = 0.0;
        }
        SheetNameDataVo daysVo = new SheetNameDataVo(standard,day,day1,lastSubTime);
        SheetNameDataVo lineVo = new SheetNameDataVo(standard,line,line1,lastSubTime);
        SheetNameDataVo oneVo = new SheetNameDataVo(standard1,one,one1,lastSubTime);
        //封装数据
        Map<String,SheetNameDataVo> map =new HashMap<>();
        map.put("listByDay",daysVo);
        map.put("listByLine",lineVo);
        map.put("listBy136",oneVo);
        return map;
    }

    @Override
    public Map<String, SheetNameDataVo> selectAllDataByQo(SelectLineNumberQo selectLineNumberQo) throws ParseException {
        String cardId =selectLineNumberQo.getWorkId();
        String perms =countByAllMapper.selectPerms(cardId);
        Map<String, SheetNameDataVo> map;
        if (perms.equals("1")){
            String section= stringOfValue(selectLineNumberQo);
            selectLineNumberQo.setSection1(section);
            map = getAllDataByAll(selectLineNumberQo);
        }else {
            map = getAllDataByOne(selectLineNumberQo);
        }
        return map;
    }

    public void isNull(String lastSubTime, List<PageData> listByDay) {
        if(listByDay.isEmpty()){
            PageData tt = new PageData();
            tt.setConclude("0");
            tt.setTarget("0");
            tt.setReality("0");
            tt.setRecordDate(lastSubTime);
            listByDay.add(tt);
        }else {
            listByDay.forEach(tt->{
                tt.setRecordDate(lastSubTime);
            });
        }
    }

    public static List<SumProductivityData> getWeekData(Date dataTime) {
        /**
         * 转为calendar格式
         * calendar.get(Calendar.MONTH)+1  calendar中的月份以0开头
         * Calendar.DAY_OF_WEEK 当前日期是所在周的第几天（以周日为一周的第一天）
         * Calendar.DATE 当前日期是几号
         *  */
        List<SumProductivityData> week = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataTime);
        // 如果是周日
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            calendar.add(Calendar.DAY_OF_YEAR, -1);
        }
        // 获取当前日期是当周的第i天
        int i = calendar.get(Calendar.DAY_OF_WEEK) - 1;

        // 获取当前日期所在周的第一天
        calendar.add(Calendar.DATE, -i + 1);
        for (int j = 0; j < 6; j++) {
            SumProductivityData sumProductivityData = new SumProductivityData();
            sumProductivityData.setDays(new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime()));
            sumProductivityData.setNum(0);
            week.add(sumProductivityData);
            calendar.add(Calendar.DATE, 1);
        }
        return week;

    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
    private Integer nullSafeInteger(Integer value) {
        if (value == null) {
            return 0;
        } else {
            return value;
        }
    }

    private String stringOfValue (SelectLineNumberQo selectLineNumberQo){
        String pl =selectLineNumberQo.getPl();
        String discern = selectLineNumberQo.getSection();
        if (pl.equals("IDS1")&& discern.equals("裝配一課")){
            discern="A5一課";
        } else if (pl.equals("IDS1")&&discern.equals("裝配二課")) {
            discern="A5二課";
        }
        return discern;
    }



}
