package cc.mrbird.febs.securityInspection.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InspectionStationReport {

    private String building;
    private String floor;
    private String station;
    private List<InspectorInfo> xunchaPeople;

}