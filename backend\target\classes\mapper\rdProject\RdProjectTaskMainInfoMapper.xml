<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectTaskMainInfoMapper">

    <!-- 結果映射 -->
    <resultMap id="RdProjectTaskMainInfoResultMap" type="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo">
        <id column="task_id" property="taskId"/>
        <result column="parent_id" property="parentId"/>
        <result column="tree_path" property="treePath"/>
        <result column="site_area" property="siteArea"/>
        <result column="sbu" property="sbu"/>
        <result column="functions" property="functions"/>
        <result column="section" property="section"/>
        <result column="task_name" property="taskName"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="next_end_time" property="nextEndTime"/>
        <result column="status" property="status"/>
        <result column="creator_worker_id" property="creatorWorkerId"/>
    </resultMap>

    <!-- 插入任務主要信息 -->
    <insert id="insertRdProjectTaskMainInfo" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo" useGeneratedKeys="true" keyProperty="taskId">
        INSERT INTO rd_project_task_main_info (
            parent_id, tree_path, site_area, sbu, functions, section, 
            task_name, start_time, end_time, next_end_time, status, creator_worker_id
        )
        VALUES (
            #{parentId}, #{treePath}, #{siteArea}, #{sbu}, #{functions}, #{section},
            #{taskName}, #{startTime}, #{endTime}, #{nextEndTime}, #{status}, #{creatorWorkerId}
        )
    </insert>

    <!-- 根據任務ID刪除任務主要信息 -->
    <delete id="deleteRdProjectTaskMainInfoById">
        DELETE FROM rd_project_task_main_info WHERE task_id = #{taskId}
    </delete>

    <!-- 根據父任務ID刪除子任務 -->
    <delete id="deleteRdProjectTaskMainInfoByParentId">
        DELETE FROM rd_project_task_main_info WHERE parent_id = #{parentId}
    </delete>

    <!-- 更新任務主要信息 -->
    <update id="updateRdProjectTaskMainInfo" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo">
        UPDATE rd_project_task_main_info
        SET parent_id = #{parentId},
            tree_path = #{treePath},
            site_area = #{siteArea},
            sbu = #{sbu},
            functions = #{functions},
            section = #{section},
            task_name = #{taskName},
            start_time = #{startTime},
            end_time = #{endTime},
            next_end_time = #{nextEndTime},
            status = #{status},
            creator_worker_id = #{creatorWorkerId}
        WHERE task_id = #{taskId}
    </update>

    <!-- 根據任務ID查詢任務主要信息 -->
    <select id="selectRdProjectTaskMainInfoById" resultMap="RdProjectTaskMainInfoResultMap">
        SELECT task_id, parent_id, tree_path, site_area, sbu, functions, section,
               task_name, start_time, end_time, next_end_time, status, creator_worker_id
        FROM rd_project_task_main_info
        WHERE task_id = #{taskId}
    </select>

    <!-- 根據父任務ID查詢子任務列表 -->
    <select id="selectRdProjectTaskMainInfoByParentId" resultMap="RdProjectTaskMainInfoResultMap">
        SELECT task_id, parent_id, tree_path, site_area, sbu, functions, section,
               task_name, start_time, end_time, next_end_time, status, creator_worker_id
        FROM rd_project_task_main_info
        WHERE parent_id = #{parentId}
    </select>

    <!-- 根據創建人工號查詢任務列表 -->
    <select id="selectRdProjectTaskMainInfoByCreatorWorkerId" resultMap="RdProjectTaskMainInfoResultMap">
        SELECT task_id, parent_id, tree_path, site_area, sbu, functions, section,
               task_name, start_time, end_time, next_end_time, status, creator_worker_id
        FROM rd_project_task_main_info
        WHERE creator_worker_id = #{creatorWorkerId}
    </select>

    <!-- 根據狀態查詢任務列表 -->
    <select id="selectRdProjectTaskMainInfoByStatus" resultMap="RdProjectTaskMainInfoResultMap">
        SELECT task_id, parent_id, tree_path, site_area, sbu, functions, section,
               task_name, start_time, end_time, next_end_time, status, creator_worker_id
        FROM rd_project_task_main_info
        WHERE status = #{status}
    </select>

    <!-- 根據樹路徑查詢任務 -->
    <select id="selectRdProjectTaskMainInfoByTreePath" resultMap="RdProjectTaskMainInfoResultMap">
        SELECT task_id, parent_id, tree_path, site_area, sbu, functions, section,
               task_name, start_time, end_time, next_end_time, status, creator_worker_id
        FROM rd_project_task_main_info
        WHERE tree_path = #{treePath}
    </select>

    <!-- 查詢所有任務主要信息 -->
    <select id="selectAllRdProjectTaskMainInfo" resultMap="RdProjectTaskMainInfoResultMap">
        SELECT task_id, parent_id, tree_path, site_area, sbu, functions, section,
               task_name, start_time, end_time, next_end_time, status, creator_worker_id
        FROM rd_project_task_main_info
    </select>

</mapper>
