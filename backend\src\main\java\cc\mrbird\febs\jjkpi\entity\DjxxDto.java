package cc.mrbird.febs.jjkpi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DjxxDto implements Serializable {
    @ApiModelProperty(value = "計劃產量")
    @TableField("")
    private BigDecimal SCSL_QOP;
    @ApiModelProperty(value = "真實產量")
    private BigDecimal SCSL_QOP_REAL;
    @ApiModelProperty(value = "成品料號")
    private String CPLH_PFN;
    @ApiModelProperty(value = "客戶名稱")
    private String KHMC_CN;
    @ApiModelProperty(value = "客戶料號")
    private String KHLH_CMN;
    @ApiModelProperty(value = "工單")
    private String GLBH_WON;
    @ApiModelProperty(value = "dc")
    private String DC_SCDC;
    @ApiModelProperty(value = "入庫單號")
    private String RUKUDANHAO;
    @ApiModelProperty(value = "備注")
    private String REMARKS;
}
