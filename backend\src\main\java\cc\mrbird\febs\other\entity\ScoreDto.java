package cc.mrbird.febs.other.entity;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScoreDto {
    @ApiModelProperty(value = "赛事名称")
    private String Name;
    @ApiModelProperty(value = "赛事描述")
    private String Describe;
    @ApiModelProperty(value = "主办单位")
    private String Organizer;
    @ApiModelProperty(value = "设置的评委账号")
    private String Judge;
    @ApiModelProperty(value = "开启时间")
    private String Time;
    @ApiModelProperty(value = "创建人工号")
    private String UserID;
    @ApiModelProperty(value = "创建时间")
    private String Date;
    @ApiModelProperty(value = "序号，先后排名")
    private String Number;
    @ApiModelProperty(value = "选手项目")
    private String Project;
//    @ApiModelProperty(value = "平均分")
//    private String PJF_Ave1;
//    @ApiModelProperty(value = "去掉最高分、最低分的平均分")
//    private String PJF_Ave2;
  //  @ApiModelProperty(value = "选手工号")
//    private String Ins_UserIDByXS;
//    @ApiModelProperty(value = "显示姓名")
//    private String Ins_NameByXS;
    @ApiModelProperty(value = "选手分数分数")
    private String fraction;
    @ApiModelProperty(value = "评委打分")
    private String fractionByPW;
    @ApiModelProperty(value = "总分(去掉最高分和最低分)")
    private Integer AdjustedAverageFraction;
}
