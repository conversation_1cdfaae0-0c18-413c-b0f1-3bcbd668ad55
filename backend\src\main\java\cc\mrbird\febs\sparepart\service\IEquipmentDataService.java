package cc.mrbird.febs.sparepart.service;

import cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureYieldOperation;
import cc.mrbird.febs.sparepart.entity.qo.SparePartAndEquipmentQo;
import cc.mrbird.febs.sparepart.entity.qo.UpdateSparePartQo;

import java.util.List;

public interface IEquipmentDataService {

    void scheduleUpdate();

    List<TConfigureEquipmentAndYield> querySparePartDataByView(SparePartAndEquipmentQo sparePartAndEquipmentQo);

    int updateSparePart(UpdateSparePartQo updateSparePartQo);

    List<TConfigureYieldOperation> queryYieldOperation(Integer id);

    TConfigureEquipmentAndYield selectTConfigureEquipmentAndYield(Integer id);

}
