package cc.mrbird.febs.produceInspection.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/19
 * @Time: 15:08
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionTemplateDto {
    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;

    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;

    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;

    @ApiModelProperty(value = "系列", example = "1700SKT", position = 4)
    private String series;

    @ApiModelProperty(value = "綫體", example = "V1", position = 5)
    private String line;

    @ApiModelProperty(value = "模板類型", example = "1", position = 6)
    private int type;
}
