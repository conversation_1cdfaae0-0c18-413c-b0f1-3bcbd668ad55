package cc.mrbird.febs.customerCondition.service.impl;

import cc.mrbird.febs.customerCondition.dao.CustomerConditionMapper;
import cc.mrbird.febs.customerCondition.entity.*;
import cc.mrbird.febs.customerCondition.entity.dto.CustomerConditionDto;
import cc.mrbird.febs.customerCondition.param.CustomerConditionQueryParam;
import cc.mrbird.febs.customerCondition.param.CustomerConditionUserQueryParam;
import cc.mrbird.febs.customerCondition.service.CustomerConditionService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄服務實現類
 */
@Service
public class CustomerConditionServiceImpl implements CustomerConditionService {

    private final CustomerConditionMapper mapper;

    @Value("${customer.condition.file.upload.path}")
    private String uploadPath;

    @Value("${customer.condition.file.access.path}")
    private String accessPath;

    @Value("${customer.condition.file.server.url}")
    private String serverUrl;

    @Value("${customer.condition.file.summary.path}")
    private String summaryPath;

    @Autowired
    public CustomerConditionServiceImpl(CustomerConditionMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<CustomerConditionCustomerData> queryCustomerList() {
        return mapper.selectCustomerList();
    }

    @Override
    public CustomerConditionCustomerData queryCustomerById(Integer customerId) {
        return mapper.selectCustomerById(customerId);
    }

    @Override
    public List<String> querySiteAreaList() {
        return mapper.selectSiteAreaList();
    }

    @Override
    public List<String> querySbuBySiteArea(String siteArea) {
        return mapper.selectSbuBySiteArea(siteArea);
    }

    @Override
    public List<String> queryFunctionsBySiteAreaAndSbu(String siteArea, String sbu) {
        return mapper.selectFunctionsBySiteAreaAndSbu(siteArea, sbu);
    }

    @Override
    public List<String> querySectionBySiteAreaAndSbuAndFunctions(String siteArea, String sbu, String functions) {
        return mapper.selectSectionBySiteAreaAndSbuAndFunctions(siteArea, sbu, functions);
    }

    @Override
    public List<CustomerConditionMainInfo> queryCustomerConditionMainInfo(CustomerConditionQueryParam queryParam) {
        return mapper.selectCustomerConditionMainInfo(queryParam);
    }

    @Override
    public List<CustomerConditionDto> queryCustomerConditionDto(CustomerConditionQueryParam queryParam) {
        return mapper.selectCustomerConditionDto(queryParam);
    }

    @Override
    public CustomerConditionFile queryCustomerConditionFileById(Integer fileId) {
        return mapper.selectCustomerConditionFileById(fileId);
    }

    @Override
    public List<CustomerConditionUser> queryCustomerConditionUsers() {
        return mapper.selectCustomerConditionUsers();
    }

    @Override
    public List<CustomerConditionMail> queryCustomerConditionMails(Integer customerId) {
        return mapper.selectCustomerConditionMails(customerId);
    }

    @Override
    @DSTransactional
    public CustomerConditionMainInfo uploadCustomerConditionFile(MultipartFile file, CustomerConditionMainInfo mainInfo) throws IOException {
        // 1. 保存文件到服務器
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String newFileName = UUID.randomUUID().toString() + fileExtension;

        // 獲取當前日期，用於創建年月文件夾
        Date uploadDate = new Date();
        java.text.SimpleDateFormat yearFormat = new java.text.SimpleDateFormat("yyyy");
        java.text.SimpleDateFormat monthFormat = new java.text.SimpleDateFormat("MM");
        String year = yearFormat.format(uploadDate);
        String month = monthFormat.format(uploadDate);

        // 構建文件保存路徑：基本路徑/年/月
        String yearMonthPath = uploadPath + File.separator + year + File.separator + month;

        // 確保目錄存在
        File uploadDir = new File(yearMonthPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 保存文件
        File destFile = new File(yearMonthPath + File.separator + newFileName);
        file.transferTo(destFile);

        // 2. 保存文件記錄
        CustomerConditionFile fileEntity = new CustomerConditionFile();
        fileEntity.setCustomerConditionFileName(originalFilename);
        // 更新文件URL，使用可直接在瀏覽器訪問的格式
        // 使用控制器的訪問路徑，而不是文件系統路徑
        // 完整URL格式：http://10.196.5.230:220/customerCondition/customer-condition-upload/年/月/[filename].xlsx
        fileEntity.setCustomerConditionFileUrl(serverUrl + "/customerCondition/customer-condition-upload/" + year + "/" + month + "/" + newFileName);
//        fileEntity.setUploadDate(new Date());
        mapper.insertCustomerConditionFile(fileEntity);

        // 3. 保存主要信息
        mainInfo.setCustomerConditionFileId(fileEntity.getCustomerConditionFileId());
        mainInfo.setCustomerConditionFileName(originalFilename);
//        mainInfo.setUploadDate(uploadDate);

        // 計算上傳次數
        int uploadCount = mapper.selectCustomerConditionUploadCount(
                mainInfo.getSiteArea(),
                mainInfo.getSbu(),
                mainInfo.getFunctions(),
                mainInfo.getSection(),
                mainInfo.getCustomerId()) + 1;

        // 設置版本號為 "V上傳日期-上傳次數"
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
        String versionNumber = "V" + sdf.format(uploadDate) + "-" + uploadCount;
        mainInfo.setVersion(versionNumber);

        mapper.insertCustomerConditionMainInfo(mainInfo);

        return mainInfo;
    }

    @Override
    public CustomerConditionDto queryCustomerConditionDetail(Integer mainInfoId) {
        return mapper.selectCustomerConditionDetail(mainInfoId);
    }

    @Override
    public List<CustomerConditionUser> queryCustomerConditionUsersByCondition(CustomerConditionUserQueryParam queryParam) {
        return mapper.selectCustomerConditionUsersByCondition(queryParam);
    }

    @Override
    public int queryCustomerConditionUploadCount(String siteArea, String sbu, String functions, String section, Integer customerId) {
        return mapper.selectCustomerConditionUploadCount(siteArea, sbu, functions, section, customerId);
    }

    @Override
    public void accessFileById(Integer fileId, HttpServletResponse response) throws IOException {
        // 1. 根據文件ID查詢文件信息
        CustomerConditionFile fileInfo = mapper.selectCustomerConditionFileById(fileId);
        if (fileInfo == null) {
            throw new IOException("文件不存在");
        }

        // 2. 獲取文件URL並解析出路徑
        String fileUrl = fileInfo.getCustomerConditionFileUrl();
        // 移除路徑前綴，例如 http://10.196.5.230:9527/customerCondition/customer-condition-upload/
        String relativePath = fileUrl.substring((serverUrl + "/customerCondition/customer-condition-upload/").length());

        // 3. 構建文件的完整路徑
        String filePath = uploadPath + File.separator + relativePath.replace("/", File.separator);
        File file = new File(filePath);

        if (!file.exists()) {
            throw new IOException("文件不存在於服務器上: " + filePath);
        }

        // 4. 設置文件類型和響應頭，然後發送文件
        String originalFileName = fileInfo.getCustomerConditionFileName();
        sendFileToResponse(file, originalFileName, response,true);
    }

    @Override
    public void accessFileByName(String fileName, HttpServletResponse response) throws IOException {
        // 1. 先嘗試在最新的年月目錄下尋找文件
        Date currentDate = new Date();
        java.text.SimpleDateFormat yearFormat = new java.text.SimpleDateFormat("yyyy");
        java.text.SimpleDateFormat monthFormat = new java.text.SimpleDateFormat("MM");
        String year = yearFormat.format(currentDate);
        String month = monthFormat.format(currentDate);

        // 嘗試使用年月方法訪問
        try {
            accessFileByYearMonthAndName(year, month, fileName, response);
            return; // 如果成功找到文件，直接返回
        } catch (IOException e) {
            // 如果在當前年月目錄下找不到，繼續嘗試其他方法
        }

        // 2. 嘗試在根目錄下尋找
        String rootPath = uploadPath + File.separator + fileName;
        File file = new File(rootPath);
        if (file.exists()) {
            // 設置文件類型和響應頭，然後發送文件
            sendFileToResponse(file, fileName, response,false);
            return;
        }

        // 3. 如果根目錄也找不到，嘗試遍歷所有年月目錄
        File baseDir = new File(uploadPath);
        if (baseDir.exists() && baseDir.isDirectory()) {
            // 遍歷年份目錄
            File[] yearDirs = baseDir.listFiles(File::isDirectory);
            if (yearDirs != null) {
                for (File yearDir : yearDirs) {
                    // 遍歷月份目錄
                    File[] monthDirs = yearDir.listFiles(File::isDirectory);
                    if (monthDirs != null) {
                        for (File monthDir : monthDirs) {
                            File targetFile = new File(monthDir, fileName);
                            if (targetFile.exists()) {
                                // 設置文件類型和響應頭，然後發送文件
                                sendFileToResponse(targetFile, fileName, response,false);
                                return;
                            }
                        }
                    }
                }
            }
        }

        // 如果所有方法都找不到文件，拋出異常
        throw new IOException("文件不存在於服務器上: " + fileName);
    }

    @Override
    public void accessFileByYearMonthAndName(String year, String month, String fileName, HttpServletResponse response) throws IOException {
        // 構建年月路徑下的文件路徑
        String yearMonthPath = uploadPath + File.separator + year + File.separator + month + File.separator + fileName;
        File file = new File(yearMonthPath);

        if (!file.exists()) {
            throw new IOException("文件不存在於服務器上: " + yearMonthPath);
        }

        // 設置文件類型和響應頭，然後發送文件
        sendFileToResponse(file, fileName, response,false);
    }

    /**
     * 將文件發送到HTTP響應
     * @param file 文件對象
     * @param fileName 文件名
     * @param response HTTP響應對象
     * @throws IOException 如果文件訪問失敗
     */
    private void sendFileToResponse(File file, String fileName, HttpServletResponse response ,boolean isDownload) throws IOException {
        // 設置響應頭
        response.reset();
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");

        // 判斷文件類型，設置相應的Content-Type
        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

        // 根據文件類型設置相應的Content-Type
        if (fileExtension.equals("xlsx") || fileExtension.equals("xls")) {
            if (fileExtension.equals("xlsx")) {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } else {
                response.setContentType("application/vnd.ms-excel");
            }
        } else {
            // 非Excel文件使用通用的二進制流
            response.setContentType("application/octet-stream");
        }

        // 設置文件名，處理中文文件名
        String encodedFileName = new String(fileName.getBytes("utf-8"), "ISO8859-1");

        // 如果是Excel文件，設置為在瀏覽器中直接打開
        if (!isDownload) {
            // 對於非下载的文件，使用inline而非attachment，使瀏覽器直接打開
            response.addHeader("Content-Disposition", "inline;filename=\"" + encodedFileName + "\"");
        } else {
            // 對於下载的文件件，仍然使用attachment強制下載
            response.addHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        }

        response.addHeader("content-Length", String.valueOf(file.length()));

        // 讀取文件並寫入響應
        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream is = new BufferedInputStream(fis);
             OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {

            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException e) {
            throw new IOException("文件訪問失敗: " + e.getMessage());
        }
    }

    @Override
    @DSTransactional
    public CustomerConditionSummaryFile uploadCustomerConditionSummaryFile(MultipartFile file) throws IOException {
        // 1. 保存文件到服務器
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String newFileName = UUID.randomUUID().toString() + fileExtension;

        // 構建文件保存路徑：使用配置文件中的summary路徑

        // 確保目錄存在
        File uploadDir = new File(summaryPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 保存文件
        File destFile = new File(summaryPath + File.separator + newFileName);
        file.transferTo(destFile);

        // 2. 保存summary文件記錄
        CustomerConditionSummaryFile summaryFile = new CustomerConditionSummaryFile();
        summaryFile.setSummaryFileName(newFileName); // 保存新的文件名而不是原始文件名
        // 在数据库中使用当前时间
        mapper.insertCustomerConditionSummaryFile(summaryFile);

        return summaryFile;
    }

    @Override
    public void downloadLatestCustomerConditionSummaryFile(HttpServletResponse response) throws IOException {
        // 1. 查詢最新的summary文件記錄
        CustomerConditionSummaryFile summaryFile = mapper.selectLatestCustomerConditionSummaryFile();
        if (summaryFile == null) {
            throw new IOException("沒有找到summary文件");
        }

        // 2. 在固定的summary目錄下尋找文件
        String fileName = summaryFile.getSummaryFileName();
        String filePath = summaryPath + File.separator + fileName;
        File file = new File(filePath);

        if (!file.exists()) {
            throw new IOException("文件不存在於服務器上: " + fileName);
        }

        // 3. 發送文件到響應
        // 由於我們在数据库中只存储了新文件名，所以這裡直接使用它
        sendFileToResponse(file, fileName, response,true);
    }
}
