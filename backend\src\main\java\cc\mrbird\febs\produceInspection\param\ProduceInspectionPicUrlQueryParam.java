package cc.mrbird.febs.produceInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表图片信息查询参数
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionPicUrlQueryParam {

    @ApiModelProperty(value = "主表ID", example = "1", position = 1)
    private Integer mainInfoId;

    @ApiModelProperty(value = "圖片URL", example = "http://example.com/image.jpg", position = 2)
    private String picUrl;

    @ApiModelProperty(value = "圖片名稱", example = "點檢圖片1", position = 3)
    private String picName;

    @ApiModelProperty(value = "圖片順序", example = "1", position = 4)
    private String picOrder;
}