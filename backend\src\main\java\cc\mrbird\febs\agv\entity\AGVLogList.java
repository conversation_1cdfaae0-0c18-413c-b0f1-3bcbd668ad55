package cc.mrbird.febs.agv.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AGVLogList {
    @ApiModelProperty(value = "日志ID", position = 0)
    private Integer id;
    @ApiModelProperty(value = "机器人ID", position = 1)
    private String robotId;
    @ApiModelProperty(value = "错误代码", position = 2)
    private String errorCode;
    @ApiModelProperty(value = "日志内容", position = 3)
    private String logText;
    @ApiModelProperty(value = "工作ID", position = 4)
    private String jlrWorkId;
    @ApiModelProperty(value = "记录人姓名", position = 5)
    private String jlrName;
    @ApiModelProperty(value = "开始日期", position = 6)
    private String startDate;
}