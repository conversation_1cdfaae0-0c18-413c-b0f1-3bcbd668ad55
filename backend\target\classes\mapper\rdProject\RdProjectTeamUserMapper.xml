<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.rdProject.dao.RdProjectTeamUserMapper">

    <!-- 結果映射 -->
    <resultMap id="RdProjectTeamUserResultMap" type="cc.mrbird.febs.rdProject.entity.RdProjectTeamUser">
        <id column="worker_id" property="workerId"/>
        <id column="team_id" property="teamId"/>
    </resultMap>

    <!-- 插入團隊用戶映射關係 -->
    <insert id="insertRdProjectTeamUser" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTeamUser">
        INSERT INTO rd_projuct_team_user (worker_id, team_id)
        VALUES (#{workerId}, #{teamId})
    </insert>

    <!-- 根據工號和團隊ID刪除團隊用戶映射關係 -->
    <delete id="deleteRdProjectTeamUser">
        DELETE FROM rd_projuct_team_user 
        WHERE worker_id = #{workerId} AND team_id = #{teamId}
    </delete>

    <!-- 根據工號刪除所有團隊用戶映射關係 -->
    <delete id="deleteRdProjectTeamUserByWorkerId">
        DELETE FROM rd_projuct_team_user WHERE worker_id = #{workerId}
    </delete>

    <!-- 根據團隊ID刪除所有團隊用戶映射關係 -->
    <delete id="deleteRdProjectTeamUserByTeamId">
        DELETE FROM rd_projuct_team_user WHERE team_id = #{teamId}
    </delete>

    <!-- 更新團隊用戶映射關係（由於只有主鍵字段，實際上沒有可更新的內容，這裡作為佔位符） -->
    <update id="updateRdProjectTeamUser" parameterType="cc.mrbird.febs.rdProject.entity.RdProjectTeamUser">
        <!-- 由於該表只有複合主鍵，沒有其他字段可以更新，此方法主要用於存在性檢查 -->
        UPDATE rd_projuct_team_user
        SET worker_id = #{workerId}
        WHERE worker_id = #{workerId} AND team_id = #{teamId}
    </update>

    <!-- 根據工號和團隊ID查詢團隊用戶映射關係 -->
    <select id="selectRdProjectTeamUser" resultMap="RdProjectTeamUserResultMap">
        SELECT worker_id, team_id
        FROM rd_projuct_team_user
        WHERE worker_id = #{workerId} AND team_id = #{teamId}
    </select>

    <!-- 根據工號查詢該用戶所屬的所有團隊 -->
    <select id="selectRdProjectTeamUserByWorkerId" resultMap="RdProjectTeamUserResultMap">
        SELECT worker_id, team_id
        FROM rd_projuct_team_user
        WHERE worker_id = #{workerId}
    </select>

    <!-- 根據團隊ID查詢該團隊的所有用戶 -->
    <select id="selectRdProjectTeamUserByTeamId" resultMap="RdProjectTeamUserResultMap">
        SELECT worker_id, team_id
        FROM rd_projuct_team_user
        WHERE team_id = #{teamId}
    </select>

    <!-- 查詢所有團隊用戶映射關係 -->
    <select id="selectAllRdProjectTeamUser" resultMap="RdProjectTeamUserResultMap">
        SELECT worker_id, team_id
        FROM rd_projuct_team_user
    </select>

</mapper>
