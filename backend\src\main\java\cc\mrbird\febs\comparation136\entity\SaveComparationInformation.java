package cc.mrbird.febs.comparation136.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.datetime.DateFormatter;

import java.io.Serializable;
import java.text.DateFormat;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveComparationInformation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    //产品处
    @ApiModelProperty(value = "產品處")
    private String SBU;

    //机能
    @ApiModelProperty(value = "机能")
    private String functions;

    //課別
    @ApiModelProperty(value = "課別")
    private String section;

    //線體
    @ApiModelProperty(value = "線體")
    private String line;

    //料號
    @ApiModelProperty(value = "料號")
    private String CPLH_PFN;

    //對比日期
    @ApiModelProperty(value = "對比日期")
    private String BD_Date;

    //班別
    @ApiModelProperty(value = "班別")
    private String ProductClass;

    //對比項目1-10
    @ApiModelProperty(value = "對比項目1")
    private String BDXM_01;

    private String BDXM_02;

    private String BDXM_03;

    private String BDXM_04;

    private String BDXM_05;

    private String BDXM_06;

    private String BDXM_07;

    private String BDXM_08;

    private String BDXM_09;

    private String BDXM_10;

    //是否ng
    @ApiModelProperty(value = "是否ng")
    private String SF_NG;

    //NG原因
    @ApiModelProperty(value = "NG原因")
    private String NG_Cause;

    //創建人名
    @ApiModelProperty(value = "創建人名")
    private String JLR_Name;

    //IPQC簽核工號
    @ApiModelProperty(value = "IPQC簽核工號")
    private String IPQC_QHRWorkId;

    //IPQC名字
    @ApiModelProperty(value = "IPQC名字")
    private String IPQC_QHRName;

    //組長簽核工號
    @ApiModelProperty(value = "組長簽核工號")
    private String ZZ_QHRWorkId;

    //簽核組長名字
    @ApiModelProperty(value = "簽核組長名字")
    private String ZZ_QHRName;

    //創建詳細時間
    @ApiModelProperty(value = "創建詳細時間")
    private String Ins_Date;

    //登錄人工號
    @ApiModelProperty(value = "登錄人工號")
    private String Ins_UserId;

    //登錄人名
    @ApiModelProperty(value = "登錄人名")
    private String Ins_Name;


}
