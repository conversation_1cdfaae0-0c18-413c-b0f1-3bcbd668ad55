package cc.mrbird.febs.comparation136.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageInformation implements Serializable {

    private Long id;

    //圖片表關聯主表信息
    private Long MainInfoID;

    //圖片url
    private String PicUrl;

    //圖片名字
    private String PicName;

    //圖片位置
    private Integer PicOrder;

    private String adress;
}
