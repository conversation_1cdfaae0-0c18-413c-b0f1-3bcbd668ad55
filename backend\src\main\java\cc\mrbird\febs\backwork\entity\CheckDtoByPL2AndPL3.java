package cc.mrbird.febs.backwork.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckDtoByPL2AndPL3 {
    private String name;
    private String uuid;
    private String unit;
    @JsonIgnore
    private Integer workId;
    @ApiModelProperty(value = "產品費用")
    private double laborCost;           //产品费用
    @ApiModelProperty(value = "人工费用")
    private double processCost;        //人工费用
    @ApiModelProperty(value = "总费用")
    private double cancellationCost;  //总费用
    private String showId;
    private String pl;
    @ApiModelProperty(value = "抽樣方法")
    private String sMethod;
    @ApiModelProperty(value = "檢驗項目")
    private String insItem;
    @ApiModelProperty(value = "合格數")
    private String aNumber;
    @ApiModelProperty(value = "不合格数")
    private String naNumber;
    @ApiModelProperty(value = "不合格處理方式說明")
    private String naTalk;
    @ApiModelProperty(value = "重工类型:1是客诉类0是其他")
    private String isOther;
}
