package cc.mrbird.febs.rdProject.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.common.domain.Tree;
import cc.mrbird.febs.common.exception.LimitAccessException;
import cc.mrbird.febs.common.exception.code.Code;
import cc.mrbird.febs.rdProject.entity.RdProjectHistory;
import cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo;
import cc.mrbird.febs.rdProject.entity.RdProjectUserExtra;
import cc.mrbird.febs.rdProject.service.RdProjectHistoryService;
import cc.mrbird.febs.rdProject.service.RdProjectTaskService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理控制器
 */
@RestController
@RequestMapping("/rdProjectTask")
@Api(tags = "研究項目協同管理")
public class RdProjectTaskController {

    @Autowired
    private RdProjectTaskService rdProjectTaskService;
    @Autowired
    private RdProjectHistoryService rdProjectHistoryService;

    // 查詢歷史動態
    @PostMapping("/queryHistory")
    @ApiOperation("/查詢歷史動態")
    public FebsResponse queryHistory(@RequestBody RdProjectHistory rdProjectHistory,
                                     @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                     @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        try {
            PageInfo<RdProjectHistory> rdProjectHistoryPageInfo = rdProjectHistoryService.selectHistory(rdProjectHistory,pageNum,pageSize);
            return new FebsResponse().code("200").data(rdProjectHistoryPageInfo);
        } catch (Exception e) {
            return new FebsResponse().code(Code.C500.getCode().toString()).message(Code.C500.getDesc());
        }
    }

    // 查詢登录用户本人的廠區、產品処、機能和課別
    @GetMapping("/queryByUserId")
    @ApiOperation("/查詢登录用户本人的廠區、產品処、機能和課別")
    public FebsResponse queryUserExtra() {
        try {
            RdProjectUserExtra rdProjectUserExtra;
            rdProjectUserExtra = rdProjectTaskService.queryUserExtra();
            if(rdProjectUserExtra==null)
                return new FebsResponse().code(Code.UserInformationGetFailed.getCode().toString())
                        .message("User's workerId not found");
            return new FebsResponse().code("200").data(rdProjectUserExtra);
        } catch (Exception e) {
            return new FebsResponse().code(Code.C500.getCode().toString()).message(Code.C500.getDesc());
        }
    }

    @GetMapping("/querySiteAreaList")
    @ApiOperation("查詢廠區列表")
    public FebsResponse querySiteAreaList() {
        List<String> list;
        try {
            list = rdProjectTaskService.querySiteAreaList();
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢廠區列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/querySbuBySiteArea")
    @ApiOperation("根據廠區查詢產品処列表")
    @ApiImplicitParam(name = "siteArea", value = "廠區", required = true, example = "寳科園區")
    public FebsResponse querySbuBySiteArea(@RequestParam("siteArea") String siteArea) {
        List<String> list;
        try {
            list = rdProjectTaskService.querySbuBySiteArea(siteArea);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢產品処列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/queryFunctionsBySiteAreaAndSbu")
    @ApiOperation("根據廠區和產品処查詢機能列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteArea", value = "廠區", required = true, example = "寳科園區"),
            @ApiImplicitParam(name = "sbu", value = "產品処", required = true, example = "IDS1")
    })
    public FebsResponse queryFunctionsBySiteAreaAndSbu(
            @RequestParam("siteArea") String siteArea,
            @RequestParam("sbu") String sbu) {
        List<String> list;
        try {
            list = rdProjectTaskService.queryFunctionsBySiteAreaAndSbu(siteArea, sbu);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢機能列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/querySectionBySiteAreaAndSbuAndFunctions")
    @ApiOperation("根據廠區、產品処和機能查詢課別列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteArea", value = "廠區", required = true, example = "寳科園區"),
            @ApiImplicitParam(name = "sbu", value = "產品処", required = true, example = "IDS1"),
            @ApiImplicitParam(name = "functions", value = "機能", required = true, example = "裝配")
    })
    public FebsResponse querySectionBySiteAreaAndSbuAndFunctions(
            @RequestParam("siteArea") String siteArea,
            @RequestParam("sbu") String sbu,
            @RequestParam("functions") String functions) {
        List<String> list;
        try {
            list = rdProjectTaskService.querySectionBySiteAreaAndSbuAndFunctions(siteArea, sbu, functions);
        } catch (Exception e) {
            return new FebsResponse().code("500").message("查詢課別列表失敗：" + e.getMessage());
        }
        return new FebsResponse().code("200").data(list);
    }

    // 查詢研究項目協同管理主要信息
    @ApiOperation(value = "查詢研究項目協同管理主要信息", httpMethod = "POST")
    @PostMapping("/queryRdProjectTaskMainInfo")
    public FebsResponse queryRdProjectTaskMainInfo(
            @RequestBody RdProjectTaskMainInfo rdProjectTaskMainInfo,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        try {
            PageInfo<Tree<RdProjectTaskMainInfo>> pageInfo = rdProjectTaskService.queryRdProjectTaskMainInfo(rdProjectTaskMainInfo,pageNum,pageSize);
            return new FebsResponse().code("200").data(pageInfo);
        } catch (LimitAccessException e){
            return new FebsResponse().code("400").message(e.getMessage());
        }
        catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數：" + rdProjectTaskMainInfo);
        }

    }

    @PostMapping(value = "/save-tree", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public FebsResponse saveTaskTree(
            @RequestPart("treeData") String treeJson,  // JSON字符串
            @RequestParam Map<String, MultipartFile> fileMap // 动态文件映射
    ){
        try {
            rdProjectTaskService.saveTaskTree(treeJson,fileMap);

            return new FebsResponse().code("200").message("創建任務成功！");
        } catch (JsonProcessingException e) {
            return new FebsResponse().code("400").message("JSON解析失败：" + e.getMessage());
        } catch (IOException e) {
            return new FebsResponse().code("400").message("文件处理异常：" + e.getMessage());
        }catch (Exception e) {
            return new FebsResponse().code("500").message("錯誤參數！" );
        }
    }


}
