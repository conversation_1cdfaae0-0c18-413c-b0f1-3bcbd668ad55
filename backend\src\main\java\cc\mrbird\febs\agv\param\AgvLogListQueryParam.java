package cc.mrbird.febs.agv.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Author: 李囯斌
 * @Date: 2025/2/25
 * @Time: 11:32
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class AgvLogListQueryParam {
    @ApiModelProperty(value = "机器人ID",example = "1", position = 1)
    private String robotId;
    @ApiModelProperty(value = "错误代码",example = "-1", position = 2)
    private String errorCode;
    @ApiModelProperty(value = "记录人工作ID",example = "F0842364", position = 4)
    private String jlrWorkId;
    @ApiModelProperty(value = "记录人姓名",example = "李曙波", position = 5)
    private String jlrName;
    @ApiModelProperty(value = "开始日期",example = "2025-01-07", position = 6)
    private String startDate;

}
