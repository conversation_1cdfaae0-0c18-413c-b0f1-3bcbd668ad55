package cc.mrbird.febs.customerCondition.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄用戶查詢參數
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionUserQueryParam {
    
    @ApiModelProperty(value = "客戶記錄條件用戶ID", example = "1", position = 0)
    private Integer customerConditionUploadUserId;
    
    @ApiModelProperty(value = "客戶記錄條件用戶名", example = "張三", position = 1)
    private String customerConditionUploadUserName;
    
    @ApiModelProperty(value = "產品処", example = "IDS1", position = 2)
    private String sbu;
    
    @ApiModelProperty(value = "機能", example = "裝配", position = 3)
    private String functions;
    
    @ApiModelProperty(value = "廠區", example = "寳科園區", position = 4)
    private String siteArea;
    
    @ApiModelProperty(value = "課別", example = "裝配一課", position = 5)
    private String section;
    
    @ApiModelProperty(value = "工號", example = "EMP001", position = 6)
    private String workId;
}
