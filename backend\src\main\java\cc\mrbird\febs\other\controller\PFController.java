package cc.mrbird.febs.other.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.other.dao.ScoreMapper;
import cc.mrbird.febs.other.entity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@ResponseBody
@Api(tags = "其他/赛事评分查看模块")
@RequestMapping("/othersByScore")
public class PFController {

    private final ScoreMapper scoreMapper;

    @Autowired
    public PFController(ScoreMapper scoreMapper) {
        this.scoreMapper = scoreMapper;
    }

    @ApiOperation(value = "获取所有的赛事名称")
    @PostMapping("/getCompetitionName")
    public FebsResponse getCompetitionName() {
        List<String> list = this.scoreMapper.selectAllCompetition();
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation(value = "获取竞赛项目、主办单位、举办时间", notes = "参数值为赛事名称")
    @GetMapping("/getCompetitionData")
    public FebsResponse getCompetitionData(@RequestParam("competition") String competitionName) {
        //根据赛事名称获取主办等数据
        List<CompetitionConfVo> list = this.scoreMapper.getCompetitionData(competitionName);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation(value = "根据赛事名称和奖项获取数据", notes = "参数值为：对应赛事名称与一等獎、二等獎、三等獎")
    @GetMapping("/getScoreBySort")
    public FebsResponse getScoreBySort(@RequestParam("prizeName") String prizeName,@RequestParam("competitionName")String competitionName) {
        List<ScoreBySortVo> list = this.scoreMapper.getScoreBYSort(prizeName,competitionName);
        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation(value = "赛事排名明细", notes = "参数值为赛事名称")
    @GetMapping("/getCompetitionByScoreData")
    public FebsResponse getThisCompetitionData(@RequestParam("competition") String competitionName) {
        //根据赛事名称获取对应的赛事Id
        //Integer id =this.scoreMapper.selectCompetitionId(competitionName);
        List<SelectScoreVo> list = scoreMapper.selectAllDataById(competitionName);
        System.out.println(list);
        //联结表查询数据，根据赛事id
        return new FebsResponse().code("200").data(list);
    }
    @ApiOperation(value = "根据赛事名称获取评分标准和评分规则")
    @GetMapping("/getCompetitionConfig")
    public  FebsResponse getCompetitionConfig(@RequestParam("competitionName") String competitionName){
        List<CompetitionConfigVo> list =scoreMapper.getCompetitionConfig(competitionName);
        return  new FebsResponse().code("200").data(list);
    }


}
