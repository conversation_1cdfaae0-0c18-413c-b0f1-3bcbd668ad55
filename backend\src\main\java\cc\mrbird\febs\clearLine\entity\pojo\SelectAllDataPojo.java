package cc.mrbird.febs.clearLine.entity.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelectAllDataPojo {
    private String id;
    private String pl;
    private String discern;
    private String production;
    private String productionLine;
    private String recordDate;
    private String startTime;
    private String endTime;
    private String clearLineTime;
    private String oldOrder;
    private String newOrder;
    private String oldPartNumber;
    private String newPartNumber;
    private String oldState;
    private String newState;
    private String remark;
    private String linkId;
    private String qh1;
    private String qh2;
    private String qh3;
    private String qhStatus;
    private String stNo;
    private String showId;
    @ApiModelProperty(value = "工作台面")
    private String tableTopByLine;
    @ApiModelProperty(value = "地面")
    private String groundByLine;
    @ApiModelProperty(value = "不良品盒")
    private String defectiveBoxByLine;
    @ApiModelProperty(value = "物料容器")
    private String materialContainerByLine;
    @ApiModelProperty(value = "标识符")
    private String tagByLine;
    @ApiModelProperty(value = "生产治具")
    private String manufacturingFixtureByLine;
    @ApiModelProperty(value = "线外治具")
    private String offLineFixtureByLine;
    @ApiModelProperty(value = "线外工站")
    private String offLineWorkStationByLine;
    @ApiModelProperty(value = "不良品维修区")
    private String badMaintainByLine;
    @ApiModelProperty(value = "工程图面/文件")
    private String projectDrawingFileByLine;
    @ApiModelProperty(value = "辅助材料")
    private String auxiliaryMaterialsByLine;
    @ApiModelProperty(value = "物料暂存区")
    private String materialStagingAreaByLine;
    @ApiModelProperty(value = "产品包装")
    private String productPackagingByLine;
    @ApiModelProperty(value = "产品展示")
    private String productDisplayByLine;
    @ApiModelProperty(value = "工作台面")
    private String tableTopByQC;
    @ApiModelProperty(value = "地面")
    private String groundByQC;
    @ApiModelProperty("不良品盒")
    private String defectiveBoxByQC;
    @ApiModelProperty("物料容器")
    private String materialContainerByQC;
    @ApiModelProperty("标识符")
    private String tagByQC;
    @ApiModelProperty("生产治具")
    private String manufacturingFixtureByQC;
    @ApiModelProperty("线外治具")
    private String offLineFixtureByQC;
    @ApiModelProperty("线外工站站")
    private String offLineWorkStationByQC;
    @ApiModelProperty("不良品维修区")
    private String badMaintainByQC;
    @ApiModelProperty("工程图面/文件")
    private String projectDrawingFileByQC;
    @ApiModelProperty("辅助材料")
    private String auxiliaryMaterialsByQC;
    @ApiModelProperty("物料暂存区")
    private String materialStagingAreaByQC;
    @ApiModelProperty("产品包装")
    private String productPackagingByQC;
    @ApiModelProperty("产品展示")
    private String productDisplayByQC;
    private String backMark;
    private List<String> picURL;
}
