package cc.mrbird.febs.line.service;

import cc.mrbird.febs.line.entity.ConfigureConnect;
import cc.mrbird.febs.line.dao.ConfigureConnectMapper;
import cc.mrbird.febs.line.service.IConfigureConnectService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@DS("mssql")
public class ConfigureConnectServiceImpl extends ServiceImpl<ConfigureConnectMapper, ConfigureConnect> implements IConfigureConnectService {

}
