<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.agv.dao.AGVLogListMapper">

    <select id="queryAGVLogList" resultType="cc.mrbird.febs.agv.entity.AGVLogList"
            parameterType="cc.mrbird.febs.agv.param.AgvLogListQueryParam">
        SELECT
        [id]              as id,
        [RobotId]          as robotId,
        [errorCode]            as errorCode,
        [Log_Text]      as logText           ,
        [JLR_WorkId]            as jlrWorkId,
        [JLR_Name]            as jlrName,
        [StartDate]           as startDate
        FROM [APPGCL_Process].[dbo].[AGV_LogList]
        <where>
            <if test="robotId != null and robotId != ''">
                AND RobotId = #{robotId}
            </if>
            <if test="errorCode != null and errorCode != ''">
                AND errorCode = #{errorCode}
            </if>
            <if test="jlrName != null and jlrName != ''">
                AND JLR_Name = #{jlrName}
            </if>
            <if test="startDate != null and startDate != ''">
                AND  DateDIFF(day,StartDate,#{startDate}) =0
            </if>
        </where>
    </select>

</mapper>