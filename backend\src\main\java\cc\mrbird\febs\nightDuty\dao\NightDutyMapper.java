package cc.mrbird.febs.nightDuty.dao;

import cc.mrbird.febs.nightDuty.entity.ExportPerson;
import cc.mrbird.febs.nightDuty.entity.NightDutyPerson;
import cc.mrbird.febs.nightDuty.entity.vo.NightDutyPersonVO;
import com.baomidou.dynamic.datasource.annotation.DS;
import cc.mrbird.febs.nightDuty.entity.ImportPerson;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("mssql-paperless")
public interface NightDutyMapper {


    /**
     * 查询签核人员信息表是否与文件中有重复的人名
     * @param importPerson
     * @return
     */
    int queryChechPerson(ImportPerson importPerson);


    /**
     * 想签核人员信息表中插入没有的数据
     * @param importPerson
     */
    void insertChectPerson(ImportPerson importPerson);

    /**
     * 查询账号数据中有没有账号
     * @param importPerson
     * @return
     */
    int queryAccount(ImportPerson importPerson);

    /**
     * 创建APP端的账号
     * @param importPerson
     */
    void createAccount(ImportPerson importPerson);

    /**
     * 批量删除人员信息
     * @param id
     */
    void deletePerson(long id);

    /**
     * 更新值夜人员信息
     * @param nightDutyPerson
     */
    void updatePerson(NightDutyPerson nightDutyPerson);

    /**
     * 通过条件查询值夜人员信息
     * @param nightDutyPerson
     * @return
     */
    List<NightDutyPerson> selectPerson(NightDutyPerson nightDutyPerson);


    /**
     * 新增值夜人员
     * @param importPerson
     */
    void insertPerson(ImportPerson importPerson);

    /**
     * excel導出
     * @param time
     * @return
     */
    List<ExportPerson> exportPerson(String time);

    /**
     * 人员导出测试
     * @param time
     * @return
     */
    byte[] exportPersonTest(String time);


    /**
     * 查詢有無賬號
     * @param nightDutyPerson1
     * @return
     */
    int selectStauts(NightDutyPerson nightDutyPerson1);


    /**
     *查詢廠區
     * @return
     */
    List<String> queryFactory();

    /**
     * 查詢樓棟
     * @return
     */
    List<String> queryBuilding();

    /**
     * 通过id查询信息
     * @param id
     * @return
     */
    NightDutyPersonVO queryInfo(long id);

    /**
     * 查询机能
     * @return
     */
    List<String> queryFunctions();

    /**
     * 查询课别
     * @return
     */
    List<String> querySection();
}
