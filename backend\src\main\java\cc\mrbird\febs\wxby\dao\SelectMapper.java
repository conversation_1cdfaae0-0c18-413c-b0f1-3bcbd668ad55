package cc.mrbird.febs.wxby.dao;

import cc.mrbird.febs.wxby.entity.EquipmentNewError;
import cc.mrbird.febs.wxby.entity.MaintenanceLog;
import cc.mrbird.febs.wxby.entity.ProductionAndProductionLine;
import cc.mrbird.febs.wxby.entity.dto.TConfigurePersonByError;
import cc.mrbird.febs.wxby.entity.dto.TDataEquipmentError;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("two")
public interface SelectMapper {
    List<EquipmentNewError>  selectAllByLine();
    //查询叫修APP数据后插入表
    List<MaintenanceLog> queryALlEquipmentErrorLog();

    //统计当天的Equipment_id是否大于0,如果大于0,不做带出数据。如果小于0则带出数据

    int selectCountByMaintenance();
    //根據EqID 查找系列線體
    ProductionAndProductionLine queryProductionAndProductionLine(String equipmentId);
    //查找對應的叫修數據
    List<TDataEquipmentError>  queryCallErrorEquipmentData();
    //查询对应的生技代班
    TConfigurePersonByError queryWxsh(String name);



}