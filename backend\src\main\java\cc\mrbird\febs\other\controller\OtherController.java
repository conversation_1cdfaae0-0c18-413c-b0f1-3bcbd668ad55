package cc.mrbird.febs.other.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.other.dao.OtherMapper;
import cc.mrbird.febs.other.entity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@ResponseBody
@Api(tags = "标准维修工时")
@RequestMapping("/OtherApi")
public class OtherController {
    @Autowired
    private OtherMapper otherMapper;
   @ApiOperation(value = "获取维修组")
   @PostMapping("/getGroupName")
    public FebsResponse getGroupName(){
        return  new FebsResponse().code("200").data(otherMapper.getDiscern());
    }
    @ApiOperation(value = "根据维修组获取系列")
   @PostMapping("/getProduction")
    public  FebsResponse getProduction(@RequestParam("groupName") String groupName){

       return new FebsResponse().code("200").data( otherMapper.getProduction(groupName));
    }
    @ApiOperation(value = "据组别系列获取线体")
    @PostMapping("/getProductionLine")
    public FebsResponse getProductionLine(@RequestBody ProductionLineDto productionLineDto){

       return new FebsResponse().code("200").data(otherMapper.getProductionLine(productionLineDto));
    }
    @ApiOperation(value = "根据组别、系列、线体获取设备")
    @PostMapping("/getEquipmentName")
    public FebsResponse getEquipmentName(@RequestBody EquipmentDto equipmentDto){
        return new FebsResponse().code("200").data(otherMapper.getEquipmentName(equipmentDto));
    }

    @PostMapping("/selectAll")
   @ApiOperation(value = "查询列表数据",notes = "查询列表数据")
    public FebsResponse selectAll(@RequestBody SelectDto selectDto) {
       try {
           List<TConfigureCause> list = otherMapper.selectAll(selectDto);
           return new FebsResponse().code("200").data(list);
       }catch (Exception e){
           return new FebsResponse().code("500").message("出错了");
       }
   }
   @PostMapping("/edit")
   @ApiOperation(value = "根据自增id修改标准工时",notes = "根据自增id修改标准工时")
   public FebsResponse edit(@RequestBody UpdateDto updateDto){
       boolean b =otherMapper.updateById(updateDto);
       if (b){
           return new FebsResponse().code("200").message("修改成功");
       }else {
           return new FebsResponse().code("500").message("修改失败");
       }

   }

}
