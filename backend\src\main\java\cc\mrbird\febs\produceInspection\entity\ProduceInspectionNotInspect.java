package cc.mrbird.febs.produceInspection.entity;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表不点检信息
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionNotInspect  {

    private Integer id;

    private String sbu;

    private String functions;

    private String section;

    private String series;

    private String line;

    private String inspectDate;

    private Integer type;

    private String operatorUserId;

    private String operatorUserName;

    private String insDate;

    private String produceClass;

    private String remark;

    private String produceTime; //时刻，如果为全天不点检则为null
}