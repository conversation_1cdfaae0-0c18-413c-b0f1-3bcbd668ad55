package cc.mrbird.febs.wxby.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataEquipmentError {
    private Integer id;
    private String equipmentId;
    private String equipmentName;
    private String classId;
    private String classDate;
    private String errorDesc;
    private String startTime;
    private String endTime;
    private String transactor;
    private String causation;
    private String countermeasures;
    private String breakdown;
    private String status;
    private String errorType;
    private String callEmpName;
}
