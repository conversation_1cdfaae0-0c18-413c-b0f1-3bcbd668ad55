package cc.mrbird.febs.customerCondition.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author: 李囯斌
 * @Date: 2025/4/14
 * @Time: 8:10
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionFileDto {

    @ApiModelProperty(value = "文件",position = 0,dataType = "MultipartFile")
    private MultipartFile file;

    @ApiModelProperty(value = "上傳用戶工號", position = 4)
    private String uploadUserWorkId;

    @ApiModelProperty(value = "上傳用戶名", position = 5)
    private String uploadUserName;

    @ApiModelProperty(value = "產品処", position = 6)
    private String sbu;

    @ApiModelProperty(value = "機能", position = 7)
    private String functions;

    @ApiModelProperty(value = "廠區", position = 8)
    private String siteArea;

    @ApiModelProperty(value = "課別", position = 9)
    private String section;

    @ApiModelProperty(value = "客戶ID", position = 10)
    private Integer customerId;

    @ApiModelProperty(value = "客戶名稱", position = 11)
    private String customerName;

    @ApiModelProperty(value = "更新備註", position = 12)
    private String updateRemark;
}
