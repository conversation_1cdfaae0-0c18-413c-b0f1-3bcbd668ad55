package cc.mrbird.febs.indexpage.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelectSheetByName {
    @ApiModelProperty("名字")
    private String name;
    @ApiModelProperty("工号")
    private String workId;
    @ApiModelProperty("日期  格式:yyyy-MM-dd")
    private String recordDate;
    @JsonIgnore
    private String endDate;
    @ApiModelProperty(value = "PL")
    private String industry;
    @ApiModelProperty(value = "课别")
    private String discern;
}
