package cc.mrbird.febs.equipmentSelfInspection.dao;

import cc.mrbird.febs.equipmentSelfInspection.entity.EquipmentInspectionProjectData;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionMainInfoDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionProjectDto;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentInspectionQueryParam;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: 李国斌
 * Date: 2024/9/10
 * Time: 8:20
 */
@DS("one")
public interface EquipmentInspectionMaintenanceMapper {
    List<String> selectDiscern(@Param("SBU")String SBU, @Param("functions")String functions);

    List<String> selectProduction(@Param("SBU")String SBU, @Param("functions") String functions, @Param("discern") String discern);

    List<String> selectProductionLine(@Param("SBU")String SBU, @Param("functions") String functions,
                                      @Param("discern") String discern, @Param("production") String production);

    List<String> selectSelfInspectionType();

    List<EquipmentInspectionMainInfoDto> selectInspectionMainInfo(EquipmentInspectionQueryParam equipmentQueryParam);

    EquipmentInspectionMainInfoDto queryIsNotDJ(EquipmentInspectionQueryParam equipmentInspectionQueryParam);

//    List<EquipmentInspectionProjectDto> selectInspectionProjects(@Param("date") String date, @Param("selfInspectionType") String selfInspectionType,
//                                                                 @Param("equipmentID") String equipmentID);

    EquipmentInspectionProjectData selectInspectionProjectData(@Param("date") String date, @Param("selfInspectionType") String selfInspectionType,
                                                               @Param("equipmentID") String equipmentID);

    List<EquipmentInspectionProjectDto> selectEquipmentInspectionProjectsByEquipmentIDAndType(@Param("equipmentID")String equipmentID,
                                                                                              @Param("djType")String djType);
    List<String> selectSBU();

    List<String> selectFunctions(String sbu);

    EquipmentInspectionProjectDto selectRYXXByUserID(@Param("userID") String userID);


    List<String> queryDiscernBySBU(@Param("SBU")String sbu);
}
