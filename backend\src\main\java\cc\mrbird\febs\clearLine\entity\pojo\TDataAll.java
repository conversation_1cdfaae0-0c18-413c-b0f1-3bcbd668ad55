package cc.mrbird.febs.clearLine.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataAll {
    private String recordData;
    private String production;
    private String productionLine;
    private String startTime;
    private String endTime;
    private String clearTime;
    private String oldOrder;
    private String newOrder;
    private String oldPartNumber;
    private String newPartNumber;
    private String oldState;
    private String newState;
    private String remark;
}
