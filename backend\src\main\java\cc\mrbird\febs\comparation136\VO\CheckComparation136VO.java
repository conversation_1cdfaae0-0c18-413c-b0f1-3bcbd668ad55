package cc.mrbird.febs.comparation136.VO;



import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckComparation136VO implements Serializable {

    private Long id;

    //退單原因
    @ApiModelProperty(value = "退單原因")
    private String TD_TDYY;

    //退單工號
    private String TD_WorkId;

    //退單名字
    private String TD_Name;

    //退單類型
    private String TD_Type;

    //簽核狀態
    private Integer STATUS;


}
