package cc.mrbird.febs.task;

import cc.mrbird.febs.common.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
@Slf4j
public class SendMail {
//        private static final String USER_AGENT = "user-agent";
//        private static final String USER_AGENT_VALUE = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)";
//        private static final String CONNECTION = "connection";
//        private static final String CONNECTION_VALUE = "Keep-Alive";
//        private static final String ACCEPT = "accept";
//        private static final String UTF8 = "utf-8";
//        private static final String ACCEPT_CHARSET = "Accept-Charset";
//        private static final String CONTENTTYPE = "contentType";
//
//        private String mainUrl = "http://10.196.7.39:7209/api/MailSend/PostMail_Send?ls_to=<EMAIL>,<EMAIL>&ls_from=<EMAIL>&ls_cc&ls_subject=標題&ls_body=";
//        private String ls_to = "";  //收件人
//        private String ls_from = "";//发件人
//        private String ls_cc = "";//cc人
//        private String ls_subject="";//  邮件标题
//        private String ls_body ="";//  邮件正文
//        public static String sendPost(String url, String param) throws IOException {
//                StringBuilder result = new StringBuilder();
//                String urlNameString = url + "?" + param;
//                URL realUrl = new URL(urlNameString);
//                URLConnection conn = realUrl.openConnection();
//                conn.setDoInput(true);
//                conn.setDoOutput(true);
//                conn.setRequestProperty(CONTENTTYPE, UTF8);
//                conn.setRequestProperty(ACCEPT_CHARSET, UTF8);
//                conn.setRequestProperty(USER_AGENT, USER_AGENT_VALUE);
//                conn.setRequestProperty(CONNECTION, CONNECTION_VALUE);
//                conn.setRequestProperty(ACCEPT, "*/*");
//                try (PrintWriter out = new PrintWriter(conn.getOutputStream()); BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
//                        String line;
//                        while ((line = in.readLine()) != null) {
//                                result.append(line);
//                        }
//                        out.flush();
//                        out.print(param);
//                } catch (Exception e) {
//                        log.error("发送 POST 请求出现异常！", e);
//                }
//                return result.toString();
//        }

 }
