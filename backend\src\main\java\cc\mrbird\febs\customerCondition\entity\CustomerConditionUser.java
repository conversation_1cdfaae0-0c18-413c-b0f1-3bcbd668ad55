package cc.mrbird.febs.customerCondition.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄用戶實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionUser {

    @ApiModelProperty(value = "客戶記錄條件用戶ID", position = 0)
    private Integer customerConditionUploadUserId;

    @ApiModelProperty(value = "客戶記錄條件用戶名", position = 1)
    private String customerConditionUploadUserName;

    @ApiModelProperty(value = "產品処", position = 2)
    private String sbu;

    @ApiModelProperty(value = "機能", position = 3)
    private String functions;

    @ApiModelProperty(value = "廠區", position = 4)
    private String siteArea;

    @ApiModelProperty(value = "課別", position = 5)
    private String section;

    @ApiModelProperty(value = "工號", position = 6)
    private String workId;
}
