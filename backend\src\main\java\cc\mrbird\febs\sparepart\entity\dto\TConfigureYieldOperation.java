package cc.mrbird.febs.sparepart.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class TConfigureYieldOperation {
    private Integer id;
    private String productionLine;
    private String equipmentName;
    private String sparePartName;
    private String operationalReason;
    private String operationType;
    private String operationTime;
    private String OperationUser;
    private String lifeLimit;
    private String actualOutput;
}
