package cc.mrbird.febs.produceInspection.service;

import cc.mrbird.febs.produceInspection.entity.*;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionInspectProjectsQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionMainInfoQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionPartNumberQueryParam;
import cc.mrbird.febs.produceInspection.param.ProduceInspectionUserInfoQueryParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/19
 * @Time: 10:03
 */
public interface ProduceInspectionService {
    List<ProduceInspectionUserInfo> queryByUserId(String userID);

    List<String> querySBU();

    List<String> queryFunctions(String sbu);

    List<String> queryDiscern(String sbu, String functions);

    List<String> queryProduction(String sbu, String functions, String discern);

    List<String> queryProductionLine(String sbu, String functions, String discern, String production);

    ProduceInspectionNotInspect queryIsNotDJ(ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam);

    List<ProduceInspectionMainInfo> queryProductionInspectionMainInfo(ProduceInspectionMainInfoQueryParam produceInspectionMainInfoQueryParam);

    List<ProduceInspectionInspectProjects> queryProductionInspectionProjectData(ProduceInspectionInspectProjectsQueryParam produceInspectionInspectProjectsQueryParam);

    List<ProduceInspectionPartNumber> queryProduceInspectionPartNumberData(ProduceInspectionPartNumberQueryParam produceInspectionPartNumberQueryParam);

    List<ProduceInspectionUserInfo> queryProduceInspectionUserInfoData(ProduceInspectionUserInfoQueryParam produceInspectionUserInfoQueryParam);

    String importPersonnelData(MultipartFile file) throws IOException;

    String importInspectionItems(MultipartFile file) throws IOException;

    String importMaterialInfo(MultipartFile file) throws IOException;

    int updatePartNumber(ProduceInspectionPartNumber partNumber);

    int deletePartNumber(Integer id);

    // 人员信息的更新和删除
    int updateUserInfo(ProduceInspectionUserInfo userInfo);
    int deleteUserInfo(Integer id);

    // 点检项目的更新和删除
    int updateInspectProject(ProduceInspectionInspectProjects project);
    int deleteInspectProject(Integer id);

    List<ProduceInspectionPicUrl> queryPicsByMainInfoId(String mainInfoId);

    List<String> queryWorkStation(String sbu, String functions, String discern, String production, String line);
}
