<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.procedurefileuploading.dao.ProcedureFileUploadMapper">

    <insert id="insertFileData">
        USE Schedule
        INSERT INTO t_data_procedurefileupload(pl, enginery, discern, production, productionLine, fileType, fileName,filePath,fileVersionCode,uploadUser, equipmentName, equipmentId, remark,updateNum,showId) values (
                               #{pl},#{enginery},#{discern},#{production},#{productionLine},#{fileType},#{fileName},#{filePath},#{fileVersionCode},#{uploadUser},#{equipmentName},#{equipmentId},#{remark},#{updateNum},#{showId})
    </insert>

    <select id="getPL" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT pl FROM t_data_procedurefileupload
    </select>

    <select id="getDiscern" resultType="java.lang.String">
        SELECT DISTINCT discern FROM t_configure_procedurefileupload WHERE pl=#{param1} AND discern like concat ('%',#{param2},'%')
    </select>

    <select id="getProduction" resultType="java.lang.String">
        SELECT DISTINCT production FROM t_data_procedurefileupload WHERE pl=#{param1} AND discern=#{param2}
    </select>

    <select id="getProductionLine" resultType="java.lang.String">
        SELECT DISTINCT  productionLine FROM t_data_procedurefileupload WHERE pl=#{param1} AND discern=#{param2} AND production=#{param3}
    </select>

    <select id="getProcedureFileUploadData"
            resultType="cc.mrbird.febs.procedurefileuploading.entity.dto.TDataProcedureFileUpload">
        USE Schedule
        SELECT * FROM t_data_procedurefileupload
        <where>
            <if test="id != null">
                AND id=#{id}
            </if>
            <if test="pl != null and pl != ''">
                AND pl=#{pl}
            </if>
            <if test="enginery != null and enginery != ''">
            AND enginery=#{enginery}
            </if>
            <if test="discern != null and discern != ''">
                AND discern=#{discern}
            </if>
            <if test="production != null and production != ''">
                AND production=#{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND productionLine=#{productionLine}
            </if>
            <if test="equipmentName != null and equipmentName != ''">
                AND equipmentName=#{equipmentName}
            </if>
        </where>
        ORDER BY showId DESC
    </select>

    <select id="getProcedureFileUploadEquipmentName" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT equipmentName FROM t_data_procedurefileupload WHERE pl=#{pl} AND discern=#{discern} AND production=#{production} AND productionLine=#{productionLine}
    </select>

    <select id="getProductionByUploadFile" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT production FROM t_configure_procedurefileupload WHERE pl=#{pl} AND discern=#{discern}
    </select>

    <select id="getProductionLineByUploadFile" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT productionLine FROM t_configure_procedurefileupload WHERE pl=#{pl} AND discern=#{discern} AND production=#{production}
    </select>

    <select id="getEquipmentNameByUploadFile" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT equipmentName FROM t_configure_procedurefileupload WHERE pl=#{pl} AND discern=#{discern} AND production=#{production} AND productionLine=#{productionLine}
    </select>

    <select id="getEquipmentCodeByUploadFile" resultType="java.lang.String">
        USE Schedule
        SELECT DISTINCT equipmentCode FROM t_configure_procedurefileupload WHERE pl=#{pl} AND discern=#{discern} AND production=#{production} AND productionLine=#{productionLine} AND equipmentName=#{equipmentName}
    </select>

    <select id="isFirstInsert" resultType="int">

    </select>

    <insert id="insertFileDataByUpdate">
        USE Schedule
        INSERT INTO t_data_procedurefileupload(pl, enginery, discern, production, productionLine, fileType, fileName,filePath,fileVersionCode,uploadUser, equipmentName, equipmentId, remark,updateNum,showId) values (
        #{pl},#{enginery},#{discern},#{production},#{productionLine},#{fileType},#{fileName},#{filePath},#{fileVersionCode},#{uploadUser},#{equipmentName},#{equipmentId},#{remark},#{updateNum},#{showId})
    </insert>

    <update id="updateShowId">
        USE Schedule
        UPDATE t_data_procedurefileupload set showId='0' WHERE id=#{id}
    </update>

    <select id="queryUpdateNum" resultType="int">
        USE Schedule
        SELECT updateNum FROM t_data_procedurefileupload WHERE id=#{id}
    </select>

    <select id="sendEmail" resultType="java.lang.String">
        SELECT ISNULL((SELECT secondEmail
                       FROM [Schedule].[dbo].[t_configure_personAndEmail]  WHERE firstName=#{name}),'0') AS secondEmail
    </select>

    <select id="selectSecondName" resultType="java.lang.String">
        SELECT ISNULL((SELECT secondName
        FROM [Schedule].[dbo].[t_configure_personAndEmail]  WHERE firstName=#{name}),' ') AS secondEmail
    </select>

    <select id="queryEngineryByWorkId" resultType="java.lang.String">
      SELECT ISNULL((SELECT DISTINCT  Discern FROM T_Configure_Equipment  WHERE LineLeaderNumber=#{workId}),'裝配一課')   AS enginery
    </select>

</mapper>

