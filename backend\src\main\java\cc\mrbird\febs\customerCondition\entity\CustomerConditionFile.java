package cc.mrbird.febs.customerCondition.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄文件實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionFile {
    
    @ApiModelProperty(value = "客戶條件記錄表ID", position = 0)
    private Integer customerConditionFileId;
    
    @ApiModelProperty(value = "客戶條件記錄表名", position = 1)
    private String customerConditionFileName;
    
    @ApiModelProperty(value = "客戶條件記錄表存儲路徑", position = 2)
    private String customerConditionFileUrl;
    
    @ApiModelProperty(value = "上傳日期", position = 3)
    private Date uploadDate;
}
