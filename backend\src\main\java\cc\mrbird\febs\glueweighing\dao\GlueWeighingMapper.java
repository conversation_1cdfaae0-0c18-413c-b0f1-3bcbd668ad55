package cc.mrbird.febs.glueweighing.dao;

import cc.mrbird.febs.glueweighing.entity.PageConfigure;
import cc.mrbird.febs.glueweighing.entity.TWeightConfig;
import cc.mrbird.febs.glueweighing.entity.Weight;
import cc.mrbird.febs.glueweighing.entity.WeightAnbnormal;
import cc.mrbird.febs.glueweighing.entity.dto.GlueEditDto;
import cc.mrbird.febs.glueweighing.entity.dto.GlueSelectDto;
import cc.mrbird.febs.glueweighing.entity.vo.AllDataVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Mapper
@DS("mssql")
public interface GlueWeighingMapper {
    boolean insertByWeight(Weight weight);
    boolean insertByWeightAnbnormal(List<WeightAnbnormal> weightAnbnormals);

    List<AllDataVo> selectPageConfigure(GlueSelectDto glueSelectDto);
    List<PageConfigure> selectProductRange();
    boolean editByGlue(GlueEditDto editDto);
    List<String> getModel(String productionLine);

    List<String> getModelBy1(String productionLine);

    List<AllDataVo> selectUpdate(String uuid);

    boolean deleteUuidData(String uuid);

    boolean editDataByUUID(@Param("weightAnbNormal") List<WeightAnbnormal> list);
}
