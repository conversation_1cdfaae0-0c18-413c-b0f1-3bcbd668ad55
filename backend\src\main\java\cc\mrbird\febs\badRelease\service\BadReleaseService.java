package cc.mrbird.febs.badRelease.service;

import cc.mrbird.febs.badRelease.entity.qo.ConditionUseingQueryQo;
import cc.mrbird.febs.badRelease.entity.vo.BadReleaseDetailVo;
import cc.mrbird.febs.badRelease.entity.vo.BadReleaseInfoVo;

import java.util.List;

public interface BadReleaseService {

    /**
     * 通過工號查詢產品處
     * @param workId
     * @return
     */
    String querySBU(String workId);

    /**
     * 通過工號查詢機能
     * @param workId
     * @return
     */
    String queryFunction(String workId);

    /**
     * 查詢課別
     * @return
     */
    List<String> querySection();

    /**
     * 查詢線體
     * @return
     */
    List<String> queryLine();

    /**
     * 查詢料號
     * @return
     */
    List<String> queryMaterialNumber();

    /**
     * 查詢不良釋放日期
     * @return
     */
    List<String> queryBadReleaseDate();

    /**
     * 條件查詢
     * 條件為：課別、線體、料號、不良釋放日期
     * @param conditionUseingQuery
     * @return
     */
    List<BadReleaseInfoVo> queryCondition(ConditionUseingQueryQo conditionUseingQuery);

    /**
     * 詳情展示
     * id查詢
     * @param id
     * @return
     */
    BadReleaseDetailVo queryById(Integer id);
}
