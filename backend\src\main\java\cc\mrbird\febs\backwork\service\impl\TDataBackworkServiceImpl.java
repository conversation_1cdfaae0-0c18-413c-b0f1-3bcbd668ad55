package cc.mrbird.febs.backwork.service.impl;

import cc.mrbird.febs.backwork.common.BadRequestException;
import cc.mrbird.febs.backwork.common.FileProperties;
import cc.mrbird.febs.backwork.common.FileUtil;
import cc.mrbird.febs.common.authentication.JWTToken;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cc.mrbird.febs.backwork.entity.TDataBackwork;
import cc.mrbird.febs.backwork.service.TDataBackworkService;
import cc.mrbird.febs.backwork.dao.TDataBackworkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;


/**
* <AUTHOR>
* @description 针对表【t_data_backwork】的数据库操作Service实现
* @createDate 2023-02-13 15:59:23
*/
@Service
public class TDataBackworkServiceImpl extends ServiceImpl<TDataBackworkMapper, TDataBackwork>
    implements TDataBackworkService{

    @Autowired
    private FileProperties properties;

    @Autowired
    private TDataBackworkMapper tDataBackworkMapper;

    private JWTToken jwtToken;

    //文件上傳實現類
    @Override
    public Map<String, String> backWorkFile(MultipartFile backWorkFile) {
        //驗證文件大小
        FileUtil.checkSize(properties.getMaxSize(),backWorkFile.getSize());
        //驗證文件上傳格式
        String documents = " doc pdf   xlsx xls docx";
        String filetype = FileUtil.getExtensionName(backWorkFile.getOriginalFilename());
        if (filetype!=null && !documents.contains(filetype)){
            throw new BadRequestException("文件格式錯誤,進支持" +documents+"格式!!!!");
        }
        //把存儲路徑和文件名放入數據庫
        //先獲取
        TDataBackwork tU = new TDataBackwork();
        String oldPath = tU.getFilePath();
        tU.getFileName();
        tU.getFilePath();
        File file = FileUtil.upload(backWorkFile,properties.getPath().getAvatar());
        return new HashMap<String, String>(1) {{
            put("backWorkFileName", file.getName());
            put("backWorkPath",file.getPath());
        }};
    }

    @Override
    public void downloadFile(String fileName) {

      File file =new File("D:\\Fit\\backWorkFile");

    }



}




