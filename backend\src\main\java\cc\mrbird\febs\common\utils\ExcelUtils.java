package cc.mrbird.febs.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/19
 * @Time: 15:57
 */
public class ExcelUtils {
    /**
     * 读取Excel文件内容
     * @param file Excel文件
     * @param clazz 目标类型
     * @return 数据列表
     */
    public static <T> List<T> readExcel(MultipartFile file, Class<T> clazz) throws IOException {
        List<T> dataList = new ArrayList<>();

        // 创建自定义的AnalysisEventListener
        AnalysisEventListener<T> listener = new AnalysisEventListener<T>() {
            @Override
            public void invoke(T data, AnalysisContext context) {
                if (data != null) {
                    dataList.add(data);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 解析完成后的操作
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) {
                throw new RuntimeException("Excel解析失败：" + exception.getMessage());
            }
        };

        try {
            EasyExcel.read(file.getInputStream())
                    .head(clazz)
                    .registerReadListener(listener)
                    .headRowNumber(1)//跳过1行表头
                    .autoCloseStream(true)
                    .autoTrim(true)
                    .ignoreEmptyRow(true)
                    .sheet()
                    .doRead();
        } catch (Exception e) {
            throw new IOException("Excel读取失败：" + e.getMessage());
        }

        return dataList;
    }
}
