package cc.mrbird.febs.produceInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表不点检信息查询参数
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionNotInspectQueryParam {

    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;

    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;

    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;

    @ApiModelProperty(value = "綫體", example = "V1", position = 4)
    private String line;

    @ApiModelProperty(value = "系列", example = "1700SKT", position = 5)
    private String series;

    @ApiModelProperty(value = "不點檢日期", example = "2024-03-19", position = 6)
    private String inspectDate;

    @ApiModelProperty(value = "不點檢類型 1、全天不點檢 2：某時刻不點檢", example = "1", position = 7)
    private Integer type;

    @ApiModelProperty(value = "操作人工號", example = "EMP001", position = 8)
    private String operatorUserId;

    @ApiModelProperty(value = "操作人名字", example = "張三", position = 9)
    private String operatorUserName;

    @ApiModelProperty(value = "生產班別", example = "早班", position = 10)
    private String produceClass;

    @ApiModelProperty(value = "時刻，如果为全天不点检则为null", example = "null", position = 10)
    private String produceTime; //时刻，如果为全天不点检则为null
}