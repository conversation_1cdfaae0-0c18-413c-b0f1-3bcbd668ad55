package cc.mrbird.febs.customerCondition.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * Author: AI Assistant
 * Date: 2024/4/14
 * Time: 10:18
 * 客戶條件記錄summary文件實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionSummaryFile {
    
    @ApiModelProperty(value = "summary文件ID", position = 0)
    private Long summaryFileId;
    
    @ApiModelProperty(value = "summary文件名", position = 1)
    private String summaryFileName;
    
    @ApiModelProperty(value = "上傳日期", position = 2)
    private Date uploadDate;
}
