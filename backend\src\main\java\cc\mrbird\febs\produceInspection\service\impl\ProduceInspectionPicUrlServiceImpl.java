package cc.mrbird.febs.produceInspection.service.impl;

import cc.mrbird.febs.common.exception.FebsException;
import cc.mrbird.febs.produceInspection.dao.ProduceInspectionMapper;
import cc.mrbird.febs.produceInspection.entity.ProduceInspectionPicUrl;
import cc.mrbird.febs.produceInspection.service.IProduceInspectionPicUrlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

/**
 * @Author: 李囯斌
 * @Date: 2025/3/20
 * @Time: 8:18
 */
@Service
public class ProduceInspectionPicUrlServiceImpl implements IProduceInspectionPicUrlService {
    @Autowired
    private ProduceInspectionMapper mapper;

    private String filePath="http://************:8086/ProduceInspection/";

    @Override
    public ProduceInspectionPicUrl uploadPic(MultipartFile file, String id) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new FebsException("上传文件不能为空");
        }

        String mainInfoId=id;

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!Arrays.asList(".jpg", ".jpeg", ".png", ".gif").contains(fileType.toLowerCase())) {
            throw new FebsException("文件 " + originalFilename + " 格式不支持，只支持jpg、jpeg、png、gif格式的图片");
        }

        // 生成新文件名
        String date = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        int picOrder = mapper.getPicCount(Integer.valueOf(mainInfoId));
        String newFileName = date+"_"+mainInfoId+"_"+picOrder;

        // 保存图片记录
        ProduceInspectionPicUrl picUrl = new ProduceInspectionPicUrl();
        picUrl.setMainInfoId(Integer.valueOf(mainInfoId));
        picUrl.setPicUrl(filePath);
        picUrl.setPicName(newFileName);
        picUrl.setInsDate(String.valueOf(new Date()));
        picUrl.setPicOrder(String.valueOf(picOrder));
        mapper.saveProduceInspectionPicUrl(picUrl);

        //调用远程接口保存文件
//        // 确保目录存在
//        File dir = new File(filePath);
//        if (!dir.exists()) {
//            dir.mkdirs();
//        }
//
//        // 保存文件
//        File dest = new File(filePath + newFileName);
//        try {
//            file.transferTo(dest);
//        } catch (IOException e) {
//            throw new FebsException("文件 " + originalFilename + " 上传失败");
//        }
        return picUrl;
    }
}
