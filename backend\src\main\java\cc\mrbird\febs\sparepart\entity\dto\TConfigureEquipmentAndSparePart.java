package cc.mrbird.febs.sparepart.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TConfigureEquipmentAndSparePart {
    private Integer id;
    @ApiModelProperty(value = "事業處",position = 0,example = "IDS1")
    private String pl;
    @ApiModelProperty(value = "機能",position = 1,example = "裝配")
    private String enginery;
    @ApiModelProperty(value = "課別",position = 2,example = "裝配一課")
    private String discern;
    @ApiModelProperty(value = "系列",position = 3,example = "1700SKT")
    private String production;
    @ApiModelProperty(value = "線體",position =4,example = "V3")
    private String productionLine;
    @ApiModelProperty(value = "設備名稱",position = 5,example = "包裝機")
    private String equipmentName;
    @ApiModelProperty(value = "設備編號",position = 6,example = "BKAS11001")
    private String equipmentId;
    @ApiModelProperty(value = "備品名稱",position = 7,example = "馬達")
    private String sparePartName;
    @ApiModelProperty(value = "備品編號",position = 8,example = "1")
    private String sparePartId;
    @ApiModelProperty(value = "邏輯刪除字段",position = 9,example = "1刪除0未刪除")
    private String isDelete;
    @ApiModelProperty(value = "壽命上限",position = 10,example = "1000000")
    private String lifeLimit;

    public TConfigureEquipmentAndSparePart(String pl, String enginery, String discern, String production,String productionLine, String equipmentName, String equipmentId, String sparePartName, String sparePartId,String lifeLimit) {
        this.pl = pl;
        this.enginery = enginery;
        this.discern = discern;
        this.production=production;
        this.productionLine = productionLine;
        this.equipmentName = equipmentName;
        this.equipmentId = equipmentId;
        this.sparePartName = sparePartName;
        this.sparePartId = sparePartId;
        this.lifeLimit=lifeLimit;
    }
}
