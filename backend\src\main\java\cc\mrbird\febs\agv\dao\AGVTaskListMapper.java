package cc.mrbird.febs.agv.dao;

import cc.mrbird.febs.agv.entity.AGVLogList;
import cc.mrbird.febs.agv.entity.AGVTaskList;
import cc.mrbird.febs.agv.param.AGVTaskListQueryParam;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("one")
public interface AGVTaskListMapper {

    List<AGVTaskList> queryAGVTaskList(AGVTaskListQueryParam agvTaskListQueryParam);
}