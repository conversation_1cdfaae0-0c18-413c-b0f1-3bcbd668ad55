package cc.mrbird.febs.nightDuty.dao;


import cc.mrbird.febs.nightDuty.entity.NightDutyInfo;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("mssql-paperless")
public interface NightDutyInfoMapper {


    /**
     * 查询值夜信息
     * @param date
     * @return
     */
    List<NightDutyInfo> queryInfo(String date);

    /**
     * 查詢簽核狀態
     * @param nightDutyInfo
     * @return
     */
    int queryStatus(NightDutyInfo nightDutyInfo);


    /**
     * 查詢區域
     * @param building
     * @return
     */
    List<String> queryArea(String building);

    /**
     * 查詢層數
     * @param building
     * @return
     */
    List<String> queryFloor(String building);

    /**
     * 查詢責任部門
     * @param building
     * @return
     */
    List<String> queryDepartment(String building);

    /**
     * 查詢責任主管
     * @param building
     * @return
     */
    List<String> queryHeader(String building);


    /**
     * 查詢時間日期
     * @return
     */
    List<String> queryDate();


    /**
     * 通過時間樓棟信息分開處理數據
     * @param date
     * @param s
     * @return
     */
    List<NightDutyInfo> queryInfoByBuilding(String date, String s);
}
