package cc.mrbird.febs.wxby.service.impl;

import cc.mrbird.febs.wxby.dao.SelectMapper;
import cc.mrbird.febs.wxby.dao.WXBYMapper;
import cc.mrbird.febs.wxby.entity.MaintenanceLog;
import cc.mrbird.febs.wxby.entity.ProductionAndProductionLine;
import cc.mrbird.febs.wxby.entity.dto.TConfigurePersonByError;
import cc.mrbird.febs.wxby.entity.dto.TDataEquipmentError;
import cc.mrbird.febs.wxby.service.EquipmentAutoWrite;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-05-27
*/
@Service
public class EquipmentAutoWriteImpl implements EquipmentAutoWrite {
    private final SelectMapper selectMapper;
    private final WXBYMapper wxbyMapper;

    @Autowired
    public EquipmentAutoWriteImpl(SelectMapper selectMapper, WXBYMapper wxbyMapper) {
        this.selectMapper = selectMapper;
        this.wxbyMapper = wxbyMapper;
    }

    @Override
    //@Scheduled(cron="0 */1 * * * ?")
    public void scheduleByAutoWrite() {
      /*List<TDataEquipmentError> list =selectMapper.queryCallErrorEquipmentData();
       List<MaintenanceLog>  ml=editMaintenanceLogDto(list);
       for (MaintenanceLog maintenanceLog : ml){
           Integer insertId = wxbyMapper.queryInsertId(maintenanceLog.getId());
           if (insertId==null){
               maintenanceLog.setDataType("設備叫修");
               wxbyMapper.insertwxby(maintenanceLog);
           }
       }*/
        List<TDataEquipmentError> list = selectMapper.queryCallErrorEquipmentData();
        if (list == null) {
            return;
        }
        List<MaintenanceLog> ml = editMaintenanceLogDto(list);
        if (ml == null) {
            return;
        }
        for (MaintenanceLog maintenanceLog : ml) {
            if (maintenanceLog == null) {
                continue;
            }
            Integer insertId = wxbyMapper.queryInsertId(maintenanceLog.getInsertId());
            if (insertId == null) {
                maintenanceLog.setDataType("設備叫修");
                wxbyMapper.insertwxby(maintenanceLog);
            }
        }
    }

    //处理查询出来的数据
    private List<MaintenanceLog> editMaintenanceLogDto(List<TDataEquipmentError> list ){
       List<MaintenanceLog> lm=new ArrayList<>();
        for (TDataEquipmentError td :list){
            MaintenanceLog ml = new MaintenanceLog();
            TConfigurePersonByError te =selectMapper.queryWxsh(td.getTransactor());
            if (te==null){
                continue;
            }
            ProductionAndProductionLine pl=selectMapper.queryProductionAndProductionLine(td.getEquipmentId());
            ml.setInsertId(td.getId());
            ml.setPl(te.getPl());
           ml.setDiscern(te.getDiscern());
           ml.setProduction(pl.getProduction());
           ml.setProductionLine(pl.getProductionLine());
           ml.setRecordDate(td.getClassDate());
           ml.setEquipmentName(td.getEquipmentName());
           ml.setEquipmentId(td.getEquipmentId());
           ml.setClassInfo(td.getClassId());
           ml.setStartTime(td.getStartTime());
           ml.setEndTime(td.getEndTime());
           ml.setLossTime(td.getBreakdown());
           ml.setFaultType(td.getErrorType());
           ml.setFaultCause(td.getCausation());
           ml.setCounterPlan(td.getCountermeasures());
           ml.setStatus("false");
           ml.setQh1("true");
           ml.setFqr(td.getCallEmpName());
           ml.setQh2("true");
           ml.setZrsj(td.getTransactor());
           ml.setQh3("false");
           ml.setQh4("false");
           ml.setQh5("false");
           ml.setQh11("false");
           ml.setQh22("false");
           ml.setQh33("false");
           ml.setWxsh(te.getWxsh());
           lm.add(ml);
        }
        return lm;
    }




}
