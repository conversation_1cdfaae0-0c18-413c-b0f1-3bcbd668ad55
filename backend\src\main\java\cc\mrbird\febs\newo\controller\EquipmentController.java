package cc.mrbird.febs.newo.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.newo.dao.EquipmentMapper;
import cc.mrbird.febs.newo.entity.*;
import cc.mrbird.febs.newo.entity.qo.GetProductionLine;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
@ResponseBody
@RequestMapping("/newo/t-configure-equipment")
@RequiresPermissions("zyc")
public class EquipmentController {
    @Autowired
    private EquipmentMapper equipmentMapper;

    /**
     * 根据工号查询课别
     **/
    @ApiOperation(value = "根据工号查询课别", notes = "根据工号查询课别")
    @GetMapping("/getDiscern")
    public FebsResponse getEquipmentMapper(@RequestParam("cardId") String cardId) {
        List<PlDiscern> list = equipmentMapper.findDiscern(cardId);
        List<PlDiscern> list2 = equipmentMapper.selectQC(cardId);
        List<PlDiscern> list3 = equipmentMapper.selectMap(cardId);
        Map<Object, Object> map = new HashMap<>();
        Map<Object, Object> map2 = new HashMap<>();
        Map<Object, Object> map3 = new HashMap<>();
        for (PlDiscern plDiscern : list) {
            plDiscern.getPL().replace("", "");
            map.put("discern", plDiscern.getDiscern());
            map.put("pl", plDiscern.getPL().replaceAll("\\s*", ""));
        }
        for (PlDiscern plDiscern : list2) {
            plDiscern.getPL().replace("", "");
            map2.put("discern", plDiscern.getDiscern());
            map2.put("pl", plDiscern.getPL().replaceAll("\\s*", ""));
        }
        for (PlDiscern plDiscern : list3) {
            plDiscern.getPL().replace("", "");
            map3.put("discern", plDiscern.getDiscern());
            map3.put("pl", plDiscern.getPL().replaceAll("\\s*", ""));
        }
        if (map.isEmpty()) {
            if (map2.isEmpty()) {
                return new FebsResponse().data(map3);
            }
            return new FebsResponse().data(map2);
        }
        return new FebsResponse().data(map);
    }
    /*
    *
    * IDS3清換線 夸課別查看資料臨時改動
    * */
    @ApiOperation(value = "根据工号查询课别2", notes = "根据工号查询课别2")
    @GetMapping("/getDiscern2")
    public FebsResponse getEquipmentMapper2(@RequestParam("cardId") String cardId) {
            try{
              //判斷是否是IDS3-品管確認人
                String a="0";
                String b ="1";
            DataRoleByIDS3 role = equipmentMapper.selectRoleByCardId(cardId);
            if (role.getPl().equals("IDS3")&&role.getRole().equals("品管確認人")){
                List<PlDiscern> list2 = equipmentMapper.selectQC(cardId);
                Map<Object, Object> map = new HashMap<>();
                List<String> list = new ArrayList<>();
                list.add("裝配一課");
                list.add("裝配二課");
                list.add("裝配三課");
                for (PlDiscern plDiscern: list2){
                    map.put("pl",plDiscern.getPL());
                    map.put("discern",list);
                    map.put("role",a);
                }
                return new FebsResponse().code("200").data(map);
            }else {
            List<PlDiscern> list = equipmentMapper.findDiscern(cardId);
            List<PlDiscern> list2 = equipmentMapper.selectQC(cardId);
            List<PlDiscern> list3 = equipmentMapper.selectMap(cardId);
            Map<Object, Object> map = new HashMap<>();
            Map<Object, Object> map2 = new HashMap<>();
            Map<Object, Object> map3 = new HashMap<>();
            for(PlDiscern plDiscern: list) {
                plDiscern.getPL().replace("", "");
                map.put("discern", plDiscern.getDiscern());
                map.put("pl", plDiscern.getPL().replaceAll("\\s*", ""));
            }
            for (PlDiscern plDiscern : list2) {
                plDiscern.getPL().replace("", "");
                map2.put("discern", plDiscern.getDiscern());
                map2.put("pl", plDiscern.getPL().replaceAll("\\s*", ""));
            }

            for (PlDiscern plDiscern : list3) {
                plDiscern.getPL().replace("", "");
                map3.put("discern", plDiscern.getDiscern());
                map3.put("pl", plDiscern.getPL().replaceAll("\\s*", ""));
            }
            if (map.isEmpty()) {
                if (map2.isEmpty()) {
                    map3.put("role",b);
                    return new FebsResponse().data(map3);
                }
                map2.put("role",b);
                return new FebsResponse().data(map2);
            }
                map.put("role",b);
                return new FebsResponse().data(map);
        }
            }catch (Exception e){
                return new FebsResponse().code("500").message("数据为空");
            }
    }


    /**
     * 根据课别查询系列
     */
    @ApiOperation(value = "根据机能、课别查询系列", notes = "根据机能、课别查询系列")
    @GetMapping("/getProduction")
    public FebsResponse getProduction(@RequestParam("Discern") String Discern, @RequestParam("pl") String pl) {
        List<Equipment> list = equipmentMapper.findSeries(Discern, pl);
        return new FebsResponse().data(list);
    }

    /**
     * 根据系列查询线体
     */
    @ApiOperation(value = "根据系列查询线体", notes = "根据系列查询线体")
    @GetMapping("/getProductionLine")
    public FebsResponse getProductionLine(@RequestParam("Production") String Production) {
        List<Equipment> list = equipmentMapper.findProductionLine(Production);
        return new FebsResponse().data(list);
    }

    /**
     * 根据线体查询对应的工令编号
     */
    @ApiOperation(value = "根据线体查询对应的工令编号")
    @GetMapping("/getGL")
    public FebsResponse getGL(@RequestParam("Production") String Production, @RequestParam("ProductionLine") String ProductionLine) {
        List<GL> list = equipmentMapper.findGL(Production, ProductionLine);
        /**
         * 数据处理部分,利用list集合特性。
         * */
        List<GL> newlist = list.stream().distinct().collect(Collectors.toList());
        return new FebsResponse().data(newlist);
    }

    /**
     * 根据线体系列查询六流投单中的数据。
     * <p>
     * 生产日报表查询投单数据接口
     */
    @ApiOperation(value = "根据系列、线体查询六流投单中的数据", notes = "根据系列、线体查询六流投单中的数据")
    @GetMapping("/getSixTDGL")
    public FebsResponse getSixTDGL(@RequestParam("Production") String Production,
                                   @RequestParam("ProductionLine") String ProductionLine,
                                   @RequestParam("ClassInfo") String ClassInfo) {
        if (Production.equals("DMD車業")||Production.equals("DMD非車業")){
            Production="DMD";
        }
        List<SixTD> list = equipmentMapper.findTDGL(Production, ProductionLine, ClassInfo);
        return new FebsResponse().data(list);
    }


    /**
     * 根据课别查询产品大组长、轮班组长。
     */
    @ApiOperation(value = "根据课别查询产品大组长、轮班组长", notes = "根据课别查询产品大组长、轮班组长")
    @GetMapping("/getProductLeader")
    public FebsResponse getLeader(@RequestParam("Discern") String Discern, @RequestParam("pl") String pl) {
        EList eList = new EList();
        eList.setProductLeader(equipmentMapper.findProdutLeader(Discern, pl));
        eList.setGroupLeaderName(equipmentMapper.findGroup(Discern, pl));
        List newList = new ArrayList();
        newList.add(eList);
//      newList.stream().filter(Objects::isNull);
        newList.remove(Collections.singleton(null));
        return new FebsResponse().data(newList);
    }

    /**
     * 线长交接表根据课别查询系列
     */
    @GetMapping("/getProductionByJJ")
    @ApiOperation(value = "根据课别查询系列TO交接表", notes = "根据课别查询系列TO交接表")
    public FebsResponse getProductionByJJ(@RequestParam("Discern") String Discern) {
        return new FebsResponse().data(equipmentMapper.findProduction(Discern));
    }

    /**
     * 线长交接表根据系列查询线体
     */
    @PostMapping("/getProductionLineByJJ")
    @ApiOperation(value = "根据系列查询线体TO交接表", notes = "根据系列查询线体TO交接表")
    public FebsResponse getProductionLineByJJ(@RequestBody GetProductionLine getProductionLine) {
        return new FebsResponse().data(equipmentMapper.finProductionLineByjj(getProductionLine));
    }

    /**
     * 线长交接表根据系列、线体查询=》计划量，实际量，差异，成品料号，客户名称，客户料号，工单，D/C，入库单号1
     */
    @GetMapping("/getSixTDByJJ")
    @ApiOperation(value = "根据系列、线体查询投单数据", notes = "根据系列、线体查询投单数据")
    public FebsResponse getSixTDByJJ(@RequestParam("Production") String Production, @RequestParam("ProductionLine") String ProductionLine, @RequestParam("ClassInfo") String ClassInfo) {
        if (Production.equals("DMD車業")||Production.equals("DMD非車業")){
            Production="DMD";
        }
        return new FebsResponse().data(equipmentMapper.findTDDJ(Production, ProductionLine, ClassInfo));
    }

    /***
     * 删除功能
     * 只保留最后提交的记录接口
     */
    public FebsResponse deleteById(@RequestParam("Id") String Id, @RequestParam Equipment equipment) {
        //插入之前先进行查询该条记录是否存在.
        List<Equipment> list = equipmentMapper.selectList(null);
        list.forEach(System.out::println);
        int id = equipmentMapper.deleteById(Id);
        return new FebsResponse().data(id + "删除成功");
    }

    @PostMapping("insertWXBY")
    @ApiOperation(value = "维修保养插入", notes = "维修保养推送")
    public FebsResponse addwxby(@RequestBody WXBY wxby) {
        boolean b = equipmentMapper.insertWXBY(wxby);
        return new FebsResponse().code("200").message(String.valueOf(b));
    }

    /**
     * <AUTHOR>
     * @date 2023/1/3
     * 根据DC查询
     */
    @PostMapping("selectByDC")
    @ApiOperation(value = "根据DC查询", notes = "根据DC查询")
    public FebsResponse selectByDC(@RequestBody SelectDcDto selectDcDto) {
        List<SelectByDC> list = equipmentMapper.selectByDC(selectDcDto);
        return new FebsResponse().data(list);
    }

    /**
     * 首页数据查询接口
     */
    //统计开线数
    @ApiOperation(value = "统计开线数", notes = "统计开线数")
    @PostMapping("/countOpenLine")
    public FebsResponse countOpenLine() {
        Integer a = equipmentMapper.countOpenLineByOne();
        Integer b = equipmentMapper.countOpenLineByTwo();
        JSONObject param = new JSONObject();
        param.put("PL1oneClass", a);
        param.put("PL2twoClass", b);
        return new FebsResponse().data(param);
    }



    //統計生產日報表填寫數
    @PostMapping("/countProductionDay")
    @ApiOperation(value = "統計生產日報表填寫數")
    public FebsResponse countProductionDay(@RequestBody CountByPlDto countByPlDto) {
        if (countByPlDto.getPl().equals("PL1")) {
            Integer a = equipmentMapper.countProductionDayByOne();
            Integer b = equipmentMapper.countProductionDayByTwo();
            JSONObject param = new JSONObject();
            param.put("oneClass", a);
            param.put("twoClass", b);
            //System.out.println("一課:"+a+"二課:"+b);
            return new FebsResponse().data(param);
        } else {
            return null;
        }
    }

    //統計線長交接表填寫數
    @ApiOperation("統計線長交接表填寫數")
    public FebsResponse countLineWorkEdit() {
        Integer a = equipmentMapper.lineWorkEditByOne();
        Integer b = equipmentMapper.lineWorkEditByTwo();
        JSONObject param = new JSONObject();
        param.put("oneClass", a);
        param.put("twoClass", b);
        return new FebsResponse().data(param);
    }

    //統計136比對錶填寫數
    @ApiOperation("統計136比對錶填寫數")
    public FebsResponse countComparison() {
        return new FebsResponse().data(null);
    }

    @ApiOperation("PL3测试")
    @PostMapping("/testConfigure")
    public FebsResponse testConfigure(@RequestBody SelectDjDto selectDjDto) {
        if (selectDjDto.getProduction().equals("Type-C三屏")) {
            selectDjDto.setProduction("Type-C人工組");
        }
        List<DjXXVo> list = equipmentMapper.findTDDJ11(selectDjDto);
        return new FebsResponse().data(list);
    }

    @ApiOperation(value = "查找组长課別", notes = "查找组长課別")
    @GetMapping("/getDiscernByCardId")
    public FebsResponse getDiscernByCardId(@RequestParam("cardId") String cardId) {
        return new FebsResponse().data(equipmentMapper.findDiscernByCardId(cardId));
    }

}
