package cc.mrbird.febs.rdProject.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import cc.mrbird.febs.rdProject.entity.RdProjectFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的任務附件表數據訪問接口
 */
@DS("primary")
public interface RdProjectFileMapper {

    // 插入文件記錄
    int insertRdProjectFile(RdProjectFile rdProjectFile);

    // 根據文件ID刪除文件記錄
    int deleteRdProjectFileById(@Param("fileId") Long fileId);

    // 根據任務ID刪除文件記錄
    int deleteRdProjectFileByTaskId(@Param("taskId") Long taskId);

    // 更新文件記錄
    int updateRdProjectFile(RdProjectFile rdProjectFile);

    // 根據文件ID查詢文件記錄
    RdProjectFile selectRdProjectFileById(@Param("fileId") Long fileId);

    // 根據任務ID查詢文件記錄列表
    List<RdProjectFile> selectRdProjectFileByTaskId(@Param("taskId") Long taskId);

    // 查詢所有文件記錄
    List<RdProjectFile> selectAllRdProjectFile();
}
