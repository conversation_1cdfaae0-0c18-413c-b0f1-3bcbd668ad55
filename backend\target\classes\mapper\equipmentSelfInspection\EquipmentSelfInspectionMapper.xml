<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.equipmentSelfInspection.dao.EquipmentSelfInspectionMapper">
    <select id="selectSelfInspectionData"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionDto">
        SELECT [id]
             , [SBU]           as pl
             , [functions]     as enginery
             , CASE WHEN  section  ='A5一課' then '裝配一課' when section='A5二課' then '裝配二課' else  section
        end AS discern
             , [series]        as production
             , [line]          as productionLine
             , [EquipmentID]   as equipmentId
             , [EquipmentName] as equipmentName
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
        <where>
            <if test="discern != null and discern != ''">
                 section = #{discern}
            </if>
            <if test="production != null and production != ''">
                AND series = #{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND line = #{productionLine}
            </if>
        </where>
    </select>

    <select id="selectProjectMaintenanceData"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.ProjectMaintenance">
        SELECT [SBU]             as pl,
               [functions]       as enginery,
        CASE WHEN  section  ='A5一課' then '裝配一課' when section='A5二課' then '裝配二課'
        end AS discern,
               [series]          as production,
               [line]            as productionLine,
               a.[EquipmentID]   as equipmentId,
               [EquipmentName]   as equipmentName,
               b.[DJBW_Position] as selfInspectionPosition,
               b.[DJLX_Type]     AS selfInspectionType,
               b.[DJZT_Status]   as status,
               b.[JZ_Basis]      as selfInspectionStandard,
                b.id
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
                 as a
                 LEFT JOIN [APPGCL_Process].[dbo].[EquipmentDJ_DJXM]
            as b
                           on a.EquipmentID = b.EquipmentID
        <where>
            <if test="discern != null and discern != ''">
                AND section = #{discern}
            </if>
            <if test="production != null and production != ''">
                AND series = #{production}
            </if>
            <if test="productionLine != null and productionLine != ''">
                AND line = #{productionLine}
            </if>
            <if test="equipmentName != null and equipmentName != ''">
                AND EquipmentName = #{equipmentName}
            </if>
            <if test="selfInspectionType != null and selfInspectionType != ''">
                AND DJLX_Type=#{selfInspectionType}
            </if>
            <if test="1==1">
              AND  b.DJBW_Position is  not null
            </if>
        </where>
    </select>

    <select id="selectEquipmentPersonData"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentPersonDto">
        SELECT [id]
             , [SBU]         as pl
             , [functions]   as enginery
             , [section]     as discern
             <!--, [section]     as discern
             , [series]      as production
             , [line]        as productionLine-->
             , [SJ_UserID]   as userId
             , [SJ_UserName] as userName
             , [SJ_Type]     as dataType
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_RYXX]
           <where>
             <if test="discern != null and discern != ''">
                 AND section = #{discern}
             </if>
        </where>
    </select>

    <select id="selectDiscern" resultType="java.lang.String">
        SELECT DISTINCT  CASE WHEN  section  ='A5一課' then '裝配一課' when section='A5二課' then '裝配二課' else section
            end AS section
        FROM
            [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
    </select>

    <select id="selectProduction" resultType="java.lang.String">
        SELECT DISTINCT  series
        FROM
            [APPGCL_Process].[dbo].[EquipmentDJ_SBXX] WHERE section=#{discern}
    </select>

    <select id="selectProductionLine" resultType="java.lang.String">
        SELECT DISTINCT  line
        FROM
        [APPGCL_Process].[dbo].[EquipmentDJ_SBXX] WHERE section=#{param1} AND series=#{param2}
    </select>

    <select id="selectEquipmentName" resultType="java.lang.String">
        SELECT
                     DISTINCT   [EquipmentName]
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX] WHERE section=#{param1} AND series=#{param2} AND line=#{param3}
    </select>

    <select id="selectSelfInspectionType" resultType="java.lang.String">
        SELECT
             DISTINCT           [DJLX_Type]

        FROM [APPGCL_Process].[dbo].[EquipmentDJ_DJXM]
    </select>

    <update id="updateByEquipmentData">
        UPDATE  [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
        set
        SBU=#{pl},functions=#{enginery},section=#{discern},series=#{production},line=#{productionLine}
        ,EquipmentID=#{equipmentId},EquipmentName=#{equipmentName}
        WHERE id=#{id}
    </update>

    <update id="updateByProjectMaintenance">
        UPDATE [APPGCL_Process].[dbo].[EquipmentDJ_DJXM] set
        EquipmentID=#{equipmentId},DJLX_Type=#{selfInspectionType},DJZT_Status=#{status},
        DJBW_Position=#{selfInspectionPosition},JZ_Basis=#{selfInspectionStandard}
        WHERE id=#{id}
    </update>

    <update id="updateByEquipmentPerson">
        UPDATE  [APPGCL_Process].[dbo].[EquipmentDJ_RYXX] set
        SBU=#{pl},functions=#{enginery}, section=#{discern},SJ_UserID=#{userId},SJ_UserName=#{userName},SJ_Type=#{dataType}
            WHERE id =#{id}
    </update>

    <delete id="delByEquipmentPerson">
     DELETE   FROM [APPGCL_Process].[dbo].[EquipmentDJ_RYXX] WHERE id =#{id}
    </delete>

    <insert id="insertByEquipmentPerson">
        INSERT INTO [APPGCL_Process].[dbo].[EquipmentDJ_RYXX](SBU, functions,section, SJ_UserID, SJ_UserName, SJ_Type) VALUES (
                                                   #{pl},#{enginery},#{discern},#{userId},#{userName},#{dataType}                                                                                          )
    </insert>

    <delete id="delByEquipmentData">
        DELETE  FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX] WHERE id=#{id}
    </delete>

    <insert id="insertByEquipmentInspection">
      INSERT INTO  [APPGCL_Process].[dbo].[EquipmentDJ_SBXX] (SBU, functions, section, series, line, EquipmentID, EquipmentName) VALUES (
       #{pl},#{enginery},#{discern},#{production},#{productionLine},#{equipmentId},#{equipmentName})
    </insert>

    <insert id="insertByProjectMaintenance">
        INSERT INTO [APPGCL_Process].[dbo].[EquipmentDJ_DJXM](EquipmentID, DJLX_Type, DJZT_Status, DJBW_Position, JZ_Basis) VALUES (
       #{equipmentId},#{selfInspectionType},#{status},#{selfInspectionPosition},#{selfInspectionStandard})
    </insert>

    <select id="countByEquipmentInspection" resultType="int">
        SELECT count(*)  AS num  FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]  WHERE series=#{param1} AND line=#{param2} AND EquipmentName=#{param3}
    </select>

    <delete id="delByProjectMaintenance">
      DELETE  FROM [APPGCL_Process].[dbo].[EquipmentDJ_DJXM] WHERE id=#{id}
    </delete>

    <select id="selectEquipmentId" resultType="java.lang.String">
        SELECT top (1) EquipmentID as equipmentId FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]  WHERE series=#{param1} AND line=#{param2} AND EquipmentName=#{param3}
    </select>

    <select id="exampleDataByEquipment"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionDto">
        SELECT TOP (10) [id]
                      ,[SBU] AS pl
                      ,[functions] AS enginery
                      ,[section]  AS discern
                      ,[series]   AS production
                      ,[line]    AS productionLine
                      ,[EquipmentID]   AS equipmentId
                      ,[EquipmentName]  AS equipmentName
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_SBXX]
    </select>

    <select id="selectExampleDataByProject"
            resultType="cc.mrbird.febs.equipmentSelfInspection.entity.dto.ProjectMaintenance">

        SELECT TOP (10) [id]
                      ,[EquipmentID] AS equipmentId
                      ,[DJLX_Type] AS selfInspectionType
                      ,[DJZT_Status] AS status
                      ,[DJBW_Position] AS selfInspectionPosition
                      ,[JZ_Basis]  AS   selfInspectionStandard
        FROM [APPGCL_Process].[dbo].[EquipmentDJ_DJXM]
    </select>
</mapper>

