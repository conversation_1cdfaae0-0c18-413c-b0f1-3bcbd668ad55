package cc.mrbird.febs.glueweighing.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.glueweighing.dao.GlueWeighingMapper;
import cc.mrbird.febs.glueweighing.entity.PageConfigure;
import cc.mrbird.febs.glueweighing.entity.WeightAnbnormal;
import cc.mrbird.febs.glueweighing.entity.dto.GlueAddDto;
import cc.mrbird.febs.glueweighing.entity.dto.GlueSelectDto;
import cc.mrbird.febs.glueweighing.entity.vo.AllDataVo;
import cn.hutool.core.lang.UUID;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.util.List;

@RestController
@ResponseBody
@Api(tags = "點膠稱重")
public class GlueWeighingController {
    @Autowired
    private GlueWeighingMapper glueWeighingMapper;
    @PostMapping("/addNewData")
    @ApiOperation(value = "點膠新增數據接口",notes = "點膠新增數據接口")
    public FebsResponse addNewData(@RequestBody GlueAddDto glueAddDto){
        UUID uuid=UUID.randomUUID();
        String weightUUID=uuid.toString();
        String anbnormalUUID=uuid.toString();
        List<WeightAnbnormal> list = glueAddDto.getWeightAnbnormals();
        for (WeightAnbnormal weightAnbnormal : list){
            weightAnbnormal.setUuid(anbnormalUUID);
        }
        glueAddDto.getWeight().setUuid(weightUUID);
        boolean b=glueWeighingMapper.insertByWeight(glueAddDto.getWeight());
        boolean c=glueWeighingMapper.insertByWeightAnbnormal(glueAddDto.getWeightAnbnormals());
        if (b==true &&c==true){
            return new FebsResponse().code("200").message("添加成功");
        }
        return new FebsResponse().code("401").message("添加失敗");
    }
    @GetMapping("/selectConfigure")
    @ApiOperation(value = "查詢課別、系列、線體",notes = "查詢系列、線體")
    public FebsResponse selectConfigure(){
        List<PageConfigure> list =glueWeighingMapper.selectProductRange();
        return new FebsResponse().code("200").data(list);
    }
    @PostMapping("/selectAllDate")
    @ApiOperation(value = "根據課別、系列、線體、班別、量測指標查詢",notes = "根據課別、系列、線體、班別、量測指標查詢")
    public FebsResponse selectAllDate(@RequestBody GlueSelectDto glueSelectDto){
        String name=glueSelectDto.getMeasurementIndex();
        List<AllDataVo> list;
        if (name !=null){

        if (name.equals("noumenon") ){
            glueSelectDto.setMeasurementIndex("本體膠量");
        }
        }
        //System.out.println("量测指标是：****"+glueSelectDto.getMeasurementIndex());
        list =glueWeighingMapper.selectPageConfigure(glueSelectDto);
        return new FebsResponse().code("200").data(list);
    }

  /*  @PostMapping("/editGlue")
    @ApiOperation(value = "點膠備註修改接口",notes = "點膠備註修改接口")
    public FebsResponse editGlue(@RequestBody GlueEditDto glueEditDto){
        boolean a = glueWeighingMapper.editByGlue(glueEditDto);
        if (a){
            return new FebsResponse().code("200").message("修改成功");
        }
        return new FebsResponse().code("401").message("修改失敗");
    }*/


    @ApiOperation(value = "根据线体获取机台号，只有三条线有机台号：ME、MF、T4",
    notes = "productionLine:线体,noumenon:本体胶重,rubber:胶圈")
    @GetMapping("/getModel")
    public FebsResponse getModel(@RequestParam("productionLine")String productionLine,
                                 @RequestParam("name")String name){
        List<String> list;
        if (name.equals("noumenon")) {
            list = glueWeighingMapper.getModel(productionLine);
        }else {
            list = glueWeighingMapper.getModelBy1(productionLine);
        }
        return new FebsResponse().code("200").data(list);

    }

    @ApiOperation(value = "查询要修改的数据",notes = "查询要修改的数据")
    @GetMapping("selectEditData")
    public  FebsResponse selectEditData(@RequestParam("uuid") String uuid){
        List<AllDataVo> list =glueWeighingMapper.selectUpdate(uuid);
        return  new FebsResponse().code("200").data(list);
    }

    @PostMapping("editData")
    @ApiOperation(value = "修改指定数据接口",notes = "修改指定数据接口")
    public  FebsResponse editData(@RequestBody List<WeightAnbnormal> anbnormals){
        try{
            String uuid = null;
            for(WeightAnbnormal weightAnbnormal: anbnormals){
              uuid=  weightAnbnormal.getUuid();
            }
            System.out.println(uuid);
          glueWeighingMapper.deleteUuidData(uuid);
          glueWeighingMapper.insertByWeightAnbnormal(anbnormals);
        }catch (Exception e){
            return new FebsResponse().code("500").message("修改失败咯!");
        }
        return new FebsResponse().code("200").message("修改成功!");
    }







}
