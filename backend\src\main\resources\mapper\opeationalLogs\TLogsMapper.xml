<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.opeationalLogs.dao.TLogsByProduction">

 <select id="queryList" resultType="cc.mrbird.febs.opeationalLogs.entity.TOpeationalLogsProduction">
   SELECT * FROM t_opeational_logs_production WHERE name =#{name} AND recordTime <![CDATA[  >=  ]]> CONVERT(datetime,#{Date})
        AND  recordTime <![CDATA[  <=  ]]> CONVERT (datetime,#{Date})
 </select>
</mapper>