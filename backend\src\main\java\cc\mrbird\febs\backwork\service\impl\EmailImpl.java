package cc.mrbird.febs.backwork.service.impl;


import cc.mrbird.febs.backwork.common.HttpUtil;
import cc.mrbird.febs.backwork.service.Email;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLDecoder;


@Service
public class EmailImpl implements Email {
    @Override
    public String
    selectEmail(String email,String subject,String body,String cc) throws IOException {
        //String url = "http://10.196.5.230:7700/testSendEmail";
       String url="http://10.196.5.230:7700/sendEmail";
        String ls_to ="?"+"toEmails="+email;
       // String ls_from ="&"+"ls_from=" +"<EMAIL>";
        String ls_cc="&"+"ccEmails="+cc;
        String ls_subject="&"+"subject="+subject;
        String ls_body="&"+"content="+body;
        HttpUtil.sendPost(url,ls_to,ls_cc,ls_subject,ls_body);
       // System.out.println("我执行了");
        return "推送成功";
    }

    @Override
    public String sendEmail() throws IOException {
        String url ="http://10.196.7.39:7209/api/MailSend/PostMail_Send";
        String ls_to ="?"+"ls_to="+"<EMAIL>";    //emailMapper.selectEmail(workId);
        //String ls_from ="&"+"ls_from=" +"<EMAIL>";
        String ls_cc="&"+"ls_cc=";
        String ls_subject="&"+"ls_subject="+"<<<表單无纸化专案----->重工记录表需要您签核";
        //String ls_body="&"+"ls_body="+"→Http://10.196.5.230:220dd";
        String ls_body = URLDecoder.decode("&"+"ls_body="+"HTTP://10.196.5.230:220<br>当前流程为:1(承办)等待您签核<br>请勿回复此邮件<br>有问题请联系ids工業互聯網開發辦公室(郵箱同名)<br><王业桂：568-25023><br><李光翔：5068-26332>","utf-8");
        HttpUtil.sendPost(url,ls_to,ls_cc,ls_subject,ls_body);
        return "推送成功";
    }

    @Override
    public String sendCCEmail(String emailByCC,String subjectByCC,String bodyByCC,String ccByCC) throws IOException {
        String url = "http://10.196.5.230:7700/sendEmail";
        String ls_to ="?"+"toEmails="+emailByCC;
        //String ls_from ="&"+"ls_from=" +"<EMAIL>";
        String ls_cc="&"+"ccEmails="+ccByCC;
        String ls_subject="&"+"subject="+subjectByCC;
        String ls_body="&"+"content="+bodyByCC;
        HttpUtil.sendPost(url,ls_to,ls_cc,ls_subject,ls_body);
        // System.out.println("我执行了");
        return "推送成功";
    }

    @Override
    public String sendEmailBySparePart(String toEmails, String ccEmails, String subject, String content) throws IOException {
        String url = "http://10.196.5.230:7700/testSendEmailBySparePart";
        String ls_to ="?"+"toEmails="+toEmails;
        String ls_cc="&"+"ccEmails="+ccEmails;
        String ls_subject="&"+"subject="+subject;
        String ls_body="&"+"content="+content;
        HttpUtil.sendPost(url,ls_to,ls_cc,ls_subject,ls_body);
        // System.out.println("我执行了");
        return "OK";
    }


}
