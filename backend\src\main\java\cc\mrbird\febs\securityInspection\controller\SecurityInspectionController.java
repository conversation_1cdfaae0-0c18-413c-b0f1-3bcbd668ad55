package cc.mrbird.febs.securityInspection.controller;

import cc.mrbird.febs.backwork.common.EhAdminConstant;
import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.common.exception.FebsException;
import cc.mrbird.febs.securityInspection.dao.SecurityInspectionMapper;
import cc.mrbird.febs.securityInspection.entity.*;
import cc.mrbird.febs.securityInspection.serviec.SecurityInspectionService;
import cc.mrbird.febs.securityInspection.utils.ExcelExportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.net.URL;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/24 09:23
 * @description: 安全巡检
 */

@RestController
@Api(tags = "安全巡檢")
@Slf4j
@RequestMapping("/securityInspection")
public class SecurityInspectionController {

    @Autowired
    private SecurityInspectionService securityInspectionService;

    @Autowired
    private SecurityInspectionMapper securityInspectionMapper;

    /**
     * 查询巡检时间
     * @return
     */
    @GetMapping("/queryDate")
    @ApiOperation("查詢時間")
    public FebsResponse queryDate(){
        log.info("查询巡检时间");
        List<String> list = securityInspectionService.queryDate();
        return new FebsResponse().code("200").message("查询成功").data(list);
    }

    /**
     * 查询厂区
     * @return
     */
    @GetMapping("/queryFactory")
    @ApiOperation("查詢廠區")
    public FebsResponse queryFactory(){
        log.info("查询厂区");
        List<String> list = securityInspectionService.queryFactory();
        return new FebsResponse().code("200").message("查询成功").data(list);
    }

    /**
     * 查询楼栋
     * @return
     */
    @GetMapping("/queryBuilding")
    @ApiOperation("查詢樓棟")
    public FebsResponse queryBuilding(){
        log.info("查询楼栋");
        List<String> list = securityInspectionService.queryBuilding();
        return new FebsResponse().code("200").message("查询成功").data(list);
    }

    /**
     * 查询楼层
     * @return
     */
    @GetMapping("/queryFloor")
    @ApiOperation("查詢樓層")
    public FebsResponse queryFloor(){
        log.info("查询楼层");
        List<String> list = securityInspectionService.queryFloor();
        return new FebsResponse().code("200").message("查询成功").data(list);
    }

    /**
     * 查询點位信息
     * @return
     */
    @PostMapping("/queryInfoByPoint")
    @ApiOperation("查詢點位信息")
    public FebsResponse queryInfo(@RequestBody QueryCondition queryCondition){
        log.info("查询點位信息:",queryCondition);
        List<InspectionStationRaw> list = securityInspectionMapper.queryStation(queryCondition);
//        List<InspectionStationReport> list1 = securityInspectionService.groupByLocation( list);
        return new FebsResponse().code("200").message("查询成功").data(list);
    }

    @PostMapping("/queryPoint")
    @ApiOperation("查詢點位图片信息")
    public FebsResponse queryPoint(@RequestBody QueryCondition queryCondition){
        log.info("查询點位信息:",queryCondition);
        List<SecurityInspectionVO> list = securityInspectionMapper.queryPoint(queryCondition);
        return new FebsResponse().code("200").message("查询成功").data(list);
    }

    /**
     * 查询樓層信息
     * @return
     */
    @PostMapping("/queryInfoByFloor")
    @ApiOperation("查詢樓層信息")
    public FebsResponse queryInfoByFloor(@RequestBody QueryCondition queryCondition){
        log.info("查询樓層信息:",queryCondition);
        List<InspectionStationRaw> list = securityInspectionMapper.selectFloor(queryCondition);
//        List<InspectionStationReport> list1 = securityInspectionService.groupByLocation( list);
        return new FebsResponse().code("200").message("查询成功").data(list);
    }


    /**
     * 导出查询点位表
     * @param response HTTP响应对象
     */
    @PostMapping("/exportQueryPointTable")
    @ApiOperation("導出點位表")
    public void exportQueryPointTable(HttpServletResponse response,@RequestBody QueryCondition queryCondition) throws FebsException {
        try {

            
            // 调用service层获取数据
            List<SecurityInspectionExportExcelAllPoint> list = securityInspectionService.queryInfo(queryCondition);

            // 使用ExcelExportUtil导出数据，传递当前日期
            ExcelExportUtil.exportQueryPointTable(list, response, queryCondition);
            
            log.info("查询点位表导出成功");
        } catch (Exception e) {
            log.error("导出查询点位表失败", e);
            throw new FebsException("导出查询点位表失败：" + e.getMessage());
        }
    }

    /**
     * 导出查询樓層表
     * @param response HTTP响应对象
     */
    @PostMapping("/exportQueryFloorTable")
    @ApiOperation("導出樓層表")
    public void exportQueryFloorTable(HttpServletResponse response,@RequestBody QueryCondition queryCondition) throws FebsException {
        try {
            List<SecurityInspectionExportExcelFloor> list = securityInspectionService.queryInfoByFloor(queryCondition);
            ExcelExportUtil.exportQueryFloorTable(list, response,queryCondition);
            log.info("查询点位表导出成功");
        } catch (Exception e) {
            log.error("导出查询点位表失败", e);
            throw new FebsException("导出查询点位表失败：" + e.getMessage());
        }
    }

    @PostMapping("/exportExcel")
    @ApiOperation("文件导出")
    public FebsResponse exportExcel(@RequestBody QueryCondition queryCondition, HttpServletResponse response) throws IOException {
        log.info("文件导出:",queryCondition);
        List<SecurityInspectionVO> list = securityInspectionMapper.queryPoint(queryCondition);
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("安全巡检");
        Row title = sheet.createRow(0);
        //合并单元格10格
        sheet.addMergedRegion(new CellRangeAddress(0,0,0,8));
        Cell titleCell = title.createCell(0);
        titleCell.setCellValue("安全巡检");

        //设置title背景颜色为绿色，字体颜色为白色，楷体，字体大小为20号
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font titleFont = workbook.createFont();
        titleFont.setFontName("楷体");
        titleFont.setFontHeightInPoints((short) 20);
        titleFont.setColor(IndexedColors.WHITE.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleCell.setCellStyle(titleStyle);

        //设置表头
        Row header = sheet.createRow(1);
        header.createCell(0).setCellValue("楼栋");
        header.createCell(1).setCellValue("楼层");
        header.createCell(2).setCellValue("巡检地点");
        header.createCell(3).setCellValue("巡检人");
        header.createCell(4).setCellValue("签到时间");
        header.createCell(5).setCellValue("是否NG");
        header.createCell(6).setCellValue("NG描述");
        sheet.addMergedRegion(new CellRangeAddress(1,1,7,8));
        header.createCell(7).setCellValue("现场图片");

        //设置表头背景颜色为浅蓝色，字体颜色为黑色，宋体，字体大小为14号
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = workbook.createFont();
        headerFont.setFontName("宋体");
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        header.getCell(0).setCellStyle(headerStyle);
        header.getCell(1).setCellStyle(headerStyle);
        header.getCell(2).setCellStyle(headerStyle);
        header.getCell(3).setCellStyle(headerStyle);
        header.getCell(4).setCellStyle(headerStyle);
        header.getCell(5).setCellStyle(headerStyle);
        header.getCell(6).setCellStyle(headerStyle);
        header.getCell(7).setCellStyle(headerStyle);

        //从第2行开始往表里面写数据
        for (int i = 0; i < list.size(); i++) {
            Row row = sheet.createRow(i+2);
            SecurityInspectionVO securityInspectionVO = list.get(i);
            row.createCell(0).setCellValue(securityInspectionVO.getBuilding());
            row.createCell(1).setCellValue(securityInspectionVO.getFloor());
            row.createCell(2).setCellValue(securityInspectionVO.getStation());
            row.createCell(3).setCellValue(securityInspectionVO.getName());
            row.createCell(4).setCellValue(securityInspectionVO.getDate());
            row.createCell(5).setCellValue(securityInspectionVO.getStatus());
            row.createCell(6).setCellValue(securityInspectionVO.getDescription());

            //将图片插入Excel表格中
            Drawing<?> patriarch = sheet.createDrawingPatriarch();

            // 处理img1和img2
            if (securityInspectionVO.getImg1() != null && !securityInspectionVO.getImg1().isEmpty()) {
                insertImageToExcel(workbook, sheet, patriarch, securityInspectionVO.getImg1(), i+2, 7);
            }

            if (securityInspectionVO.getImg2() != null && !securityInspectionVO.getImg2().isEmpty()) {
                insertImageToExcel(workbook, sheet, patriarch, securityInspectionVO.getImg2(), i+2, 8);
            }

        }

        //设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("安全巡检.xlsx", "UTF-8"));
        workbook.write(response.getOutputStream());
        workbook.close();
        response.flushBuffer();
        return new FebsResponse().code("200").message("文件导出成功");
    }
    
    /**
     * 将网络图片插入到Excel单元格中
     *
     * @param workbook Excel工作簿
     * @param sheet 工作表
     * @param patriarch 绘图对象
     * @param imageUrl 图片URL
     * @param rowIndex 行索引
     * @param colIndex 列索引
     */
    private void insertImageToExcel(Workbook workbook, Sheet sheet, Drawing<?> patriarch, String imageUrl, int rowIndex, int colIndex) {
        ByteArrayOutputStream byteArrayOut = null;
        java.io.InputStream inputStream = null;

        try {
            // 设置行高（1厘米约等于28.35点）
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }
            row.setHeight((short)(2.29 * 28.35 * 20)); // 设置行高为2.29厘米

            // 设置列宽（1厘米约等于4.5个字符宽度）
            sheet.setColumnWidth(colIndex, (int)(4.29 * 4.5 * 256)); // 设置列宽为4.29厘米

            // 使用HttpClient或直接使用InputStream从URL获取图片数据
            URL url = new URL(imageUrl);
            inputStream = url.openStream();
            byte[] imageBytes = IOUtils.toByteArray(inputStream);

            // 如果图片数据为空，直接返回
            if (imageBytes == null || imageBytes.length == 0) {
                log.warn("无法获取图片数据: {}", imageUrl);
                return;
            }

            // 将图片添加到工作簿
            int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);

            // 创建锚点，设置图片位置
            // 使用列和行的索引来定位图片，确保图片完全填充单元格
            ClientAnchor anchor = patriarch.createAnchor(0, 0, 0, 0,
                colIndex, rowIndex, colIndex + 1, rowIndex + 1);

            // 设置图片缩放类型为不随单元格调整
            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_DONT_RESIZE);

            // 添加图片到绘图对象
            patriarch.createPicture(anchor, pictureIdx);

        } catch (Exception e) {
            // 记录异常但不中断处理
            log.error("插入图片到Excel失败: {}", imageUrl, e);
        } finally {
            // 确保资源被正确关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
            if (byteArrayOut != null) {
                try {
                    byteArrayOut.close();
                } catch (IOException e) {
                    log.error("关闭输出流失败", e);
                }
            }
        }
    }
}
