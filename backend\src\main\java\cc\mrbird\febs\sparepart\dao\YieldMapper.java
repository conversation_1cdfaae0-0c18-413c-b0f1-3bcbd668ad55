package cc.mrbird.febs.sparepart.dao;

import cc.mrbird.febs.sparepart.entity.dto.EquipmentDataBy123;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndYield;
import cc.mrbird.febs.sparepart.entity.dto.TConfigureYieldOperation;
import cc.mrbird.febs.sparepart.entity.qo.SparePartAndEquipmentQo;
import cc.mrbird.febs.sparepart.entity.qo.UpdateSparePartQo;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;

@DS("two-data")
public interface YieldMapper {
    void UpdateMethodBy123();

    List<EquipmentDataBy123> getSharePartIn123();

    List<TConfigureEquipmentAndYield> querySparePartAndView(SparePartAndEquipmentQo sparePartAndEquipmentQo);
    int updateSparePart(UpdateSparePartQo updateSparePartQo);

    int insertSparePart(UpdateSparePartQo updateSparePartQo);

    List<TConfigureYieldOperation> queryYieldOperation(Integer id);

    int UpdateByEmailSendState(Integer id,String state);
    int autoSumYield(String classData,String eqId);
    List<TConfigureEquipmentAndYield> selectTConfigureEquipmentAndYield();
    int updateByAutoSum(Integer id,Integer sum);
    List<TConfigureEquipmentAndYield> autoSumYieldByMin();
    int sumYieldByAuto(String classDate,String eqId);
    int updateById(Integer id,int yield);

    TConfigureEquipmentAndYield selectTConfigureEquipmentAndYieldById(Integer id);



}