package cc.mrbird.febs.sparepart.dao;

import cc.mrbird.febs.sparepart.entity.dto.TConfigureEquipmentAndSparePart;
import cc.mrbird.febs.sparepart.entity.dto.TConfigurePersonAndEquipment;
import cc.mrbird.febs.sparepart.entity.qo.SparePartAndEquipmentQo;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;
@DS("two-data")
public interface SparePartByDataMapper {
    //查詢所有線體和對應管控備品關係
    List<TConfigureEquipmentAndSparePart> queryBySharePartAndEquipment(SparePartAndEquipmentQo sparePartAndEquipmentVo );
    List<TConfigurePersonAndEquipment> queryByPersonAndEquipment(SparePartAndEquipmentQo sparePartAndEquipmentVo);

    List<String> queryPl();

    List<String> queryDiscern(String pl);

    List<String> queryProductionLine(SparePartAndEquipmentQo sparePartAndEquipmentVo);
    List<String> queryProductionLineByEdit(SparePartAndEquipmentQo sparePartAndEquipmentVo);
    List<String> querySparePArtCode(SparePartAndEquipmentQo sparePartAndEquipmentVo);

    int upDataBySparePartAndEquipment(TConfigureEquipmentAndSparePart tConfigureEquipmentAndSparePart);

    int upDataByPersonAndEquipment(TConfigurePersonAndEquipment tConfigurePersonAndEquipment);

    int insertByParePartAndEquipment(TConfigureEquipmentAndSparePart tConfigureEquipmentAndSparePart);
    int insertByPersonAndEquipment(TConfigurePersonAndEquipment tConfigurePersonAndEquipment);

    int existsWithSameFields(TConfigureEquipmentAndSparePart tConfigureEquipmentAndSparePart);

    int existsWithSameFieldsByPersonAndEquipment(TConfigurePersonAndEquipment tConfigurePersonAndEquipment);

    List<String> queryProduction(SparePartAndEquipmentQo sparePartAndEquipmentQo);
    List<String> queryProductionByEdit(SparePartAndEquipmentQo sparePartAndEquipmentQo);
    String  queryProductionLeader(String production,String productionLine);
}
