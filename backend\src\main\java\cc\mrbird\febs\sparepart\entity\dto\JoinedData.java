package cc.mrbird.febs.sparepart.entity.dto;

import java.util.List;

public class JoinedData {
    private EquipmentDataBy123 equipmentData;
    private List<TConfigurePersonAndEquipment> configureData;

    public JoinedData(EquipmentDataBy123 equipmentData, List<TConfigurePersonAndEquipment> configureData) {
        this.equipmentData = equipmentData;
        this.configureData = configureData;
    }

    public EquipmentDataBy123 getEquipmentData() {
        return equipmentData;
    }

    public List<TConfigurePersonAndEquipment> getConfigureData() {
        return configureData;
    }





    public void performOperationOnMergedData(List<JoinedData> mergedDataList) {
        mergedDataList.forEach(mergedData -> {
            System.out.println("Equipment Data: " + mergedData.getEquipmentData());
            System.out.println("Configure Data: " + mergedData.getConfigureData());
        });


    }
}
