package cc.mrbird.febs.equipmentSelfInspection.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentPersonDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.ProjectMaintenance;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentQueryParam;
import cc.mrbird.febs.equipmentSelfInspection.service.EquipmentSelfInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.util.List;

/**
 * Author: 覃金華
 * Date: 2024-08-12
 * Time: 上午 10:59
 */
@Api(tags = "設備自主點檢")
@RequestMapping("/equipmentSelfInspection")
@Slf4j
@RestController
@ResponseBody
public class EquipmentSelfInspectionController {
    private final EquipmentSelfInspectionService equipmentSelfInspectionService;

    @Autowired
    public EquipmentSelfInspectionController(EquipmentSelfInspectionService equipmentSelfInspectionService) {
        this.equipmentSelfInspectionService = equipmentSelfInspectionService;
    }
    //查詢課別
    @GetMapping("/queryDiscern")
    @ApiOperation("/獲取課別")
    public FebsResponse queryDiscern(){
        List<String> list = equipmentSelfInspectionService.queryDiscern();
        return  new FebsResponse().code("200").data(list);
    }
    //根據課別查詢系列
    @ApiImplicitParams({
    @ApiImplicitParam(name = "discern",value="課別,裝配一課|裝配二課|裝配三課",example = "裝配一課")
        })
    @GetMapping("/queryProduction")
    @ApiOperation("/獲取系列")
    public FebsResponse queryProduction(@RequestParam(value = "discern",required = false)String discern){
        List<String> list ;
        try {
            if ("裝配一課".equals(discern)){
                discern= "A5一課";
            }
            if ("裝配二課".equals(discern)) {
                discern= "A5二課";
            }
            list = equipmentSelfInspectionService.queryProduction(discern);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+discern);
        }
        return new FebsResponse().code("200").data(list);
    }
    //根據課別系列查詢線體
    @ApiImplicitParams({
            @ApiImplicitParam(name = "discern",value="課別,裝配一課|裝配二課|裝配三課",example = "裝配一課"),
            @ApiImplicitParam(name = "production",value="系列,1700SKT|4186SKT|4677SKT",example = "1700SKT")
    })
    @GetMapping("/queryProductionLine")
    @ApiOperation("獲取線體")
    public FebsResponse queryProductionLine(@RequestParam(value = "discern",required = false)String discern,
                                            @RequestParam(value = "production",required = false)String production){
        List<String> list ;
        try {
            if ("裝配一課".equals(discern)){
                discern= "A5一課";
            }
            if ("裝配二課".equals(discern)) {
                discern= "A5二課";
            }
            list = equipmentSelfInspectionService.queryProductionLine(discern,production);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+discern+production);
        }
        return new FebsResponse().code("200").data(list);
    }
    //查詢設備信息
    @ApiImplicitParams({
            @ApiImplicitParam(name = "discern",value="課別,裝配一課|裝配二課|裝配三課",example = "裝配一課"),
            @ApiImplicitParam(name = "production",value="系列,1700SKT|4186SKT|4677SKT",example = "1700SKT"),
            @ApiImplicitParam(name = "productionLine",value="線體,V1|V2|V3",example = "V1")
    })
    @GetMapping("/queryEquipmentName")
    @ApiOperation("獲取設備")
    public FebsResponse queryEquipmentName(@RequestParam(value = "discern",required = false)String discern,
                                           @RequestParam(value = "production",required = false)String production,
                                           @RequestParam(value = "productionLine",required = false)String productionLine
    ){
        List<String> list ;
        try {
            if ("裝配一課".equals(discern)){
                discern= "A5一課";
            }
            if ("裝配二課".equals(discern)) {
                discern= "A5二課";
            }
            list = equipmentSelfInspectionService.queryEquipmentName(discern,production,productionLine);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數:"+discern+production+productionLine);
        }
        return new FebsResponse().code("200").data(list);
    }



    @ApiOperation(value = "查詢設備數據信息",httpMethod = "POST")
    @PostMapping("/queryEquipmentData")
    public FebsResponse queryEquipmentData(@RequestBody EquipmentQueryParam equipmentQueryParam){
        List<EquipmentInspectionDto> list;
        try {
            String discern1 = "";
            if ("裝配一課".equals(equipmentQueryParam.getDiscern())) {
                discern1 = "A5一課";
                equipmentQueryParam.setDiscern(discern1);
            }
            if ("裝配二課".equals(equipmentQueryParam.getDiscern())) {
                discern1 = "A5二課";
                equipmentQueryParam.setDiscern(discern1);
            }
            list = equipmentSelfInspectionService.queryEquipmentInspectionData(equipmentQueryParam);
        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+equipmentQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }
    //查詢點檢類型
    @ApiOperation(value = "獲取點檢類型")
    @GetMapping("/querySelfInspectionType")
    public  FebsResponse querySelfInspectionType(){
        List<String> list = equipmentSelfInspectionService.querySelfInspectionType();
        return new FebsResponse().code("200").data(list);
    }

    //查詢點檢項目信息
    @ApiOperation(value = "查詢點檢項目數據",httpMethod = "POST")
    @PostMapping("/querySelfInspection")
    public FebsResponse querySelfInspection(@RequestBody EquipmentQueryParam equipmentQueryParam){
        List<ProjectMaintenance> list;
        try {
            String discern1 = "";
            if ("裝配一課".equals(equipmentQueryParam.getDiscern())) {
                discern1 = "A5一課";
                equipmentQueryParam.setDiscern(discern1);
            }
            if ("裝配二課".equals(equipmentQueryParam.getDiscern())) {
                discern1 = "A5二課";
                equipmentQueryParam.setDiscern(discern1);
            }
            list = equipmentSelfInspectionService.queryProjectMaintenanceData(equipmentQueryParam);

        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+equipmentQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    //查詢人員信息
    @ApiOperation(value = "查詢人員信息",httpMethod = "POST")
    @PostMapping("/queryEquipmentPerson")
    public FebsResponse queryEquipmentPerson(@RequestBody EquipmentQueryParam equipmentQueryParam){
        List<EquipmentPersonDto> list;
        try {
            String discern1 = "";
            if ("裝配一課".equals(equipmentQueryParam.getDiscern())) {
                discern1 = "A5一課";
                equipmentQueryParam.setDiscern(discern1);
            }
            if ("裝配二課".equals(equipmentQueryParam.getDiscern())) {
                discern1 = "A5二課";
                equipmentQueryParam.setDiscern(discern1);
            }
            list = equipmentSelfInspectionService.queryEquipmentPersonData(equipmentQueryParam);
        }catch (Exception e){
            return new FebsResponse().code("500").message("錯誤參數"+equipmentQueryParam);
        }
        return new FebsResponse().code("200").data(list);
    }

    //設備數據修改接口
    @ApiOperation(value = "修改設備數據")
    @PostMapping("/updateByEquipmentData")
    public FebsResponse updateByEquipmentData(@RequestBody EquipmentInspectionDto equipmentInspectionDto){
            int i ;
            try{
                if ("裝配一課".equals(equipmentInspectionDto.getDiscern())) {
                    equipmentInspectionDto.setDiscern( "A5一課") ;
                }
                if ("裝配二課".equals(equipmentInspectionDto.getDiscern())) {
                    equipmentInspectionDto.setDiscern( "A5二課") ;
                }
                i=equipmentSelfInspectionService.updateByEquipment(equipmentInspectionDto);
            }catch (Exception e){
                return new FebsResponse().code("500").message("修改失敗，參數為："+equipmentInspectionDto);
            }
        return  new FebsResponse().code("200").message("修改成功,數量為"+i);
    }
    //點檢項目修改接口
    @ApiOperation(value = "修改點檢項目數據")
    @PostMapping("/updateByProjectMaintenance")
    public FebsResponse updateByProjectMaintenance(@RequestBody ProjectMaintenance projectMaintenance){
        int i ;
        try{
            if ("裝配一課".equals(projectMaintenance.getDiscern())) {
                projectMaintenance.setDiscern( "A5一課") ;
            }
            if ("裝配二課".equals(projectMaintenance.getDiscern())) {
                projectMaintenance.setDiscern( "A5二課") ;
            }
            i=equipmentSelfInspectionService.updateByProjectMaintenance(projectMaintenance);
        }catch (Exception e){
            return new FebsResponse().code("500").message("修改失敗，參數為："+projectMaintenance);
        }
        return  new FebsResponse().code("200").message("修改成功,數量為"+i);
    }
    //人員修改接口
    @ApiOperation(value = "修改人員數據")
    @PostMapping("/updateByPerson")
    public FebsResponse updateByPerson(@RequestBody EquipmentPersonDto equipmentPersonDto){
        int i ;
        try{
            if ("裝配一課".equals(equipmentPersonDto.getDiscern())) {
                equipmentPersonDto.setDiscern( "A5一課") ;
            }
            if ("裝配二課".equals(equipmentPersonDto.getDiscern())) {
                equipmentPersonDto.setDiscern( "A5二課") ;
            }
            i=equipmentSelfInspectionService.updateByEquipmentPerson(equipmentPersonDto);
        }catch (Exception e){
            return new FebsResponse().code("500").message("修改失敗，參數為："+equipmentPersonDto);
        }
        return  new FebsResponse().code("200").message("修改成功,數量為"+i);
    }

    //人員數據刪除
    @ApiOperation(value = "人員數據刪除")
    @GetMapping("/delByEquipmentPerson")
    public FebsResponse delByEquipmentPerson(@RequestParam("id")Integer id){
        int i;
        try {
            i = equipmentSelfInspectionService.delByEquipmentPerson(id);
        }catch (Exception e){
            return new FebsResponse().code("500").message("刪除失敗，參數為:"+id);
        }
        return new FebsResponse().code("200").message("刪除成功,數量為:"+i);
    }

    //設備數據刪除
    @ApiOperation(value = "設備數據刪除")
    @GetMapping("/delByEquipmentData")
    public FebsResponse delByEquipmentData(@RequestParam("id") Integer id){
        int i;
        try{
            i= equipmentSelfInspectionService.delByEquipmentData(id);
        }catch (Exception e){
            return new FebsResponse().code("500").message("刪除失敗，參數為:"+id);
        }
        return  new FebsResponse().code("200").message("刪除成功,數量為："+i);
    }

    @ApiOperation(value = "點檢項目數據刪除")
    @GetMapping("/delByProjectMaintenance")
    public FebsResponse delByProjectMaintenance(@RequestParam("id") Integer id){
        int i;
        try{
            i= equipmentSelfInspectionService.delByProjectMaintenance(id);
        }catch (Exception e){
            return new FebsResponse().code("500").message("刪除失敗，參數為:"+id);
        }
        return  new FebsResponse().code("200").message("刪除成功,數量為："+i);
    }



    //人員數據插入
    @PostMapping("/insertByEquipmentPerson")
    @ApiOperation("人員數據插入")
    public FebsResponse insertByEquipmentPerson(@RequestBody EquipmentPersonDto equipmentPersonDto){
        int i;
        try{
            i= equipmentSelfInspectionService.insertByEquipmentPerson(equipmentPersonDto);
        }catch (Exception e){
            return new FebsResponse().code("500").message("刪除失敗，參數為:"+equipmentPersonDto);
        }
        return  new FebsResponse().code("200").message("插入成功,數量為:"+i);
    }
    //設備信息維護模板導出
    @ApiOperation("設備信息維護模板導出")
    @PostMapping("/getEquipmentDataExampleFile")
    public void getEquipmentDataExampleFile(HttpServletResponse response){
        try{
            //String discern="";
            //把最新的数据写进Excel里,然后再下载
            EquipmentQueryParam equipmentInspectionDto  = new EquipmentQueryParam();
            List<EquipmentInspectionDto> list =equipmentSelfInspectionService.selectExampleData();
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            XSSFSheet sheet = xssfWorkbook.createSheet("sheet1");
            //設置表頭
            XSSFRow headerRow  = sheet.createRow(0);
            headerRow .createCell(0).setCellValue("SBU");
            headerRow .createCell(1).setCellValue("機能");
            headerRow .createCell(2).setCellValue("課別");
            headerRow .createCell(3).setCellValue("系列");
            headerRow .createCell(4).setCellValue("線體");
            headerRow .createCell(5).setCellValue("設備ID");
            headerRow .createCell(6).setCellValue("設備名稱");
            int rowNum=1;
            for (EquipmentInspectionDto param :list){
                XSSFRow row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(param.getPl());
                row.createCell(1).setCellValue(param.getEnginery());
                row.createCell(2).setCellValue(param.getDiscern());
                row.createCell(3).setCellValue(param.getProduction());
                row.createCell(4).setCellValue(param.getProductionLine());
                row.createCell(5).setCellValue(param.getEquipmentId());
                row.createCell(6).setCellValue(param.getEquipmentName());

            }
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode("20225sadas.xlsx", "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            /*  response.addHeader("content-Length", "");*/
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/octet-stream");
            response.setHeader("Custom-Status", "200");
            try(OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())){
                xssfWorkbook.write(outputStream);
                outputStream.flush();
            }
            xssfWorkbook.close();
        }catch (Exception e){
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

    }

    //點檢項目信息維護模板導出
    @ApiOperation("點檢項目設備模板數據導出")
    @PostMapping("/getProjectMaintenanceExampleFile")
    public void getProjectMaintenanceExampleFile(HttpServletResponse response){
        try{
            //把最新的数据写进Excel里,然后再下载
            List<ProjectMaintenance> list =equipmentSelfInspectionService.selectExampleDataByProject();
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            XSSFSheet sheet = xssfWorkbook.createSheet("sheet1");
            //設置表頭
            XSSFRow headerRow  = sheet.createRow(0);
            headerRow .createCell(0).setCellValue("設備編號");
            headerRow .createCell(1).setCellValue("點檢類型");
            headerRow .createCell(2).setCellValue("點檢狀態");
            headerRow .createCell(3).setCellValue("點檢部位");
            headerRow .createCell(4).setCellValue("點檢基準");
            int rowNum=1;
            for (ProjectMaintenance param :list){
                XSSFRow row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(param.getEquipmentId());
                row.createCell(1).setCellValue(param.getSelfInspectionType());
                row.createCell(2).setCellValue(param.getStatus());
                row.createCell(3).setCellValue(param.getSelfInspectionPosition());
                row.createCell(4).setCellValue(param.getSelfInspectionStandard());
            }
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode("20225sadas.xlsx", "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            /*  response.addHeader("content-Length", "");*/
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/octet-stream");
            response.setHeader("Custom-Status", "200");
            try(OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())){
                xssfWorkbook.write(outputStream);
                outputStream.flush();
            }
            xssfWorkbook.close();
        }catch (Exception e){
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

    }

    //設備信息Excel導入
    @ApiOperation("設備信息Excel導入")
    @PostMapping("/excelImportByEquipment")
    public FebsResponse excelImportByEquipment(@RequestParam("file") MultipartFile file,HttpServletResponse response){
        if (file.isEmpty()) {
            response.setStatus(400,"error");
            return new FebsResponse().code("400").message("没有可以上传的文件，是1KB也没有，没有").status("error");
        }
        if (!isExcelFile(file)) {
            response.setStatus(400,"error");
            return new FebsResponse().code("400").message("这个文件不是Excel文件,不是").status("error");
        }
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if(row.getRowNum() == 0) continue;
                String pl = getValueFromCell(row.getCell(0));
                String enginery = getValueFromCell(row.getCell(1));
                String discern = getValueFromCell(row.getCell(2));
                String production=getValueFromCell(row.getCell(3));
                String productionLine = getValueFromCell(row.getCell(4));
                String equipmentId = getValueFromCell(row.getCell(5));
                String equipmentName= getValueFromCell(row.getCell(6));
                if (equipmentId == null || equipmentId.trim().isEmpty()) {
                    continue;
                }
                EquipmentInspectionDto e1 = new EquipmentInspectionDto();
                e1.setPl(pl);
                e1.setEnginery(enginery);
                e1.setDiscern(discern);
                e1.setProduction(production);
                e1.setProductionLine(productionLine);
                e1.setEquipmentId(equipmentId);
                e1.setEquipmentName(equipmentName);

                //插入設備表
                equipmentSelfInspectionService.inertByEquipmentInspection(e1);
            }
            workbook.close();
        } catch (Exception ex) {
            response.setStatus(400,"error");
            return  new FebsResponse().code("400").message("文件错误,文件狀態是:"+ex.getMessage()).status("error");
        }
        response.setStatus(200,"success");
        return  new FebsResponse().code("200").message("导入成功,文件名稱是:"+file.getName()).status("success");
    }

    //點檢項目導入
    @ApiOperation("點檢項目Excel導入")
    @PostMapping("/excelImportBySelfInspection")
    public FebsResponse excelImportBySelfInspection(@RequestParam("file") MultipartFile file,HttpServletResponse response){
        if (file.isEmpty()) {
            response.setStatus(400,"error");
            return new FebsResponse().code("400").message("没有可以上传的文件，是1KB也没有，没有").status("error");
        }
        if (!isExcelFile(file)) {
            response.setStatus(400,"error");
            return new FebsResponse().code("400").message("这个文件不是Excel文件,不是").status("error");
        }
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if(row.getRowNum() == 0) continue;
                String equipmentId = getValueFromCell(row.getCell(0));
                String selfInspectionType = getValueFromCell(row.getCell(1));
                String status = getValueFromCell(row.getCell(2));
                String selfInspectionPosition=getValueFromCell(row.getCell(3));
                String selfInspectionStandard = getValueFromCell(row.getCell(4));
                if (equipmentId == null || equipmentId.trim().isEmpty()) {
                    continue;
                }
                ProjectMaintenance e2 =new ProjectMaintenance();
                e2.setEquipmentId(equipmentId);
                e2.setSelfInspectionType(selfInspectionType);
                e2.setStatus(status);
                e2.setSelfInspectionPosition(selfInspectionPosition);
                e2.setSelfInspectionStandard(selfInspectionStandard);
                //插入點檢項目數據表
                equipmentSelfInspectionService.insertByProjectMaintenance(e2);
            }
            workbook.close();
        } catch (Exception ex) {
            response.setStatus(400,"error");
            return  new FebsResponse().code("400").message("文件错误,文件狀態是:"+ex.getMessage()).status("error");
        }
        response.setStatus(200,"success");
        return  new FebsResponse().code("200").message("导入成功,文件名稱是:"+file.getName()).status("success");
    }

    private boolean isExcelFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType.equals("application/vnd.ms-excel") ||
                contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                contentType.equals("text/csv");
    }

    private String getValueFromCell(Cell cell) {
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((int)numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            default:
                return "";
        }
    }

    //将接收的数据导出为EXCEL
    @ApiOperation("将传入的数据输出成为Excel文件")
    @PostMapping("/exportExcelByData")
    public void   exportExcelByData(@RequestBody List<ProjectMaintenance> list,HttpServletResponse response) {
        try {
            //String discern = "";
            //把最新的数据写进Excel里,然后再下载
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            XSSFSheet sheet = xssfWorkbook.createSheet("sheet1");
            //設置表頭
            XSSFRow headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("產品處");
            headerRow.createCell(1).setCellValue("機能");
            headerRow.createCell(2).setCellValue("課別");
            headerRow.createCell(3).setCellValue("系列");
            headerRow.createCell(4).setCellValue("線體");
            headerRow.createCell(5).setCellValue("設備編號");
            headerRow.createCell(6).setCellValue("設備名稱");

/*            headerRow.createCell(6).setCellValue("點檢類型");
            headerRow.createCell(7).setCellValue("點檢狀態");
            headerRow.createCell(8).setCellValue("點檢部位");
            headerRow.createCell(9).setCellValue("基準");*/
            int rowNum = 1;
            for (ProjectMaintenance param : list) {
                XSSFRow row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(param.getPl());
                row.createCell(1).setCellValue(param.getEnginery());
          /*      if ("裝配一課".equals(param.getDiscern())) {
                    discern = "A5一課";
                } else if ("裝配二課".equals(param.getDiscern())) {
                    discern = "A5二課";
                }*/
                row.createCell(2).setCellValue(param.getDiscern());
                row.createCell(3).setCellValue(param.getProduction());
                row.createCell(4).setCellValue(param.getProductionLine());
                row.createCell(5).setCellValue(param.getEquipmentId());
                row.createCell(6).setCellValue(param.getEquipmentName());


                /*row.createCell(6).setCellValue(param.getSelfInspectionType());
                row.createCell(7).setCellValue(param.getStatus());
                row.createCell(8).setCellValue(param.getSelfInspectionPosition());
                row.createCell(9).setCellValue(param.getSelfInspectionStandard());*/
            }
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLDecoder.decode("20225sadas.xlsx", "UTF-8")/*URLEncoder.encode(fileName, "utf-8")*/);
            /*  response.addHeader("content-Length", "");*/
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/octet-stream");
            response.setHeader("Custom-Status", "200");
            try (OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
                xssfWorkbook.write(outputStream);
                outputStream.flush();
            }
            xssfWorkbook.close();
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
        response.setStatus(200,"success");

    }

}
