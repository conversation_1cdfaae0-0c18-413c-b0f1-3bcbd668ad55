package cc.mrbird.febs.agv.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Author: 李囯斌
 * @Date: 2025/2/26
 * @Time: 8:34
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class AGVTaskListQueryParam {
    @ApiModelProperty(value = "机器人ID",example = "1", position = 1)
    private String robotId;
    @ApiModelProperty(value = "任务创建者姓名",example = "李曙波", position = 2)
    private String taskCreateUserName;
    @ApiModelProperty(value = "任务创建者工号",example = "F0842364", position = 3)
    private String taskCreateUserId;
    @ApiModelProperty(value = "任务创建日期",example = "2025-01-07", position = 4)
    private String taskCreateDate;
}
