package cc.mrbird.febs.produceInspection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表点检时段信息查询参数
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionInspectionTimeQueryParam {

    @ApiModelProperty(value = "產品処", example = "IDS1", position = 1)
    private String sbu;

    @ApiModelProperty(value = "機能", example = "裝配", position = 2)
    private String functions;

    @ApiModelProperty(value = "課別", example = "裝配一課", position = 3)
    private String section;

    @ApiModelProperty(value = "綫體", example = "V1", position = 4)
    private String line;

    @ApiModelProperty(value = "系列", example = "1700SKT", position = 5)
    private String series;

    @ApiModelProperty(value = "生產班別", example = "早班", position = 6)
    private String produceClass;

    @ApiModelProperty(value = "開始時間", example = "08:00", position = 7)
    private String startTime;

    @ApiModelProperty(value = "結束時間", example = "16:00", position = 8)
    private String endTime;
}