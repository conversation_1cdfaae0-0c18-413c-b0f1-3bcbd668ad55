package cc.mrbird.febs.indexpage.dao;

import cc.mrbird.febs.indexpage.entity.TDataComment;
import cc.mrbird.febs.indexpage.entity.TDataCommentAndUsers;
import cc.mrbird.febs.indexpage.entity.TDataUsers;
import com.baomidou.dynamic.datasource.annotation.DS;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
@DS("one")
public interface CommentSectionMapper {
    void insertDataByTComment(TDataComment tDataComment);

    void insertDataByUsers(TDataUsers tDataUsers);

    List<TDataComment> getRepliesByParentIdAndLevel(Integer parentId, Integer lever);

    List<TDataCommentAndUsers> getAllCommentData();

    void setSort(String id);
    Integer selectSortId();
    void updateBySort(Integer id);
}
