package cc.mrbird.febs.wxby.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "T_Equipment_Maintenance")
public class WXBYDto {
   private static final long serialVersionUID = 1L;
   @TableId(value = "Id",type = IdType.AUTO)
   private Long  id;
   @ApiModelProperty(value = "系列",required=true,position = 2)
   @TableField(value = "Production")
   private String  production;
   @ApiModelProperty(value = "线体",required=true,position = 3)
   @TableField(value = "ProductionLine")
   private String productionLine;
   @TableField(value = "EquipmentName")
   @ApiModelProperty(value = "设备名称",required = true,position = 4)
   private String equipmentName;
   @TableField(value = "EquipmentId")
   @ApiModelProperty(value = "设备编号",required = true,position = 5)
   private String  equipmentId;
   @TableField(value = "SCLH")
   @ApiModelProperty(value = "生产料号",required = true,position = 6)
   private String sclh;
   @TableField(value = "StartTime")
   @ApiModelProperty(value = "开始时间",required = true,position = 7)
   private String startTime;
   @TableField(value = "EndTime")
   @ApiModelProperty(value = "结束时间",required = true,position = 8)
   private String  endTime;
   @TableField(value = "LossTime")
   @ApiModelProperty(value = "损失时间",required = true,position = 9)
   private String   lossTime;
   @TableField(value = "FQR")
   @ApiModelProperty(value = "線長名字",required = true,position = 10)
   private String fqr;
   @TableField(value = "ZRSJ")
   @ApiModelProperty(value = "责任生技",required = true,position = 11)
   private String zrsj;
   @TableField(value = "RecordDate")
   @ApiModelProperty(value = "填写日期",required = true,position = 12)
   private String recordDate;
   @TableField(value = "ClassInfo")
   @ApiModelProperty(value = "班别",required = true,position = 13)
   private String classInfo;
   @TableField(value = "STATUS")
   @ApiModelProperty(value = "状态",required = true,position = 14)
   private String status;
}
