package cc.mrbird.febs.produceInspection.entity;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * Author: AI Assistant
 * Date: 2024/3/19
 * Time: 8:18
 * 生产自主检查表图片信息
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class ProduceInspectionPicUrl implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer mainInfoId;

    private String picUrl;

    private String picName;

    private String picOrder;

    private String insDate;
}