package cc.mrbird.febs.glueweighing.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AllDataVo {
  private String uuid;
  private String  production;
  private String productionLine;
  private String machineNumber;
  private String measurementIndex;
  private String measurementNum;
  private String recordDate;
  private String recordTime;
  private String specificationMax;
  private String specificationMin;
  private String oldWeight;
  private String newWeight;
  private String frequency;
  private String temperature;
  private String washValve;
  private String glueChange;
  private String glueWeight;
  private String judgementResult;
  private String otherTime;
  private String classInfo;
}
