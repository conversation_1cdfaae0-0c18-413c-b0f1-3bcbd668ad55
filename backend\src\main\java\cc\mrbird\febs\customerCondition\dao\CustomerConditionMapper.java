package cc.mrbird.febs.customerCondition.dao;

import cc.mrbird.febs.customerCondition.entity.*;
import cc.mrbird.febs.customerCondition.entity.dto.CustomerConditionDto;
import cc.mrbird.febs.customerCondition.param.CustomerConditionQueryParam;
import cc.mrbird.febs.customerCondition.param.CustomerConditionUserQueryParam;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄數據訪問接口
 */
@DS("one")
public interface CustomerConditionMapper {

    // 查詢客戶列表
    List<CustomerConditionCustomerData> selectCustomerList();

    // 根據ID查詢客戶
    CustomerConditionCustomerData selectCustomerById(@Param("customerId") Integer customerId);

    // 查詢廠區列表
    List<String> selectSiteAreaList();

    // 根據廠區查詢產品処列表
    List<String> selectSbuBySiteArea(@Param("siteArea") String siteArea);

    // 根據廠區和產品処查詢機能列表
    List<String> selectFunctionsBySiteAreaAndSbu(@Param("siteArea") String siteArea, @Param("sbu") String sbu);

    // 根據廠區、產品処和機能查詢課別列表
    List<String> selectSectionBySiteAreaAndSbuAndFunctions(@Param("siteArea") String siteArea, @Param("sbu") String sbu, @Param("functions") String functions);

    // 查詢客戶條件記錄主要信息
    List<CustomerConditionMainInfo> selectCustomerConditionMainInfo(CustomerConditionQueryParam queryParam);

    // 查詢客戶條件記錄詳細信息
    List<CustomerConditionDto> selectCustomerConditionDto(CustomerConditionQueryParam queryParam);

    // 查詢客戶條件記錄文件
    CustomerConditionFile selectCustomerConditionFileById(@Param("fileId") Integer fileId);

    // 查詢客戶條件記錄用戶
    List<CustomerConditionUser> selectCustomerConditionUsers();

    // 查詢客戶條件記錄郵件
    List<CustomerConditionMail> selectCustomerConditionMails(@Param("customerId") Integer customerId);

    // 保存客戶條件記錄文件
    @DS("one")
    int insertCustomerConditionFile(CustomerConditionFile file);

    // 保存客戶條件記錄主要信息
    int insertCustomerConditionMainInfo(CustomerConditionMainInfo mainInfo);

    // 查詢客戶條件記錄上傳次數
    int selectCustomerConditionUploadCount(@Param("siteArea") String siteArea, @Param("sbu") String sbu,
            @Param("functions") String functions, @Param("section") String section, @Param("customerId") Integer customerId);

    // 查詢客戶條件記錄詳情
    CustomerConditionDto selectCustomerConditionDetail(@Param("mainInfoId") Integer mainInfoId);

    // 根據多條件查詢用戶信息
    List<CustomerConditionUser> selectCustomerConditionUsersByCondition(CustomerConditionUserQueryParam queryParam);

    // 保存客戶條件記錄summary文件
    int insertCustomerConditionSummaryFile(CustomerConditionSummaryFile summaryFile);

    // 查詢最新的客戶條件記錄summary文件
    CustomerConditionSummaryFile selectLatestCustomerConditionSummaryFile();

    // 查詢本月未更新記錄的客戶和對應的local cs郵箱
    List<CustomerConditionMail> selectCustomersWithoutUpdateThisMonth();

    // 根據郵箱ID查詢郵箱信息
    CustomerConditionMail selectCustomerConditionMailById(Integer mailId);

    // 批量查詢郵箱信息
    List<CustomerConditionMail> selectCustomerConditionMailByIds(List<Integer> mailIds);
}
