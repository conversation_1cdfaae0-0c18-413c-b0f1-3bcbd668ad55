package cc.mrbird.febs.newo.entity.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetProductionLine {
    @ApiModelProperty(value = "事業處",example = "IDS1")
    private String pl;
    @ApiModelProperty(value = "課別",example = "裝配一課")
    private String discern;
    @ApiModelProperty(value = "系列",example = "SP6")
    private String production;
}
