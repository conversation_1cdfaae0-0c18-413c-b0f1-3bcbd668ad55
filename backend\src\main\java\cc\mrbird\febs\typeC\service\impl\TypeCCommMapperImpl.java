package cc.mrbird.febs.typeC.service.impl;


import cc.mrbird.febs.typeC.dao.TypeCByNotAMapper;
import cc.mrbird.febs.typeC.dao.TypeCMapper;
import cc.mrbird.febs.typeC.entity.qo.TableColumnName;
import cc.mrbird.febs.typeC.service.TypeCCommMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


@Service
public class TypeCCommMapperImpl implements TypeCCommMapper {
    private final TypeCByNotAMapper typeCByNotAMapper;

    private final TypeCMapper typeCMapper;

    public TypeCCommMapperImpl(TypeCByNotAMapper typeCByNotAMapper, TypeCMapper typeCMapper) {
        this.typeCByNotAMapper = typeCByNotAMapper;
        this.typeCMapper = typeCMapper;
    }

    @DS("150")
    @Override
    public List<Map<String, Object>> getTableData1(JdbcTemplate jdbcTemplate, TableColumnName tableColumnName) {
        String currentDataSource = DynamicDataSourceContextHolder.peek();
        String columns =typeCColumns(tableColumnName.getTableName());
       /* String sql ="SELECT "+columns+"  FROM "+tableColumnName.getTableName()+" where"+" time >="+"'"+tableColumnName.getStartTime()+"'" + "and" + " time <=" +"'"+tableColumnName.getEndTime()+"'";
        jdbcTemplate.queryForList(sql);*/
        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            int finalI = i;
            CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
                DynamicDataSourceContextHolder.push(currentDataSource);
                String selectSql ="SELECT "+ columns+"  FROM (SELECT ROW_NUMBER() OVER (ORDER BY id desc ,time desc) AS RowNum, * FROM "
                        + tableColumnName.getTableName()+" where"+" time >="+"'"+tableColumnName.getStartTime()+"'" + "and" + " time <=" +"'"+tableColumnName.getEndTime()+"'"
                        + ") AS RowConstrainedResult WHERE RowNum >= "
                        + finalI * 10000
                        + " AND RowNum < "
                        + (finalI + 1) * 10000
                        + " ORDER BY RowNum";
                List<Map<String, Object>> result = jdbcTemplate.queryForList(selectSql);
                DynamicDataSourceContextHolder.clear();
                return result;
            });
            futures.add(future);
        }
// 执行所有的异步任务
        CompletableFuture[] completableFutures = futures.toArray(new CompletableFuture[0]);
        CompletableFuture.allOf(completableFutures).join();
// 获取并处理每个异步任务的结果
        List<Map<String, Object>> results = new ArrayList<>();
        for (CompletableFuture<List<Map<String, Object>>> future : futures) {
            try {
                //List<Map<String, Object>> result = future.get(); // 获取每个异步任务的结果
                List<Map<String, Object>> result = future.get(1, TimeUnit.MINUTES);
                results.addAll(result);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                future.cancel(true); // 如果超时，取消任务
                e.printStackTrace();
            }
        }
        return results;
    }
    @DS("150-notA")
    @Override
    public List<Map<String, Object>> getTableData2(JdbcTemplate jdbcTemplate, TableColumnName tableColumnName) {
        String currentDataSource = DynamicDataSourceContextHolder.peek();
        String columns =typeCNotAColumns(tableColumnName.getTableName());
       /* String sql ="SELECT "+columns+"  FROM "+tableColumnName.getTableName()+" where"+" time >="+"'"+tableColumnName.getStartTime()+"'" + "and" + " time <=" +"'"+tableColumnName.getEndTime()+"'";
        jdbcTemplate.queryForList(sql);*/
        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            int finalI = i;
            CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
                DynamicDataSourceContextHolder.push(currentDataSource);
                String selectSql ="SELECT "+ columns+"  FROM (SELECT ROW_NUMBER() OVER (ORDER BY id desc ,time desc) AS RowNum, * FROM "
                        + tableColumnName.getTableName()+" where"+" time >="+"'"+tableColumnName.getStartTime()+"'" + "and" + " time <=" +"'"+tableColumnName.getEndTime()+"'"
                        + ") AS RowConstrainedResult WHERE RowNum >= "
                        + finalI * 10000
                        + " AND RowNum < "
                        + (finalI + 1) * 10000
                        + " ORDER BY RowNum";
                List<Map<String, Object>> result = jdbcTemplate.queryForList(selectSql);
                DynamicDataSourceContextHolder.clear();
                return result;
            });
            futures.add(future);
        }
// 执行所有的异步任务
        CompletableFuture[] completableFutures = futures.toArray(new CompletableFuture[0]);
        CompletableFuture.allOf(completableFutures).join();
// 获取并处理每个异步任务的结果
        List<Map<String, Object>> results = new ArrayList<>();
        for (CompletableFuture<List<Map<String, Object>>> future : futures) {
            try {
                //List<Map<String, Object>> result = future.get(); // 获取每个异步任务的结果
                List<Map<String, Object>> result = future.get(1, TimeUnit.MINUTES);
                results.addAll(result);
                if (results.size() >=100000) return results;
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                future.cancel(true); // 如果超时，取消任务
                e.printStackTrace();
            }
        }
        return results;
    }
    @DS("150")
    @Override
    public List<Map<String, Object>> getTableDataByA1000(JdbcTemplate jdbcTemplate, TableColumnName tableColumnName) {
        String columns = typeCColumns(tableColumnName.getTableName());
        String sql = "SELECT top(1000) "+ columns+" FROM " + tableColumnName.getTableName() +" order by id desc";
        List<Map<String,Object>> list =jdbcTemplate.queryForList(sql);
        return list;
    }
    @DS("150-notA")
    @Override
    public List<Map<String, Object>> getTableDataByNotA1000(JdbcTemplate jdbcTemplate, TableColumnName tableColumnName) {
        String columns = typeCNotAColumns(tableColumnName.getTableName());
        String sql = "SELECT top(1000) "+ columns+" FROM " + tableColumnName.getTableName() + " ORDER BY id desc ";
        List<Map<String,Object>> list =jdbcTemplate.queryForList(sql);
        return list;
    }
    @DS("150-notA")
    @Override
    public List<Map<String, Object>> getTableDataByTest(JdbcTemplate jdbcTemplate, TableColumnName tableColumnName) {
        String currentDataSource = DynamicDataSourceContextHolder.peek();
        String columns = typeCNotAColumns(tableColumnName.getTableName());
        int pageSize = 10;

        DynamicDataSourceContextHolder.push(currentDataSource);
        String selectSql = "SELECT "+ columns + " FROM " + tableColumnName.getTableName()+" where time >= '"+tableColumnName.getStartTime()+"' and time <= '"+tableColumnName.getEndTime()+
                "' ORDER BY id desc, time desc OFFSET " + (tableColumnName.getPage() - 1) * pageSize + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(selectSql);
        DynamicDataSourceContextHolder.clear();
        return result;
    }

    //获取非A产品对应表的全部字段
    private String typeCNotAColumns(String tableName){
        List<String> list =typeCByNotAMapper.selectColumns(tableName);
        String str =String.join(",",list);
        return str;
    }
    //获取A产品对应表的全部字段

    private String typeCColumns(String tableName){
        List<String> list =typeCMapper.selectColumns(tableName);
        String str = String.join(",",list);
        return  str;
    }












/*    @DS("150") // 使用数据源
    public List<Map<String, Object>> getTableData1(JdbcTemplate jdbcTemplate, String tableName,String customerName) {
       //String currentDataSource = DynamicDataSourceContextHolder.peek();
      *//*   switch (customerName) {
           case "A客戶":*//*
                //return jdbcTemplate.queryForList(" SELECT  top (1000)* FROM " + tableName);
            *//*case "非A客戶":
                if (tableName.equals("AOI_DATA_TYPEC_T7_CCD3_B2 ")){
                    return jdbcTemplate.queryForList("USE AOI_DATA_TYPEC SELECT  top (100000)* FROM "+tableName);
                }else {
                    return jdbcTemplate.queryForList("USE AOI_DATA_TYPEC SELECT * FROM "+tableName);
                }*//*

               *//* List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>();
                for (int i = 0; i < 10; i++) {
                    int finalI = i;
                    CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
                        DynamicDataSourceContextHolder.push(currentDataSource);
                        String selectSql ="SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY id desc ,time desc) AS RowNum, * FROM AOI_DATA_TYPEC.dbo."
                                + tableName
                                + ") AS RowConstrainedResult WHERE RowNum >= "
                                + finalI * 10000
                                + " AND RowNum < "
                                + (finalI + 1) * 10000
                                + " ORDER BY RowNum";
                        List<Map<String, Object>> result = jdbcTemplate.queryForList(selectSql);
                        DynamicDataSourceContextHolder.clear();
                        return result; // 返回查询结果
                    });

                    futures.add(future);
                }
// 执行所有的异步任务
                CompletableFuture[] completableFutures = futures.toArray(new CompletableFuture[0]);
                CompletableFuture.allOf(completableFutures).join();
// 获取并处理每个异步任务的结果
                List<Map<String, Object>> results = new ArrayList<>();
                for (CompletableFuture<List<Map<String, Object>>> future : futures) {
                    try {
                        List<Map<String, Object>> result = future.get(); // 获取每个异步任务的结果
                        results.addAll(result);
                    } catch (InterruptedException | ExecutionException e) {
                        e.printStackTrace();
                    }
                }
                return results;  // 这里应该返回收集的结果*//*

        return jdbcTemplate.queryForList(" SELECT  top (1000)* FROM " + tableName);
    }
    @DS("150") // 使用数据源
    public List<Map<String, Object>> getTableDataByCondition(JdbcTemplate jdbcTemplate, String tableName,String sql) {
        List<Map<String,Object>> map =  jdbcTemplate.queryForList("SELECT * FROM " + tableName +" where " + sql);
        return map;
    }*/





}