package cc.mrbird.febs.rdProject.dao;

import cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理數據訪問接口
 */
@DS("primary")
public interface RdProjectTaskMapper {

    // 查詢廠區列表
    List<String> selectSiteAreaList();

    // 根據廠區查詢產品処列表
    List<String> selectSbuBySiteArea(@Param("siteArea") String siteArea);

    // 根據廠區和產品処查詢機能列表
    List<String> selectFunctionsBySiteAreaAndSbu(@Param("siteArea") String siteArea, @Param("sbu") String sbu);

    // 根據廠區、產品処和機能查詢課別列表
    List<String> selectSectionBySiteAreaAndSbuAndFunctions(@Param("siteArea") String siteArea, @Param("sbu") String sbu, @Param("functions") String functions);

    // 查詢研究項目協同管理主要信息
    List<RdProjectTaskMainInfo> queryRdProjectTaskMainInfo(RdProjectTaskMainInfo rdProjectTaskMainInfo);

    // 查詢只指定為自己或自己的所屬的團隊的任務
    List<RdProjectTaskMainInfo> selectTasksByWorkerId(
            @Param("workerId") String workerId,
            @Param("condition") RdProjectTaskMainInfo condition
    );

    // 查詢指定自己的完整任務樹
    List<RdProjectTaskMainInfo> selectTaskTreesByWorkerId(@Param("workerId") String workerId,
                                                          @Param("condition") RdProjectTaskMainInfo condition);

    // 批量根據taskId查詢
    List<RdProjectTaskMainInfo> selectBatchByTaskIds(@Param("taskIds") List<Long> taskIds);

    // 查詢只指定為自己或自己的所屬的團隊的滿足條件的任務子節點的根節點組合（主要是因爲要分頁，按照根節點分頁查詢）
    List<String> selectTaskTreeRootIdsByWorkerId(@Param("workerId") String workerId,
                                                 @Param("condition") RdProjectTaskMainInfo condition);

    // 查詢所有滿足條件的任務子節點的根節點組合（主要是因爲要分頁，按照根節點分頁查詢）
    List<String> selectTaskTreeRootIds(@Param("condition") RdProjectTaskMainInfo condition);

    // 根據根節點來條件查詢節點
    List<RdProjectTaskMainInfo> selectTaskTreesByRootIds(@Param("ids") List<String> ids,
                                                          @Param("condition") RdProjectTaskMainInfo condition);

}
