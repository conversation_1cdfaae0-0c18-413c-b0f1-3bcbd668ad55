package cc.mrbird.febs.nightDuty.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NightDuty<PERSON>erson implements Serializable {

    private long id;


    @ApiModelProperty("厂区")
    private String factory;


    @ApiModelProperty("楼栋")
    //A4-A5
    private String building;


    @ApiModelProperty("值夜日期")
    //日期
    private String date;


    @ApiModelProperty("工号")
    //工号
    private String workId;


    @ApiModelProperty("名字")
    //名字
    private String name;


    //时间
    private String time;


    //賬號狀態 1表示有賬號，0表示沒有賬號
    private String status;
}
