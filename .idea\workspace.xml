<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7a4fb787-0db2-4aea-a980-3ec992d25e06" name="默认变更列表" comment="优化查询主要信息接口以及不点检查询接口">
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/backend/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/controller/RdProjectTaskController.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/controller/RdProjectTaskController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/entity/vo/RdProjectTaskMainInfoVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/entity/vo/RdProjectTaskMainInfoVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/service/RdProjectTaskService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/service/RdProjectTaskService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/service/impl/RdProjectTaskServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/rdProject/service/impl/RdProjectTaskServiceImpl.java" afterDir="false" />
    </list>
    <list id="55e902f0-5780-4d10-bedb-45d8d2053c69" name="修改項目協同管理的task查詢接口為分頁查詢" comment="修改項目協同管理的task查詢接口為分頁查詢" />
    <list id="25ae064a-13fc-42f8-b15b-97fc35015886" name="准备一次性查询所有数据" comment="准备一次性查询所有数据" />
    <list id="c921aa8c-a976-4bec-8cbb-cf0a3c02bada" name="新增红墨水导出接口以及条件查询接口" comment="" />
    <list id="f8f14674-8f2f-4be2-960e-c5c1ef0f9fef" name="項目協同管理task查詢接口修改為分頁" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Tag Library Descriptor" />
        <option value="AnnotationType" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="liguobin" />
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="liguobin" />
        <option value="lgb" />
        <option value="李国斌" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="liguobin" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="ProjectId" id="2lrc3HZVDpwSEAw7I69SSWjwfR4" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="ASKED_ADD_EXTERNAL_FILES" value="true" />
    <property name="DatabaseDriversLRU" value="mysql" />
    <property name="RequestMappingsPanelOrder0" value="0" />
    <property name="RequestMappingsPanelOrder1" value="1" />
    <property name="RequestMappingsPanelWidth0" value="75" />
    <property name="RequestMappingsPanelWidth1" value="75" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="SHARE_PROJECT_CONFIGURATION_FILES" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../QualityAssurance_backend" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="project.structure.last.edited" value="项目" />
    <property name="project.structure.proportion" value="0.15" />
    <property name="project.structure.side.proportion" value="0.2" />
    <property name="settings.editor.selected.configurable" value="vcs.Git" />
  </component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\javaWorkspace\backend-lesspaper\backend\src\main\resources\mapper\produceInspection" />
      <recent name="D:\javaWorkspace\backend-lesspaper\backend\src\main\resources\mapper\AGV" />
      <recent name="D:\javaWorkspace\backend-lesspaper\backend\src\main\resources\mapper\equipmentSelfInspection" />
      <recent name="D:\javaWorkspace\backend-lesspaper\backend\src\main\java\cc\mrbird\febs\equipmentSelfInspection\service\impl" />
      <recent name="D:\javaWorkspace\backend-lesspaper\backend\src\main\java\cc\mrbird\febs\equipmentSelfInspection\service" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="ArquillianJUnit" factoryName="" nameIsGenerated="true">
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FebsApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="febs_shiro_jwt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cc.mrbird.febs.FebsApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="7a4fb787-0db2-4aea-a980-3ec992d25e06" name="默认变更列表" comment="" />
      <changelist id="25ae064a-13fc-42f8-b15b-97fc35015886" name="准备一次性查询所有数据" comment="" />
      <changelist id="c921aa8c-a976-4bec-8cbb-cf0a3c02bada" name="新增红墨水导出接口以及条件查询接口" comment="" />
      <changelist id="f8f14674-8f2f-4be2-960e-c5c1ef0f9fef" name="項目協同管理task查詢接口修改為分頁" comment="" />
      <changelist id="55e902f0-5780-4d10-bedb-45d8d2053c69" name="修改項目協同管理的task查詢接口為分頁查詢" comment="" />
      <created>1725941436889</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1725941436889</updated>
      <workItem from="1725941438583" duration="17400000" />
      <workItem from="1725967476299" duration="32000" />
      <workItem from="1726013269861" duration="24323000" />
      <workItem from="1726099102311" duration="3950000" />
      <workItem from="1726138460540" duration="650000" />
      <workItem from="1726185269094" duration="6391000" />
      <workItem from="1726299617749" duration="2348000" />
      <workItem from="1726444219814" duration="4370000" />
      <workItem from="1726617369354" duration="10816000" />
      <workItem from="1726703727742" duration="2957000" />
      <workItem from="1726726181536" duration="88000" />
      <workItem from="1726726368332" duration="6664000" />
      <workItem from="1726789815246" duration="4987000" />
      <workItem from="1726876764211" duration="4061000" />
      <workItem from="1727049181309" duration="8168000" />
      <workItem from="1727135737289" duration="6485000" />
      <workItem from="1727171252801" duration="1227000" />
      <workItem from="1727221536399" duration="12163000" />
      <workItem from="1727308657293" duration="10018000" />
      <workItem from="1727330060395" duration="4652000" />
      <workItem from="1727394538647" duration="18542000" />
      <workItem from="1727484835259" duration="1000" />
      <workItem from="1727505873141" duration="375000" />
      <workItem from="1727506268156" duration="2217000" />
      <workItem from="1729213723172" duration="26000" />
      <workItem from="1729213764444" duration="42000" />
      <workItem from="1729215821066" duration="4206000" />
      <workItem from="1729653121209" duration="874000" />
      <workItem from="1729661458394" duration="827000" />
      <workItem from="1729834806955" duration="74000" />
      <workItem from="1729904238701" duration="603000" />
      <workItem from="1730168012205" duration="214000" />
      <workItem from="1730168269506" duration="85000" />
      <workItem from="1730168467432" duration="112000" />
      <workItem from="1730180079529" duration="790000" />
      <workItem from="1730337484545" duration="3103000" />
      <workItem from="1730514226436" duration="193000" />
      <workItem from="1730514439393" duration="1242000" />
      <workItem from="1730523415600" duration="693000" />
      <workItem from="1730878218617" duration="28000" />
      <workItem from="1730878259342" duration="1186000" />
      <workItem from="1730942476382" duration="7149000" />
      <workItem from="1730955612044" duration="5719000" />
      <workItem from="1731391572648" duration="1411000" />
      <workItem from="1731894841875" duration="57000" />
      <workItem from="1731894917119" duration="8419000" />
      <workItem from="1731907027797" duration="2259000" />
      <workItem from="1731973907180" duration="15498000" />
      <workItem from="1732009960650" duration="31000" />
      <workItem from="1732010027689" duration="597000" />
      <workItem from="1732060390441" duration="8271000" />
      <workItem from="1732146809033" duration="4116000" />
      <workItem from="1732233354517" duration="4310000" />
      <workItem from="1732320598428" duration="805000" />
      <workItem from="1732753765837" duration="56000" />
      <workItem from="1732753863478" duration="464000" />
      <workItem from="1733185794979" duration="534000" />
      <workItem from="1733186373149" duration="1379000" />
      <workItem from="1733219673312" duration="1524000" />
      <workItem from="1733384588231" duration="1444000" />
      <workItem from="1733444029231" duration="17000" />
      <workItem from="1733446462152" duration="3033000" />
      <workItem from="1733532725594" duration="7000" />
      <workItem from="1734311806522" duration="255000" />
      <workItem from="1734420562765" duration="73000" />
      <workItem from="1734498706831" duration="85000" />
      <workItem from="1734591551797" duration="25000" />
      <workItem from="1735275811896" duration="4125000" />
      <workItem from="1735343936883" duration="434000" />
      <workItem from="1735344392788" duration="1566000" />
      <workItem from="1735516969330" duration="77000" />
      <workItem from="1736124770818" duration="374000" />
      <workItem from="1736125284519" duration="31000" />
      <workItem from="1736126213870" duration="98000" />
      <workItem from="1740452903591" duration="4576000" />
      <workItem from="1740481356727" duration="721000" />
      <workItem from="1740529078082" duration="3601000" />
      <workItem from="1740624458307" duration="1701000" />
      <workItem from="1740713609164" duration="1470000" />
      <workItem from="1740719027396" duration="5215000" />
      <workItem from="1740725840867" duration="2452000" />
      <workItem from="1740729458528" duration="970000" />
      <workItem from="1740730533765" duration="215000" />
      <workItem from="1740730947809" duration="864000" />
      <workItem from="1740794731892" duration="3357000" />
      <workItem from="1740798254972" duration="92000" />
      <workItem from="1740798499040" duration="317000" />
      <workItem from="1742343653918" duration="109000" />
      <workItem from="1742347876645" duration="9888000" />
      <workItem from="1742362073005" duration="12781000" />
      <workItem from="1742381737173" duration="287000" />
      <workItem from="1742382046728" duration="464000" />
      <workItem from="1742382530538" duration="1149000" />
      <workItem from="1742428901683" duration="20213000" />
      <workItem from="1742786732808" duration="4504000" />
      <workItem from="1742801325769" duration="1521000" />
      <workItem from="1743131358277" duration="297000" />
      <workItem from="1743209232660" duration="318000" />
      <workItem from="1743228800444" duration="2526000" />
      <workItem from="1743231481221" duration="232000" />
      <workItem from="1743477249325" duration="834000" />
      <workItem from="1743478147569" duration="354000" />
      <workItem from="1743478542001" duration="789000" />
      <workItem from="1744182022150" duration="121000" />
      <workItem from="1744242725889" duration="4979000" />
      <workItem from="1744329682597" duration="1362000" />
      <workItem from="1744415944633" duration="10023000" />
      <workItem from="1744427805237" duration="408000" />
      <workItem from="1744428517529" duration="141000" />
      <workItem from="1744433925412" duration="134000" />
      <workItem from="1744434859981" duration="3995000" />
      <workItem from="1744588557125" duration="20000" />
      <workItem from="1744588589813" duration="8915000" />
      <workItem from="1744599794527" duration="914000" />
      <workItem from="1744600786678" duration="99000" />
      <workItem from="1744675899393" duration="9522000" />
      <workItem from="1744693542900" duration="2702000" />
      <workItem from="1744704887898" duration="3365000" />
      <workItem from="1744714763805" duration="623000" />
      <workItem from="1744763471449" duration="101000" />
      <workItem from="1744764055262" duration="83000" />
      <workItem from="1744764473384" duration="1545000" />
      <workItem from="1744848221901" duration="28000" />
      <workItem from="1744877855592" duration="1428000" />
      <workItem from="1744937077242" duration="2122000" />
      <workItem from="1744971335752" duration="593000" />
      <workItem from="1745028629829" duration="1185000" />
      <workItem from="1745194758160" duration="40000" />
      <workItem from="1745488061867" duration="301000" />
      <workItem from="1745490018633" duration="104000" />
      <workItem from="1749194272585" duration="25000" />
      <workItem from="1749194315576" duration="337000" />
      <workItem from="1749194973187" duration="469000" />
      <workItem from="1749427202574" duration="347000" />
      <workItem from="1749428968631" duration="7000" />
      <workItem from="1750396763589" duration="547000" />
      <workItem from="1750397356405" duration="175000" />
      <workItem from="1750463381145" duration="1505000" />
      <workItem from="1750638459636" duration="21000" />
      <workItem from="1750643348221" duration="52000" />
      <workItem from="1750645058044" duration="46000" />
      <workItem from="1750663817306" duration="609000" />
      <workItem from="1750664569560" duration="2029000" />
      <workItem from="1750722872794" duration="599000" />
      <workItem from="1751676782936" duration="266000" />
      <workItem from="1751874169184" duration="303000" />
      <workItem from="1751933429786" duration="256000" />
      <workItem from="1752459731671" duration="220000" />
      <workItem from="1752627481686" duration="811000" />
      <workItem from="1752642743803" duration="1199000" />
      <workItem from="1752712695482" duration="14000" />
      <workItem from="1752712771240" duration="17463000" />
      <workItem from="1752801138594" duration="672000" />
      <workItem from="1752817026454" duration="2124000" />
      <workItem from="1752819899590" duration="514000" />
      <workItem from="1752882296344" duration="338000" />
      <workItem from="1752887480018" duration="12949000" />
      <workItem from="1752912574458" duration="8000" />
      <workItem from="1753055340200" duration="10272000" />
      <workItem from="1753066813466" duration="566000" />
      <workItem from="1753067402249" duration="11835000" />
      <workItem from="1753142321666" duration="113000" />
      <workItem from="1753142477497" duration="11722000" />
    </task>
    <task id="LOCAL-00023" summary="新增红墨水导出接口以及条件查询接口">
      <created>1731906328369</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1731906328369</updated>
    </task>
    <task id="LOCAL-00024" summary="新增红墨水导出接口以及条件查询接口">
      <created>1731907450810</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1731907450810</updated>
    </task>
    <task id="LOCAL-00025" summary="优化查詢紅墨水品質記錄接口设置图片总数量">
      <created>1734312061244</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1734312061244</updated>
    </task>
    <task id="LOCAL-00026" summary="AGV查询接口">
      <created>1740461399196</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1740461399196</updated>
    </task>
    <task id="LOCAL-00027" summary="完成AGV任务、日志查询接口">
      <created>1740532320546</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1740532320546</updated>
    </task>
    <task id="LOCAL-00028" summary="优化查询AGV接口的换行符和回车符的数据">
      <created>1740625822077</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1740625822077</updated>
    </task>
    <task id="LOCAL-00029" summary="优化點檢查询接口">
      <created>1740727902271</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1740727902271</updated>
    </task>
    <task id="LOCAL-00030" summary="优化查询主要信息接口-&gt;sbu2section表查询">
      <created>1740798056163</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1740798056164</updated>
    </task>
    <task id="LOCAL-00031" summary="完成生产自主检查的查询接口和下载模板接口">
      <created>1742369800257</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1742369800258</updated>
    </task>
    <task id="LOCAL-00032" summary="新增文件上傳接口">
      <created>1742376573035</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1742376573035</updated>
    </task>
    <task id="LOCAL-00033" summary="生产点检的料号、人员、项目的更新、删除接口">
      <created>1742453193054</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1742453193054</updated>
    </task>
    <task id="LOCAL-00034" summary="完成自主生產點檢接口">
      <created>1742793109341</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1742793109341</updated>
    </task>
    <task id="LOCAL-00035" summary="新增projectRemark字段">
      <created>1743131651335</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1743131651335</updated>
    </task>
    <task id="LOCAL-00036" summary="新增导出接口">
      <created>1743231642622</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1743231642622</updated>
    </task>
    <task id="LOCAL-00037" summary="人員新增兩個字段">
      <created>1744245915118</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1744245915118</updated>
    </task>
    <task id="LOCAL-00038" summary="新增客戶條件查詢接口">
      <created>1744426363781</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1744426363781</updated>
    </task>
    <task id="LOCAL-00039" summary="優化保存文件接口，新增客戶條件文件下載接口，新增summary上傳下載接口">
      <created>1744599538840</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1744599538840</updated>
    </task>
    <task id="LOCAL-00040" summary="summary文件實體類">
      <created>1744599598552</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1744599598552</updated>
    </task>
    <task id="LOCAL-00041" summary="优化客户条件主要信息查询接口，使其返回fileUrl">
      <created>1744600687704</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1744600687704</updated>
    </task>
    <task id="LOCAL-00042" summary="新增每月1號自動發送最新summary文件給所有郵箱的功能">
      <created>1744680646043</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1744680646043</updated>
    </task>
    <task id="LOCAL-00043" summary="清理未使用的代碼">
      <created>1744680908409</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1744680908409</updated>
    </task>
    <task id="LOCAL-00044" summary="新增每月定时跟催未更新客户条件记录表的功能">
      <created>1744686390060</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1744686390060</updated>
    </task>
    <task id="LOCAL-00045" summary="發送郵件新增微智造鏈接">
      <created>1744697432730</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1744697432730</updated>
    </task>
    <task id="LOCAL-00046" summary="生产自主检查项目新增4个字段；上传点检项目数据时对频率进行了限制">
      <created>1744768032590</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1744768032590</updated>
    </task>
    <task id="LOCAL-00047" summary="修復因爲lombok問題而無法獲取到點檢項目某個字段的bug">
      <created>1744879257657</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1744879257657</updated>
    </task>
    <task id="LOCAL-00048" summary="修复由于数据库中生产自主检查用户表多条相同用户导致的bug">
      <created>1744937993439</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1744937993439</updated>
    </task>
    <task id="LOCAL-00049" summary="暂时注释掉定时任务代码">
      <created>1745488383156</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1745488383156</updated>
    </task>
    <task id="LOCAL-00050" summary="更新频率条件为次/班">
      <created>1749195443563</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1749195443563</updated>
    </task>
    <task id="LOCAL-00051" summary="根据id下载文件">
      <created>1750664420530</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1750664420530</updated>
    </task>
    <task id="LOCAL-00052" summary="处理文件下载bug">
      <created>1750665172269</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1750665172269</updated>
    </task>
    <task id="LOCAL-00053" summary="修改id下载文件的接口返回方式">
      <created>1750666451732</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1750666451732</updated>
    </task>
    <task id="LOCAL-00054" summary="取消客户条件的task的注销">
      <created>1751874445761</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1751874445762</updated>
    </task>
    <task id="LOCAL-00055" summary="新增自主检查上传的频率">
      <created>1752459791503</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1752459791503</updated>
    </task>
    <task id="LOCAL-00056" summary="新增頻率單位">
      <created>1752628134745</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1752628134746</updated>
    </task>
    <task id="LOCAL-00057" summary="優化樹工具、搭建rdProject功能模塊基礎">
      <created>1752819136845</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1752819136846</updated>
    </task>
    <task id="LOCAL-00058" summary="新增研究項目協同管理查詢接口">
      <created>1752893278432</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1752893278432</updated>
    </task>
    <task id="LOCAL-00059" summary="重新實現了項目協同管理的task查詢接口，使其更符合邏輯。">
      <created>1752906315355</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1752906315355</updated>
    </task>
    <task id="LOCAL-00060" summary="繼續重寫項目協同管理task查詢接口">
      <created>1753063654049</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1753063654050</updated>
    </task>
    <task id="LOCAL-00061" summary="項目協同管理task查詢接口修改為分頁">
      <created>1753066290294</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1753066290294</updated>
    </task>
    <task id="LOCAL-00062" summary="修改項目協同管理的task查詢接口為分頁查詢">
      <created>1753066511041</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1753066511041</updated>
    </task>
    <task id="LOCAL-00063" summary="修改項目協同管理的task查詢接口為分頁查詢">
      <created>1753067261221</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1753067261221</updated>
    </task>
    <task id="LOCAL-00064" summary="修復bug">
      <created>1753067498537</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1753067498537</updated>
    </task>
    <task id="LOCAL-00065" summary="修復idea奇奇怪怪的bug">
      <created>1753067714852</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1753067714852</updated>
    </task>
    <task id="LOCAL-00066" summary="git">
      <created>1753067797015</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1753067797015</updated>
    </task>
    <task id="LOCAL-00067" summary="修復git的Bug">
      <created>1753067851749</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1753067851749</updated>
    </task>
    <task id="LOCAL-00068" summary="优化查询主要信息接口以及不点检查询接口">
      <created>1753067888372</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1753067888372</updated>
    </task>
    <task id="LOCAL-00069" summary="新增歷史動態功能">
      <created>1753078999282</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1753078999282</updated>
    </task>
    <task id="LOCAL-00070" summary="簡單完成新增項目協同管理任務樹功能">
      <created>1753082691319</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1753082691319</updated>
    </task>
    <task id="LOCAL-00071" summary="將歷史動態查詢接口改造為分頁查詢">
      <created>1753083506825</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1753083506825</updated>
    </task>
    <option name="localTasksCounter" value="72" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="HEAD" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/liguobin" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="生产自主检查项目新增4个字段；上传点检项目数据时对频率进行了限制" />
    <MESSAGE value="修復因爲lombok問題而無法獲取到點檢項目某個字段的bug" />
    <MESSAGE value="修复由于数据库中生产自主检查用户表多条相同用户导致的bug" />
    <MESSAGE value="暂时注释掉定时任务代码" />
    <MESSAGE value="更新频率条件为次/班" />
    <MESSAGE value="根据id下载文件" />
    <MESSAGE value="处理文件下载bug" />
    <MESSAGE value="修改id下载文件的接口返回方式" />
    <MESSAGE value="取消客户条件的task的注销" />
    <MESSAGE value="新增自主检查上传的频率" />
    <MESSAGE value="新增頻率單位" />
    <MESSAGE value="優化樹工具、搭建rdProject功能模塊基礎" />
    <MESSAGE value="新增研究項目協同管理查詢接口" />
    <MESSAGE value="重新實現了項目協同管理的task查詢接口，使其更符合邏輯。" />
    <MESSAGE value="繼續重寫項目協同管理task查詢接口" />
    <MESSAGE value="項目協同管理task查詢接口修改為分頁" />
    <MESSAGE value="修改項目協同管理的task查詢接口為分頁查詢" />
    <MESSAGE value="修復bug" />
    <MESSAGE value="修復idea奇奇怪怪的bug" />
    <MESSAGE value="git" />
    <MESSAGE value="修復git的Bug" />
    <MESSAGE value="优化查询主要信息接口以及不点检查询接口" />
    <MESSAGE value="新增歷史動態功能" />
    <MESSAGE value="簡單完成新增項目協同管理任務樹功能" />
    <MESSAGE value="將歷史動態查詢接口改造為分頁查詢" />
    <option name="LAST_COMMIT_MESSAGE" value="將歷史動態查詢接口改造為分頁查詢" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/agv/controller/AGVTaskListController.java</url>
          <line>33</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/procedurefileuploading/controller/ProcedureFileUploadController.java</url>
          <line>130</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/produceInspection/service/impl/ProduceInspectionPicUrlServiceImpl.java</url>
          <line>45</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/produceInspection/service/impl/ProduceInspectionServiceImpl.java</url>
          <line>115</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/backend/src/main/java/cc/mrbird/febs/produceInspection/service/impl/ProduceInspectionServiceImpl.java</url>
          <line>139</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>