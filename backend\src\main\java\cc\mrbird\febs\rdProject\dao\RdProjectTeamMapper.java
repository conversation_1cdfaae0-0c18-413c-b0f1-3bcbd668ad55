package cc.mrbird.febs.rdProject.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import cc.mrbird.febs.rdProject.entity.RdProjectTeam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的團隊表數據訪問接口
 */
@DS("primary")
public interface RdProjectTeamMapper {

    // 插入團隊信息
    int insertRdProjectTeam(RdProjectTeam rdProjectTeam);

    // 根據團隊ID刪除團隊信息
    int deleteRdProjectTeamById(@Param("teamId") Long teamId);

    // 更新團隊信息
    int updateRdProjectTeam(RdProjectTeam rdProjectTeam);

    // 根據團隊ID查詢團隊信息
    RdProjectTeam selectRdProjectTeamById(@Param("teamId") Long teamId);

    // 根據團隊名稱查詢團隊信息
    RdProjectTeam selectRdProjectTeamByName(@Param("teamName") String teamName);

    // 根據廠區查詢團隊列表
    List<RdProjectTeam> selectRdProjectTeamBySiteArea(@Param("siteArea") String siteArea);

    // 根據產品処查詢團隊列表
    List<RdProjectTeam> selectRdProjectTeamBySbu(@Param("sbu") String sbu);

    // 根據機能查詢團隊列表
    List<RdProjectTeam> selectRdProjectTeamByFunctions(@Param("functions") String functions);

    // 根據課別查詢團隊列表
    List<RdProjectTeam> selectRdProjectTeamBySection(@Param("section") String section);

    // 查詢所有團隊信息
    List<RdProjectTeam> selectAllRdProjectTeam();
}
