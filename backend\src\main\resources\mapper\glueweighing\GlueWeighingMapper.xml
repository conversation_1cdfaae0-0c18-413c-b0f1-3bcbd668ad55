<?xml version="1.0" encoding ="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.glueweighing.dao.GlueWeighingMapper">

<insert id="insertByWeight">
        USE Schedule
    INSERT INTO  weight(uuid,pl,enginery,discern,production,productionLine,classInfo,recordDate,recordTime,frequency,temperature,otherTime,washValve,machineNumber,glueChange)
    values (#{uuid},#{pl},#{enginery},#{discern},#{production},#{productionLine},#{classInfo},#{recordDate},#{recordTime},#{frequency},#{temperature},#{otherTime},#{washValve},#{machineNumber},#{glueChange})
 </insert>

<insert id="insertByWeightAnbnormal">
    USE Schedule
    INSERT INTO weight_uuid(uuid,measurementIndex,measurementNum,specificationMin,specificationMax,oldWeight,newWeight,glueWeight,judgementResult)values
    <foreach collection="collection" separator="," item="item">
        (#{item.uuid},#{item.measurementIndex},#{item.measurementNum},#{item.specificationMin},#{item.specificationMax},#{item.oldWeight},#{item.newWeight},#{item.glueWeight},#{item.judgementResult})
    </foreach>
    </insert>

<select id="selectPageConfigure" resultType="cc.mrbird.febs.glueweighing.entity.vo.AllDataVo">
    USE Schedule
    SELECT a.uuid,production,productionLine,machineNumber,b.measurementIndex,b.measurementNum,recordTime,
    b.specificationMax,b.specificationMin,b.oldWeight,b.newWeight,frequency,temperature,washValve,
    glueChange,b.glueWeight,b.judgementResult,a.otherTime,a.recordDate,classInfo
    FROM weight AS a LEFT JOIN weight_uuid AS b
    ON a.uuid = b.uuid
    <where>
        <if test="discern != null and discern != ''">
            AND discern=#{discern}
        </if>
        <if test="production != null and production != ''">
            AND production=#{production}
        </if>
        <if test="productionLine != null and productionLine != ''">
            AND productionLine=#{productionLine}
        </if>
        <if test="classInfo != null and classInfo != ''">
            AND classInfo=#{classInfo}
        </if>
        <if test="measurementIndex != null and measurementIndex != ''">
            AND b.measurementIndex=#{measurementIndex}
        </if>
        <if test="recordDateStart != null and recordDateStart != ''">
            AND a.recordDate &gt;= CONVERT(date,#{recordDateStart})
        </if>
        <if test="recordDateEnd != null and recordDateEnd != ''">
            AND a.recordDate &lt;= CONVERT(date,#{recordDateEnd})
        </if>
    </where>
   <!-- ORDER BY productionLine,machineNumber,recordDate ASC -->
    ORDER BY id desc
    </select>

<select id="selectProductRange" resultType="cc.mrbird.febs.glueweighing.entity.PageConfigure">
    USE Schedule
    SELECT DISTINCT Discern,Production,ProductionLine FROM T_Configure_Equipment WHERE PL='IDS3' AND Discern=N'裝配三課' AND ProductionLine IN ('TC','TD','T4')
    </select>

<update id="editByGlue" >
    USE Schedule
    UPDATE weight set frequency=#{frequency},temperature=#{temperature},otherTime=#{otherTime},
    washValve=#{washValve},glueChange=#{glueChange}
    WHERE uuid=#{uuid}
    </update>

<select id="getModel" resultType="java.lang.String">
    SELECT  noumenon
    FROM [Schedule].[dbo].[t_weight_config] WHERE productionLine=#{productionLine}
    </select>

<select id="getModelBy1" resultType="java.lang.String">
    SELECT  rubber
    FROM [Schedule].[dbo].[t_weight_config] WHERE productionLine=#{productionLine}
    </select>

<select id="selectUpdate" resultType="cc.mrbird.febs.glueweighing.entity.vo.AllDataVo">
    USE Schedule
    SELECT a.uuid,production,productionLine,machineNumber,b.measurementIndex,b.measurementNum,recordTime,
           b.specificationMax,b.specificationMin,b.oldWeight,b.newWeight,frequency,temperature,washValve,
           glueChange,b.glueWeight,b.judgementResult,a.otherTime,a.recordDate,classInfo
    FROM weight AS a INNER JOIN weight_uuid AS b
    ON a.uuid = b.uuid
    WHERE a.uuid=#{uuid}
    </select>

<delete id="deleteUuidData">
    delete  weight_uuid  WHERE uuid=#{uuid}
    </delete>

<update id="editDataByUUID">
    USE Schedule
    <foreach collection="weightAnbNormal" item="date">
    update  weight_uuid
    <set>
        uuid =#{date.uuid},glueWeight=#{date.glueWeight},judgementResult=#{date.judgementResult},
        oldWeight=#{date.oldWeight},measurementIndex=#{date.measurementIndex},measurementNum=#{date.measurementNum},
        specificationMax=#{date.specificationMax},specificationMin=#{date.specificationMin},newWeight=#{date.newWeight}
    </set>
    <where>
        uuid = #{date.uuid}
    </where>
    </foreach>
    </update>
</mapper>