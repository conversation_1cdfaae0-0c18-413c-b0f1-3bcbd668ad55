package cc.mrbird.febs.lessDaysheet.dao;

import cc.mrbird.febs.lessDaysheet.entity.BadStatement;
import cc.mrbird.febs.lessDaysheet.entity.BadStatementUUID;
import cc.mrbird.febs.lessDaysheet.entity.dto.BadStatementEdit;
import cc.mrbird.febs.lessDaysheet.entity.dto.BadStatementQcDto;
import cc.mrbird.febs.lessDaysheet.entity.dto.BadStatementSelectDto;
import cc.mrbird.febs.lessDaysheet.entity.dto.BadStatementUpdateDto;
import cc.mrbird.febs.lessDaysheet.entity.vo.BadStatementSelectVo;
import cc.mrbird.febs.lessDaysheet.entity.vo.BadStatementVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
@DS("mssql")
public interface  BadStatementMapper {
    //insert
    boolean insertAll(BadStatement badStatement);

    //insert
    boolean insertByBadStatementUUID(List<BadStatementUUID> badStatementUUID);

    //select
    List<BadStatementVo> selectAllByBadStatement(String uuid);

    //checkByLineLeader
    boolean updateByStatementOnLineLeader(BadStatementUpdateDto badStatementUpdateDto);

    //checkByQcName
    boolean updateByStatementOnQc(List<BadStatementQcDto> badStatementQcDto);

    //selectVo  一个对象
    List<BadStatementSelectVo> selectBadStatementVo(BadStatementSelectDto badStatementSelectDto);

    //selectVo   一个list对象
    //update
    List<BadStatementUUID> selectBadItem(String uuid);

    List<String>          selectQCName(Integer id);


    /**
    *
    * <AUTHOR>
    * @date 2023-06-17
     * 获取工号
     */
    int getRole(String workId);

    boolean badStatementByEdit(BadStatementEdit badStatementEdit);

    boolean badStatementUUIDByEdit(BadStatementUUID badStatementUUID);

    boolean delByBadStatementUUID(String uuid);
}
