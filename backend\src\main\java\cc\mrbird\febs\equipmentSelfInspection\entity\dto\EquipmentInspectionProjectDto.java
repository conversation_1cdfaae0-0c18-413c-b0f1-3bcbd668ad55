package cc.mrbird.febs.equipmentSelfInspection.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 李囯斌
 * Date: 2024/9/10
 * Time: 14:19
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class EquipmentInspectionProjectDto {
    @ApiModelProperty(value = "點檢項目ID",position = 0)
    private Integer projectId;
    @ApiModelProperty(value = "設備ID",position = 1)
    private String equipmentId;
    @ApiModelProperty(value = "點檢類型",position = 2)
    private String selfInspectionType;
    @ApiModelProperty(value = "點檢狀態",position = 3)
    private String status;
    @ApiModelProperty(value = "點檢部位",position = 4)
    private String selfInspectionPosition;
    @ApiModelProperty(value = "點檢標準、基準",position = 5)
    private String selfInspectionStandard;
    @ApiModelProperty(value = "點檢值",position = 6)
    private String djValue;
    @ApiModelProperty(value = "點檢結果",position = 7)
    private String djResult;
    @ApiModelProperty(value = "點檢備注",position = 8)
    private String djNotes;
    @ApiModelProperty(value = "產品処",position = 9)
    private String pl;
    @ApiModelProperty(value = "機能",position = 10)
    private String enginery;
    @ApiModelProperty(value = "課別",position = 11)
    private String discern;
    @ApiModelProperty(value = "系列",position = 12)
    private String production;
    @ApiModelProperty(value = "綫體",position = 13)
    private String productionLine;
    @ApiModelProperty(value = "點檢日期",position = 14)
    private String date;
}
