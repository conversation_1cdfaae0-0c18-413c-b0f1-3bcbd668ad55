package cc.mrbird.febs.equipmentSelfInspection.service.impl;

import cc.mrbird.febs.equipmentSelfInspection.dao.EquipmentInspectionMaintenanceMapper;
import cc.mrbird.febs.equipmentSelfInspection.entity.EquipmentInspectionProjectData;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionMainInfoDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionProjectDto;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentInspectionQueryParam;
import cc.mrbird.febs.equipmentSelfInspection.service.EquipmentInspectionMaintenanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 李国斌
 * Date: 2024-09-10
 * Time: 下午 01:06
 */
@Service
public class EquipmentInspectionMaintenanceImpl implements EquipmentInspectionMaintenanceService {
    private final EquipmentInspectionMaintenanceMapper mapper;

    @Autowired
    public EquipmentInspectionMaintenanceImpl(EquipmentInspectionMaintenanceMapper emapper) {
        this.mapper = emapper;
    }
    @Override
    public List<String> queryDiscern(String SBU, String functions) {
        List<String> list =mapper.selectDiscern(SBU,functions);
        return list;
    }

    @Override
    public List<String> queryProduction(String SBU, String functions, String discern) {
        List<String> list = mapper.selectProduction(SBU, functions, discern);
        return list;
    }

    @Override
    public List<String> queryProductionLine(String SBU, String functions, String discern, String production) {
        List<String> list =mapper.selectProductionLine(SBU, functions, discern,production);
        return list;
    }

    @Override
    public List<String> querySelfInspectionType() {
        List<String> list =mapper.selectSelfInspectionType();
        return list;
    }

    @Override
    public List<EquipmentInspectionMainInfoDto> queryEquipmentInspectionMainInfo(EquipmentInspectionQueryParam equipmentQueryParam) {
        List<EquipmentInspectionMainInfoDto> list =mapper.selectInspectionMainInfo(equipmentQueryParam);

        return list;
    }

    @Override
    public EquipmentInspectionMainInfoDto queryIsNotDJ(EquipmentInspectionQueryParam equipmentInspectionQueryParam) {
        EquipmentInspectionMainInfoDto dto=mapper.queryIsNotDJ(equipmentInspectionQueryParam);
        return dto;
    }

    @Override
    public List<EquipmentInspectionProjectDto> queryEquipmentInspectionProjectsByEquipmentIDByEquipmentIDAndType(String equipmentID,String djType) {
        return mapper.selectEquipmentInspectionProjectsByEquipmentIDAndType(equipmentID,djType);
    }

    @Override
    public EquipmentInspectionProjectData queryEquipmentInspectionProjectData(String date, String selfInspectionType, String equipmentID) {
        return mapper.selectInspectionProjectData(date,selfInspectionType,equipmentID);
    }

    @Override
    public List<String> querySBU() {
        return mapper.selectSBU();
    }

    @Override
    public List<String> queryFunctions(String sbu) {
        return mapper.selectFunctions(sbu);
    }


    @Override
    public EquipmentInspectionProjectDto queryByUserId(String userID) {
        return mapper.selectRYXXByUserID(userID);
    }

    @Override
    public List<String> queryDiscernBySBU(String sbu) {
        return mapper.queryDiscernBySBU(sbu);
    }


}

