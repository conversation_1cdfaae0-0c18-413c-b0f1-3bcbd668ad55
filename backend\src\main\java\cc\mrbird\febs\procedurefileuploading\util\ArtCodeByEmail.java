package cc.mrbird.febs.procedurefileuploading.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
* <AUTHOR>
* @date: 2024-07-22
* @Time: 下午 01:33
*
*/
public class ArtCodeByEmail {
    public  String getCode(String production,String productionLine,String secondName,String fileVersion,
                            String getUploadUser,String remark
                          ) throws UnsupportedEncodingException {
        String code ="<!DOCTYPE html>\n" +
                "<html lang=\"zh-Hant\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>备件寿命到期预警</title>\n" +
                "</head>\n" +
                "<body style=\"font-family: 'Arial', sans-serif; background-color: #f4f4f9; margin: 0; padding: 20px;\">\n" +
                "<div style=\"background-color: #ffffff; width: 80%; margin: auto; padding: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);\">\n" +
                "    <div style=\"background-color: #0073e6; color: #ffffff; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;\">\n" +
                "        <h1 style=\"font-size: 24px; margin: 0;\">"+production+"-"+productionLine+"-"+"設備程式版次更新提示</h1>\n" +
                "    </div>\n" +
                "    <div style=\"margin-top: 20px; line-height: 1.6; padding: 0 20px;\">\n" +
                "        <p>尊敬的管理者，"+secondName+"</p>\n" +
                "        <p style=\"font-size: 16px;\">您好！設備標準程式版次已更新，请留意以下详情：</p>\n" +
                "        <ul style=\"font-size: 16px; padding-left: 20px;\">\n" +
                "            <li>更改文件版號："+fileVersion+"</li>\n" +
                "            <li>變更人："+getUploadUser+"</li>\n" +
                "            <li>變更說明："+remark+"</li>\n" +
                "        </ul>\n" +
                "    </div>\n" +
                "    <div style=\"margin-top: 20px; text-align: center; color: #777777; font-size: 14px;\">\n" +
                "        <p>此邮件为系统自动发送，请勿直接回复。</p>\n" +
                "    </div>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>\n";
        code= URLEncoder.encode(code,String.valueOf(StandardCharsets.UTF_8));
        return   code;
    }

}