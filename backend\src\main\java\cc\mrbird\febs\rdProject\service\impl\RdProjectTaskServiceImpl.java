package cc.mrbird.febs.rdProject.service.impl;

import cc.mrbird.febs.common.annotation.RdProjectLog;
import cc.mrbird.febs.common.domain.Tree;
import cc.mrbird.febs.common.exception.LimitAccessException;
import cc.mrbird.febs.common.utils.FebsUtil;
import cc.mrbird.febs.common.utils.TreeUtil;
import cc.mrbird.febs.rdProject.dao.RdProjectTaskMainInfoMapper;
import cc.mrbird.febs.rdProject.dao.RdProjectTaskMapper;
import cc.mrbird.febs.rdProject.dao.RdProjectUserExtraMapper;
import cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo;
import cc.mrbird.febs.rdProject.entity.RdProjectUserExtra;
import cc.mrbird.febs.rdProject.entity.vo.RdProjectTaskMainInfoVO;
import cc.mrbird.febs.rdProject.service.RdProjectTaskService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理服務實現類
 */
@Service
public class RdProjectTaskServiceImpl implements RdProjectTaskService {

    @Autowired
    private RdProjectTaskMapper mapper;

    @Autowired
    private RdProjectTaskMainInfoMapper rdProjectTaskMainInfoMapper;

    @Autowired
    private RdProjectUserExtraMapper rdProjectUserExtraMapper;

    @Override
    public List<String> querySiteAreaList() {
        return mapper.selectSiteAreaList();
    }

    @Override
    public List<String> querySbuBySiteArea(String siteArea) {
        return mapper.selectSbuBySiteArea(siteArea);
    }

    @Override
    public List<String> queryFunctionsBySiteAreaAndSbu(String siteArea, String sbu) {
        return mapper.selectFunctionsBySiteAreaAndSbu(siteArea, sbu);
    }

    @Override
    public List<String> querySectionBySiteAreaAndSbuAndFunctions(String siteArea, String sbu, String functions) {
        return mapper.selectSectionBySiteAreaAndSbuAndFunctions(siteArea, sbu, functions);
    }

    @Override
    public RdProjectUserExtra queryUserExtra() {
        String workerId = FebsUtil.getCurrentUser().getUsername();//t_user的username本質是workerId
        if(StringUtils.isEmpty(workerId))
        {
            return null;
        }
        return rdProjectUserExtraMapper.selectRdProjectUserExtraByWorkerId(workerId);
    }

    // 查詢研究項目協同管理主要信息
    @RdProjectLog("查詢任務")
    @Override
    public PageInfo<Tree<RdProjectTaskMainInfo>> queryRdProjectTaskMainInfo(RdProjectTaskMainInfo rdProjectTaskMainInfo, int pageNum, int pageSize)throws LimitAccessException {
        Subject subject = SecurityUtils.getSubject();
        //startPage一定要放在if裏面，因爲subject.isPermitted會調用一次查詢
        if(subject.isPermitted("rd_project:view:all")){
            //拥有查询全部的权限
//            List<RdProjectTaskMainInfo> rdProjectTaskMainInfos = this.mapper.queryRdProjectTaskMainInfo(rdProjectTaskMainInfo);
            PageHelper.startPage(pageNum,pageSize);
            List<String> rootIds = this.mapper.selectTaskTreeRootIds(rdProjectTaskMainInfo);
            return getTreePageInfo(rdProjectTaskMainInfo, rootIds);
        }else if(subject.isPermitted("rd_project:view:self")){
            //只拥有查询自己的数据的权限
            PageHelper.startPage(pageNum,pageSize);
            String workerId=FebsUtil.getCurrentUser().getUsername();
//            List<RdProjectTaskMainInfo> rdProjectTaskMainInfos = this.mapper.selectTaskTreesByWorkerId(workerId, rdProjectTaskMainInfo);
            List<String> rootIds = this.mapper.selectTaskTreeRootIdsByWorkerId(workerId, rdProjectTaskMainInfo);
            return getTreePageInfo(rdProjectTaskMainInfo, rootIds);
        }else {
            throw new LimitAccessException("not allow to view!");
        }
    }



    @NotNull
    private PageInfo<Tree<RdProjectTaskMainInfo>> getTreePageInfo(RdProjectTaskMainInfo rdProjectTaskMainInfo, List<String> rootIds) {
        //保留分頁信息
        PageInfo<String> idsPageInfo = new PageInfo<>(rootIds);
        List<RdProjectTaskMainInfo> rdProjectTaskMainInfos = this.mapper.selectTaskTreesByRootIds(rootIds, rdProjectTaskMainInfo);

        //複製分頁信息
        PageInfo<Tree<RdProjectTaskMainInfo>> treePageInfo = new PageInfo<>(Collections.singletonList(buildRdProjectTaskMainInfoTrees(rdProjectTaskMainInfos)));
        BeanUtils.copyProperties(idsPageInfo, treePageInfo);
        treePageInfo.setList(Collections.singletonList(buildRdProjectTaskMainInfoTrees(rdProjectTaskMainInfos)));
        return treePageInfo;
    }

    // 构建task树
    private Tree<RdProjectTaskMainInfo> buildRdProjectTaskMainInfoTrees(List<RdProjectTaskMainInfo> rdProjectTaskMainInfoList) {
        //1、查询出遗漏的节点
        List<Long> missingTreePathTaskIds = findMissingTreePathTaskIds(rdProjectTaskMainInfoList);
        if(!missingTreePathTaskIds.isEmpty()) {
            List<RdProjectTaskMainInfo> missingTreePathTaskInfoList = this.mapper.selectBatchByTaskIds(missingTreePathTaskIds);
            rdProjectTaskMainInfoList.addAll(missingTreePathTaskInfoList);
        }
        //2、建树
        List<Tree<RdProjectTaskMainInfo>> trees = new ArrayList<>();
        rdProjectTaskMainInfoList.forEach(task -> {
            Tree<RdProjectTaskMainInfo> tree = new Tree<>();
            tree.setId(task.getTaskId().toString());
            tree.setParentId(task.getParentId().toString());
            tree.setData(task);
            trees.add(tree);
        });

        Tree<RdProjectTaskMainInfo> rdProjectTaskMainInfoTree = TreeUtil.buildOptimized(trees);
        return rdProjectTaskMainInfoTree;
    }

    // 找出在rdProjectTaskMainInfos列表中，根据treePath所引用的taskId，有哪些taskId没有出现在当前列表的taskId中
    public List<Long> findMissingTreePathTaskIds(List<RdProjectTaskMainInfo> rdProjectTaskMainInfos) {
        // 1. 收集当前列表中所有存在的taskId（去重）
        Set<Long> existingTaskIds = rdProjectTaskMainInfos.stream()
                .map(RdProjectTaskMainInfo::getTaskId)
                .collect(Collectors.toSet());

        // 2. 收集所有treePath中引用的taskId（去重）
        Set<Long> referencedTaskIds = new HashSet<>();
        for (RdProjectTaskMainInfo task : rdProjectTaskMainInfos) {
            if (task.getTreePath() != null && !task.getTreePath().isEmpty()) {
                // 解析treePath字符串（格式如"123,456,789"）
                String[] pathIds = task.getTreePath().split(",");
                for (String idStr : pathIds) {
                    try {
                        long taskId = Long.parseLong(idStr.trim());
                        // 避免将0加入引用集合（0表示根节点）
                        if (taskId != 0L) {
                            referencedTaskIds.add(taskId);
                        }
                    } catch (NumberFormatException e) {
                        // 忽略格式不正确的ID（例如空字符串或非数字）
                    }
                }
            }
        }

        // 3. 找出被引用但不在当前列表中的taskId（转换为List返回）
        return referencedTaskIds.stream()
                .filter(refId -> !existingTaskIds.contains(refId))
                .collect(Collectors.toList());
    }

    // 解析並保存任務樹
    @DSTransactional
    @Override
    public void saveTaskTree(String treeJson, Map<String, MultipartFile> fileMap) throws IOException {
        // 1. 解析JSON树
        ObjectMapper mapper = new ObjectMapper();
        Tree<RdProjectTaskMainInfoVO> root = mapper.readValue(
                treeJson,
                new TypeReference<Tree<RdProjectTaskMainInfoVO>>() {}
        );

        // 2. 绑定附件到节点
        traverseAndBindFiles(root, fileMap);

        // 3. 递归保存（带事务回滚）
        saveTreeRecursive(root, null,"");
    }

    // 附件绑定方法
    private void traverseAndBindFiles(Tree<RdProjectTaskMainInfoVO> node, Map<String, MultipartFile> fileMap) {
        String nodeId = node.getId();

        // 收集该节点的所有附件 (例如：file_123_0, file_123_1)
        List<MultipartFile> attachments = fileMap.entrySet().stream()
                .filter(e -> e.getKey().startsWith("file_" + nodeId + "_"))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        // 设置到节点数据中
        node.getData().setAttachments(attachments);

        // 递归处理子节点
        if (node.getChildren() != null) {
            node.getChildren().forEach(child -> traverseAndBindFiles(child, fileMap));
        }
    }

    // 递归保存树结构
    private void saveTreeRecursive(Tree<RdProjectTaskMainInfoVO> node, Long parentId,String treePath) {
        RdProjectTaskMainInfoVO task = node.getData();
        task.setParentId(parentId != null ? parentId : 0L); // 设置父ID
        String workerId = FebsUtil.getCurrentUser().getUsername();
        task.setCreatorWorkerId(workerId); //設置創建者工號
        //設置產區等信息
        List<RdProjectUserExtra> rdProjectUserExtras = rdProjectUserExtraMapper.selectRdProjectUserExtraByUserName(workerId);
        if(rdProjectUserExtras!=null&&!rdProjectUserExtras.isEmpty()){
            RdProjectUserExtra userExtra = rdProjectUserExtras.get(0);
            task.setSiteArea(userExtra.getSiteArea());
            task.setSbu(userExtra.getSbu());
            task.setFunctions(userExtra.getFunctions());
            task.setSection(userExtra.getSection());
        }


        // 1.1 保存任务主信息
        rdProjectTaskMainInfoMapper.insertRdProjectTaskMainInfo(task);
        Long taskId = task.getTaskId();
        // 1.2 设置tree_path(以根节点到自己的路径)
        if(StringUtils.isEmpty(treePath)){
            //如果该节点为根节点，则只有自己的ID
            treePath=taskId.toString();
        }else {
            treePath=treePath+","+taskId.toString();
        }
        // 1.3 更新任務主信息中的tree_path
        task.setTreePath(treePath);
        rdProjectTaskMainInfoMapper.updateRdProjectTaskMainInfo(task);

        // 2. 保存该节点的附件（如果有）
        if (task.getAttachments() != null) {
            //TODO:完成文件批量插入和保存在本地文件夹下
//            attachmentService.saveAttachments(taskId, task.getAttachments());
        }

        // 3.TODO:任務指定責任人

        // 4. 递归处理子节点
        if (node.getChildren() != null) {
            String finalTreePath = treePath;
            node.getChildren().forEach(child -> saveTreeRecursive(child, taskId, finalTreePath));
        }
    }
}
