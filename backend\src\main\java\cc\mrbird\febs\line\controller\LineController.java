package cc.mrbird.febs.line.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.line.dao.LineMapper;
import cc.mrbird.febs.line.service.ILineService;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/line")
@RequiresPermissions("zyc")
public class LineController {
    @Autowired
    private ILineService iLineService;
    @Autowired
    private LineMapper lineMapper;
    @ApiOperation(value = "根据工号查询姓名",notes = "根据工号查询姓名")
    @GetMapping("/queryName")
    public FebsResponse queryNameByCardId(@RequestParam("cardId")String cardId){
        String name=iLineService.queryNameByCardId(cardId);
        return new FebsResponse().data(name);
    }

    @GetMapping("/getDiscern")
    @ApiOperation(value = "查詢課別",notes = "查詢課別")
    public FebsResponse getDiscern(@RequestParam("cardId")String cardId){
            String roleId=lineMapper.queryRoleId(cardId);
            if(roleId.equals("11")||roleId.equals("80")){
                return new FebsResponse().data(lineMapper.queryDiscern(cardId));
            }else if(roleId.equals("13")){
                return  new FebsResponse().data(lineMapper.queryDiscernBySection(cardId));
            }else if(roleId.equals("12")){
                return  new FebsResponse().data(lineMapper.queryDiscernByGroup(cardId));
            }else{
                return new FebsResponse().message("not found");
            }
    }
    @GetMapping("/getProduction")
    @ApiOperation(value = "查詢系列",notes = "查詢系列")
    public FebsResponse getProduction(@RequestParam("discern")String discern,@RequestParam("cardId")String cardId){
        String roleId=lineMapper.queryRoleId(cardId);
        if(roleId.equals("11")||roleId.equals("80")){
            return new FebsResponse().data(lineMapper.queryProduction(cardId,discern));
        }else if(roleId.equals("13")){
            return  new FebsResponse().data(lineMapper.queryProductionBySection(cardId,discern));
        }else if(roleId.equals("12")){
            return  new FebsResponse().data(lineMapper.queryProductionByGroup(cardId,discern));
        }else{
            return new FebsResponse().message("not found");
        }

    }
    @GetMapping("/getLine")
    @ApiOperation(value = "查詢綫體",notes = "查詢綫體")
    public FebsResponse getLine(@RequestParam("production")String production,@RequestParam("cardId")String cardId){
        String roleId=lineMapper.queryRoleId(cardId);
        if(roleId.equals("11")||roleId.equals("80")){
            if(lineMapper.queryProducitonLine(production,cardId).isEmpty()){
                return new FebsResponse().code("200").message("查询成功").status("success").data(" ");
            }
            return new FebsResponse().code("200").message("查询成功").status("success").data(lineMapper.queryProducitonLine(production,cardId));
        }else if(roleId.equals("13")){
            if(lineMapper.queryProducitonLineBySection(production,cardId).isEmpty()){
                return new FebsResponse().code("200").message("查询成功").status("success").data(" ");
            }
            return new FebsResponse().code("200").message("查询成功").status("success").data(lineMapper.queryProducitonLineBySection(production,cardId));
        }else if(roleId.equals("12")){
            if(lineMapper.queryProducitonLineByGroup(production,cardId).isEmpty()){
                return new FebsResponse().code("200").message("查询成功").status("success").data(" ");
            }
            return new FebsResponse().code("200").message("查询成功").status("success").data((lineMapper.queryProducitonLineByGroup(production,cardId)));
        }else{
            return new FebsResponse().message("not found");
        }
    }
}
