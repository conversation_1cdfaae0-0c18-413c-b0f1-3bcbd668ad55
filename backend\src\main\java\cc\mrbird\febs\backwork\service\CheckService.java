package cc.mrbird.febs.backwork.service;
import cc.mrbird.febs.backwork.entity.CheckDto;
import cc.mrbird.febs.backwork.entity.GoBackVo;
import cc.mrbird.febs.backwork.entity.TDataBackwork;
import cc.mrbird.febs.common.domain.FebsResponse;

import java.io.IOException;

public interface CheckService {

    FebsResponse CheckByPl1(CheckDto checkDto);

    FebsResponse CheckByPl2(CheckDto checkDto);

    FebsResponse CheckByPl3(CheckDto checkDto);

    boolean insertNotNull(TDataBackwork tDataBackwork);


    FebsResponse goBackCheckByPl1(GoBackVo goBackVo) throws IOException;

    FebsResponse goBackCheckByPl2(GoBackVo goBackVo) throws IOException;

    FebsResponse goBackCheckByPl3(GoBackVo goBackVo) throws IOException;
}
