package cc.mrbird.febs.customerCondition.task;

import cc.mrbird.febs.backwork.service.Email;
import cc.mrbird.febs.customerCondition.dao.CustomerConditionMapper;
import cc.mrbird.febs.customerCondition.entity.CustomerConditionMail;
import cc.mrbird.febs.customerCondition.entity.CustomerConditionSummaryFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客戶條件記錄定時任務
 * 每月1號自動發送最新summary文件給所有郵箱
 */
@Slf4j
@Component
public class CustomerConditionScheduledTask {

    @Autowired
    private CustomerConditionMapper customerConditionMapper;

    @Autowired
    private Email email;

    @Value("${customer.condition.file.server.url}")
    private String baseUrl;

    @Value("${customer.condition.file.summary.path}")
    private String summaryPath;

    @Value("${customer.condition.file.server.access.url}")
    private String accessUrl;

    /**
     * 讀取Excel文件內容並轉換為HTML表格
     * @param filePath Excel文件路徑
     * @return HTML表格字符串
     */
    private String readExcelContentAsHtml(String filePath) {
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<table border='1' cellspacing='0' cellpadding='5' style='border-collapse: collapse; width: 100%;'>\n");

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {

            // 獲取第一個工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 處理標題行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                htmlBuilder.append("<tr><td>文件沒有有效的標題行，請直接下載文件查看。</td></tr>\n");
                htmlBuilder.append("</table>");
                return htmlBuilder.toString();
            }

            // 標題行存在，先處理標題
            int columnCount = headerRow.getLastCellNum();
            htmlBuilder.append("<tr style='background-color: #f2f2f2; font-weight: bold;'>\n");
            for (int i = 0; i < columnCount; i++) {
                Cell cell = headerRow.getCell(i);
                String cellValue = getCellValueAsString(cell);
                htmlBuilder.append("<th>").append(cellValue).append("</th>\n");
            }
            htmlBuilder.append("</tr>\n");

            // 處理數據行（最多顯示前100行，避免郵件過大）
            int maxRows = Math.min(sheet.getLastRowNum(), 100);
            for (int i = 1; i <= maxRows; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    htmlBuilder.append("<tr>\n");
                    for (int j = 0; j < columnCount; j++) {
                        Cell cell = row.getCell(j);
                        String cellValue = getCellValueAsString(cell);
                        htmlBuilder.append("<td>").append(cellValue).append("</td>\n");
                    }
                    htmlBuilder.append("</tr>\n");
                }
            }

            // 如果數據超過100行，添加提示信息
            if (sheet.getLastRowNum() > 100) {
                htmlBuilder.append("<tr><td colspan='").append(columnCount)
                        .append("' style='text-align: center;'>... (僅顯示前100行數據，完整數據請下載文件查看)</td></tr>\n");
            }

        } catch (Exception e) {
            log.error("讀取Excel文件失敗: {}", e.getMessage());
            htmlBuilder.append("<tr><td>無法讀取Excel文件內容，請直接下載文件查看。</td></tr>\n");
        }

        htmlBuilder.append("</table>");
        return htmlBuilder.toString();
    }

    /**
     * 獲取單元格的值並轉換為字符串
     * @param cell Excel單元格
     * @return 單元格值的字符串表示
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return sdf.format(cell.getDateCellValue());
                } else {
                    // 避免數字顯示為科學計數法
                    double value = cell.getNumericCellValue();
                    // 如果是整數，則去掉小數點
                    if (value == Math.floor(value)) {
                        return String.valueOf((int) value);
                    }
                    return String.valueOf(value);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    double value = cell.getNumericCellValue();
                    if (value == Math.floor(value)) {
                        return String.valueOf((int) value);
                    }
                    return String.valueOf(value);
                } catch (Exception e) {
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception ex) {
                        return "";
                    }
                }
            case BLANK:
                return "";
            case ERROR:
                return "ERROR";
            default:
                return "";
        }
    }

    /**
     * 每月1號早上8點執行
     * 自動發送最新summary文件給所有郵箱
     */
    @Scheduled(cron = "0 0 8 1 * ?")
    public void sendLatestSummaryFileToAllEmails() {
        log.info("開始執行定時任務：發送最新summary文件給所有郵箱");
        try {
            // 1. 獲取最新的summary文件
            CustomerConditionSummaryFile latestSummaryFile = customerConditionMapper.selectLatestCustomerConditionSummaryFile();
            if (latestSummaryFile == null) {
                log.warn("沒有找到summary文件，無法執行定時任務");
                return;
            }

            // 2. 獲取所有郵箱信息（不指定customerId，獲取所有郵箱）
            List<CustomerConditionMail> allMails = customerConditionMapper.selectCustomerConditionMails(null);
            if (allMails == null || allMails.isEmpty()) {
                log.warn("沒有找到郵箱信息，無法執行定時任務");
                return;
            }

            // 3. 構建郵件內容
            String summaryFileName = latestSummaryFile.getSummaryFileName();
            Date uploadDate = latestSummaryFile.getUploadDate();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = dateFormat.format(uploadDate);

            // 構建文件下載鏈接
            String fileDownloadUrl = baseUrl + "/customerCondition/downloadLatestCustomerConditionSummaryFile";

            // 構建郵件主題
            String subject = "客戶條件記錄summary文件更新通知 - " + formattedDate;

            // 讀取Excel文件內容
            String filePath = summaryPath + File.separator + summaryFileName;
            String excelContentHtml = readExcelContentAsHtml(filePath);

            // 構建郵件正文
            StringBuilder bodyBuilder = new StringBuilder();
            bodyBuilder.append("尊敬的用戶：<br><br>");
            bodyBuilder.append("客戶條件記錄系統已更新最新的summary文件，詳情如下：<br><br>");
//            bodyBuilder.append("文件名稱：").append(summaryFileName).append("<br>");
            bodyBuilder.append("更新日期：").append(formattedDate).append("<br><br>");
            bodyBuilder.append("文件內容預覽：<br><br>");
            bodyBuilder.append(excelContentHtml).append("<br><br>");
            bodyBuilder.append("您可以通過以下鏈接下載最新的summary文件：<br>");
            bodyBuilder.append("<a href=\"").append(fileDownloadUrl).append("\">點擊下載最新summary文件</a><br><br>");
            bodyBuilder.append("如有任何問題，请联系ids工業互聯網開發辦公室(郵箱同名)。<br><br>");
            bodyBuilder.append("此郵件為系統自動發送，請勿回復。<br>");
            bodyBuilder.append("微智造系統");

            String body = bodyBuilder.toString();

            // 4. 發送郵件給所有郵箱（去重）
            // 使用Set保存郵箱地址，自動去重
            Set<String> toEmailSet = new HashSet<>();
            Set<String> ccEmailSet = new HashSet<>();

            // 區分普通用戶和領導郵箱
            for (CustomerConditionMail mail : allMails) {
                if (mail.getMailAddress() != null && !mail.getMailAddress().trim().isEmpty()) {
                    if ("2".equals(mail.getUserType())) {
                        // 領導郵箱放入抄送
                        ccEmailSet.add(mail.getMailAddress().trim());
                    } else {
                        // 普通用戶郵箱放入收件人
                        toEmailSet.add(mail.getMailAddress().trim());
                    }
                }
            }

//            // 如果沒有普通用戶郵箱，則將領導郵箱作為收件人
//            if (toEmailSet.isEmpty() && !ccEmailSet.isEmpty()) {
//                toEmailSet.addAll(ccEmailSet);
//                ccEmailSet.clear();
//            }

            // 將Set轉換為逗號分隔的字符串
            StringBuilder allEmailAddresses = new StringBuilder();
            StringBuilder ccEmailAddresses = new StringBuilder();

            // 將收件人郵箱地址轉換為字符串
            for (String email : toEmailSet) {
                if (allEmailAddresses.length() > 0) {
                    allEmailAddresses.append(",");
                }
                allEmailAddresses.append(email);
            }

//            // 將抄送郵箱地址轉換為字符串
//            for (String email : ccEmailSet) {
//                if (ccEmailAddresses.length() > 0) {
//                    ccEmailAddresses.append(",");
//                }
//                ccEmailAddresses.append(email);
//            }
            // 發送郵件
            if (allEmailAddresses.length() > 0) {
                String encodedSubject = URLEncoder.encode(subject, "UTF-8");
                String encodedBody = URLEncoder.encode(body, "UTF-8");
//                String encodedCc = ccEmailAddresses.length() > 0 ? URLEncoder.encode(ccEmailAddresses.toString(), "UTF-8") : "";
                String encodedCc ="";
                email.selectEmail(allEmailAddresses.toString(), encodedSubject, encodedBody, encodedCc);
                log.info("成功發送最新summary文件郵件給所有郵箱，收件人：{}，抄送：{}", allEmailAddresses, ccEmailAddresses);
            } else {
                log.warn("沒有有效的郵箱地址，無法發送郵件");
            }
        } catch (Exception e) {
            log.error("發送最新summary文件郵件失敗", e);
        }
    }

    /**
     * 手動觸發發送郵件的方法（用於測試）
     */
    public void manualSendLatestSummaryFileToAllEmails() throws IOException {
        sendLatestSummaryFileToAllEmails();
    }

    /**
     * 每月28號早上8點執行
     * 提醒本月未更新客戶條件記錄的local cs
     */
    @Scheduled(cron = "0 0 8 28 * ?")
    public void remindLocalCsForCustomersWithoutUpdate() {
        log.info("開始執行定時任務：提醒本月未更新客戶條件記錄的local cs");
        try {
            // 1. 查詢本月未更新記錄的客戶和對應的local cs郵箱
            List<CustomerConditionMail> mailsToRemind = customerConditionMapper.selectCustomersWithoutUpdateThisMonth();
            if (mailsToRemind == null || mailsToRemind.isEmpty()) {
                log.info("本月所有客戶都已更新條件記錄，無需發送提醒");
                return;
            }

            log.info("找到{}\u500b本月未更新記錄的客戶，準備發送提醒郵件", mailsToRemind.size());

            // 2. 按郵箱地址分組，避免同一人收到多封郵件
            Map<String, List<CustomerConditionMail>> mailGroups = new HashMap<>();
            for (CustomerConditionMail mail : mailsToRemind) {
                if (mail.getMailAddress() != null && !mail.getMailAddress().trim().isEmpty()) {
                    String emailAddress = mail.getMailAddress().trim();
                    if (!mailGroups.containsKey(emailAddress)) {
                        mailGroups.put(emailAddress, new ArrayList<>());
                    }
                    mailGroups.get(emailAddress).add(mail);
                }
            }

            // 3. 對每個郵箱發送提醒
            for (Map.Entry<String, List<CustomerConditionMail>> entry : mailGroups.entrySet()) {
                String emailAddress = entry.getKey();
                List<CustomerConditionMail> customerMails = entry.getValue();

                // 構建郵件主題
                String subject = "客戶條件記錄本月未更新提醒";

                // 構建郵件正文
                StringBuilder bodyBuilder = new StringBuilder();
                bodyBuilder.append("尊敬的").append(customerMails.get(0).getUserName()).append("您好：<br><br>");
                bodyBuilder.append("系統檢測到您負責的以下客戶本月尚未更新條件記錄：<br><br>");

                // 添加客戶列表
                bodyBuilder.append("<table border='1' cellspacing='0' cellpadding='5' style='border-collapse: collapse; width: 100%;'>\n");
                bodyBuilder.append("<tr style='background-color: #f2f2f2; font-weight: bold;'>\n");
//                bodyBuilder.append("<th>客戶ID</th>\n");
                bodyBuilder.append("<th>客戶名稱</th>\n");
                bodyBuilder.append("</tr>\n");

                for (CustomerConditionMail customerMail : customerMails) {
                    bodyBuilder.append("<tr>\n");
//                    bodyBuilder.append("<td>").append(customerMail.getCustomerId()).append("</td>\n");
                    bodyBuilder.append("<td>").append(customerMail.getCustomerName()).append("</td>\n");
                    bodyBuilder.append("</tr>\n");
                }

                bodyBuilder.append("</table><br><br>");

                bodyBuilder.append("請盡快更新相關客戶的條件記錄。<br><br>");
                bodyBuilder.append("您可以通過以下鏈接進入微智造系統：<br>");
                bodyBuilder.append("<a href=\"").append(accessUrl).append("\">微智造系統</a><br><br>");
                bodyBuilder.append("如有任何問題，请联系ids工業互聯網開發辦公室(郵箱同名)。<br><br>");
                bodyBuilder.append("此郵件為系統自動發送，請勿回復。<br>");
                bodyBuilder.append("微智造系統");

                String body = bodyBuilder.toString();

                // 發送郵件
                try {
                    String encodedSubject = URLEncoder.encode(subject, "UTF-8");
                    String encodedBody = URLEncoder.encode(body, "UTF-8");

                    // 抄送給領導（使用Set去重）
                    Set<Integer> leaderMailIdSet = new HashSet<>();

                    // 先收集所有領導郵箱ID，避免重複查詢
                    for (CustomerConditionMail customerMail : customerMails) {
                        if (customerMail.getLeaderMailId() != null && customerMail.getLeaderMailId() > 0) {
                            leaderMailIdSet.add(customerMail.getLeaderMailId());
                        }
                    }

                    // 存儲領導郵箱地址，自動去重
                    Set<String> leaderEmailAddresses = new HashSet<>();

                    // 批量查詢領導郵箱
                    if (!leaderMailIdSet.isEmpty()) {
                        // 將Set轉換為List，用於批量查詢
                        List<Integer> leaderMailIds = new ArrayList<>(leaderMailIdSet);

                        // 一次性批量查詢所有領導郵箱
                        List<CustomerConditionMail> leaderMails = customerConditionMapper.selectCustomerConditionMailByIds(leaderMailIds);

                        // 提取有效的郵箱地址
                        for (CustomerConditionMail leaderMail : leaderMails) {
                            if (leaderMail != null && leaderMail.getMailAddress() != null && !leaderMail.getMailAddress().trim().isEmpty()) {
                                leaderEmailAddresses.add(leaderMail.getMailAddress().trim());
                            }
                        }
                    }

                    // 將Set轉換為逗號分隔的字符串
                    StringBuilder ccEmailAddresses = new StringBuilder();
                    for (String leaderEmail : leaderEmailAddresses) {
                        if (ccEmailAddresses.length() > 0) {
                            ccEmailAddresses.append(",");
                        }
                        ccEmailAddresses.append(leaderEmail);
                    }

                    String encodedCc = ccEmailAddresses.length() > 0 ? URLEncoder.encode(ccEmailAddresses.toString(), "UTF-8") : "";

                    email.selectEmail(emailAddress, encodedSubject, encodedBody, encodedCc);
                    log.info("成功發送提醒郵件給{}，提醒{}\u500b客戶的條件記錄本月尚未更新", emailAddress, customerMails.size());
                } catch (Exception e) {
                    log.error("發送提醒郵件給{}失敗: {}", emailAddress, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("提醒本月未更新客戶條件記錄的local cs失敗", e);
        }
    }
//
//    /**
//     * 手動觸發提醒郵件的方法（用於測試）
//     */
//    public void manualRemindLocalCsForCustomersWithoutUpdate() throws IOException {
//        remindLocalCsForCustomersWithoutUpdate();
//    }
}
