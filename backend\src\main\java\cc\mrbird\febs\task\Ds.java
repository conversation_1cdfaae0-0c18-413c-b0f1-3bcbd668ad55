package cc.mrbird.febs.task;

import cc.mrbird.febs.wxby.dao.SelectMapper;
import cc.mrbird.febs.wxby.dao.WXBYMapper;
import cc.mrbird.febs.wxby.entity.MaintenanceLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
@EnableAsync
public class Ds {

/*  @Autowired
    private WXBYMapper wxbyMapper;
    @Autowired
    private SelectMapper selectMapper;
    //1000 = 1秒
    //测试环境可以每秒运行一次,建议关闭SQL语句打印之后再测试
    //生产环境可以一小时运行一次,避免造成资源访问浪费
    //  一小时 =  360000  毫秒
    @Scheduled(fixedRate = 10000)
    public void Task1(){
        try{
            List<MaintenanceLog> list = selectMapper.queryALlEquipmentErrorLog();
            List<MaintenanceLog> list2 = wxbyMapper.selectAllByGetDate();
            list.removeAll(list2);
            System.out.println(list2);
            System.out.println(list);
           List<String>b= wxbyMapper.countMaintenance();
            System.out.println(b);
            if (b.equals("") && b==null ){

                System.out.println("已经存在一笔相同内容的数据,无法同步");
            }else{
                wxbyMapper.insertMaintenanceLog(list);
            }
        }catch (Exception e){
            System.out.println("暂时无新增数据,请稍后查看");
        }
    }*/
}
