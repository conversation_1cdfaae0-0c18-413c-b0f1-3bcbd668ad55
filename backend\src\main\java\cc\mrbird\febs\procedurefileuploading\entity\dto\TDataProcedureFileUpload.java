package cc.mrbird.febs.procedurefileuploading.entity.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataProcedureFileUpload {
    private Integer id;
    private String pl;
    private String enginery;
    private String discern;
   private String production;
    private String productionLine;
    private String fileType;
    private String fileName;
    private String filePath;
    private String fileVersionCode;
    private String uploadUser;
    private String equipmentName;
    private String equipmentId;
    private String remark;
    private Integer updateNum;
    private String showId;
    private Integer fileId;
}
