package cc.mrbird.febs.sparepart.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentDataBy123 {
    private Integer id;
    private String production;
    private String productionLine;
    private String equipmentName;
    private String sparePartName;
    private String lifeLimit;
    private String actualOutput;
    private String personInChargeEmailBy01;
    private String personInChargeEmailBy02;
    private String personInChargeEmailByMax;
    private String isSendEmail;
    private String personInChargeNameBy01;
    private String equipmentUpdateTime;
    private String personInChargeNameBy02;
    private String personInChargeNameByMax;

}
