package cc.mrbird.febs.rdProject.service;

import cc.mrbird.febs.common.domain.Tree;
import cc.mrbird.febs.common.exception.LimitAccessException;
import cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo;
import cc.mrbird.febs.rdProject.entity.RdProjectUserExtra;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理服務接口
 */
public interface RdProjectTaskService {


    // 查詢廠區列表
    List<String> querySiteAreaList();

    // 根據廠區查詢產品処列表
    List<String> querySbuBySiteArea(String siteArea);

    // 根據廠區和產品処查詢機能列表
    List<String> queryFunctionsBySiteAreaAndSbu(String siteArea, String sbu);

    // 根據廠區、產品処和機能查詢課別列表
    List<String> querySectionBySiteAreaAndSbuAndFunctions(String siteArea, String sbu, String functions);

    // 查詢登录用户本人的廠區、產品処、機能和課別
    RdProjectUserExtra queryUserExtra();

    // 查詢研究項目協同管理的主要信息
    PageInfo<Tree<RdProjectTaskMainInfo>> queryRdProjectTaskMainInfo(RdProjectTaskMainInfo rdProjectTaskMainInfo,int pageNum,int pageSize) throws LimitAccessException;

    // 解析並保存任務樹
    void saveTaskTree(String treeJson, Map<String, MultipartFile> fileMap) throws IOException;
}
