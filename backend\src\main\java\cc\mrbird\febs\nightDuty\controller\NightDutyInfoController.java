package cc.mrbird.febs.nightDuty.controller;


import cc.mrbird.febs.common.domain.FebsResponse;

import cc.mrbird.febs.nightDuty.dao.NightDutyInfoMapper;
import cc.mrbird.febs.nightDuty.entity.Information;
import cc.mrbird.febs.nightDuty.entity.NightDutyInfo;
import cc.mrbird.febs.nightDuty.entity.vo.NightDutyInfoVo;
import cc.mrbird.febs.nightDuty.util.DataProcessing;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/nightDuty")
@Api(tags = "值夜接口")
@Slf4j
public class NightDutyInfoController {

    @Autowired
    private NightDutyInfoMapper nightDutyInfoMapper;

    @PostMapping("/queryNightDutyInfo")
    @ApiOperation("查询值夜信息")
    public FebsResponse queryInfo(@RequestBody Information information) {
        List<NightDutyInfo> finList = new ArrayList<>();
        String building = information.getBuilding();
        String date = information.getDate();
        String status = information.getStatus();
        List<String> area = nightDutyInfoMapper.queryArea(building);
        List<String> department = nightDutyInfoMapper.queryDepartment(building);
        List<String> header = nightDutyInfoMapper.queryHeader(building);
        List<String> floor = nightDutyInfoMapper.queryFloor(building);
        /*List<NightDutyInfo> list = nightDutyInfoMapper.queryInfo(date);*/
        List<NightDutyInfo> list = nightDutyInfoMapper.queryInfoByBuilding(date,building);

        //根据楼栋来查询
        if(date.isEmpty() && !building.isEmpty()){
            List<NightDutyInfo> lists = new ArrayList<>();

            List<String> date1 = nightDutyInfoMapper.queryDate();
            for (int i = 0; i < date1.size(); i++) {
                String date2 = date1.get(i);
                List<NightDutyInfo> listBuinding1 = nightDutyInfoMapper.queryInfoByBuilding(date2,building);
                if(listBuinding1.size() != 0) {
                    DataProcessing.dataProcessing(listBuinding1, lists, area, department, header, floor, building);
                }
            }
            System.out.println(lists.size());
            DataProcessing.dataStatus(finList,lists,status);
            return new FebsResponse().code("200").message("查詢成功").data(finList);
        }

        //時間作為單獨的一項查詢
        if(!date.isEmpty() && building.isEmpty()){
            List<NightDutyInfo> lists = new ArrayList<>();
            List<String> buildings = new ArrayList<>();
            buildings.add(0,"A2-A3");
            buildings.add(1,"A4-A5");
            //先處理A2-A3的數據
            List<NightDutyInfo> listBuinding1 = nightDutyInfoMapper.queryInfoByBuilding(date,buildings.get(0));
            List<String> area1 = nightDutyInfoMapper.queryArea(buildings.get(0));
            List<String> department1 = nightDutyInfoMapper.queryDepartment(buildings.get(0));
            List<String> header1 = nightDutyInfoMapper.queryHeader(buildings.get(0));
            List<String> floor1 = nightDutyInfoMapper.queryFloor(buildings.get(0));

           if(listBuinding1.size() != 0) {

              DataProcessing.dataProcessing(listBuinding1,lists,area1,department1,header1,floor1,buildings.get(0));

           }
            //處理A4-A5的數據
            List<NightDutyInfo> listBuinding2 = nightDutyInfoMapper.queryInfoByBuilding(date,buildings.get(1));
            List<String> area2 = nightDutyInfoMapper.queryArea(buildings.get(1));
            List<String> department2 = nightDutyInfoMapper.queryDepartment(buildings.get(1));
            List<String> header2 = nightDutyInfoMapper.queryHeader(buildings.get(1));
            List<String> floor2 = nightDutyInfoMapper.queryFloor(buildings.get(1));

            if(listBuinding2.size() != 0){
              DataProcessing.dataProcessing(listBuinding2,lists,area2,department2,header2,floor2,buildings.get(1));

            }

            System.out.println(lists.size());
            DataProcessing.dataStatus(finList,lists,status);
            return new FebsResponse().code("200").message("查詢成功").data(finList);
        }

        //時間日期全為空時
        if(date.isEmpty() && building.isEmpty()){
            List<NightDutyInfo> lists = new ArrayList<>();
            List<String> buildings = new ArrayList<>();
            buildings.add(0,"A2-A3");
            buildings.add(1,"A4-A5");
            List<String> date1 = nightDutyInfoMapper.queryDate();
            for (int i = 0; i < buildings.size(); i++) {
                String build = buildings.get(i);
                String building2 = buildings.get(i);
                List<String> area2 = nightDutyInfoMapper.queryArea(building2);
                List<String> department2 = nightDutyInfoMapper.queryDepartment(building2);
                List<String> header2 = nightDutyInfoMapper.queryHeader(building2);
                List<String> floor2 = nightDutyInfoMapper.queryFloor(building2);
                for (int j = 0; j < date1.size(); j++) {
                    String date2 = date1.get(j);
                    List<NightDutyInfo> list1 = nightDutyInfoMapper.queryInfoByBuilding(date2,build);
                        if(list1.size() != 0) {
                            DataProcessing.dataProcessing(list1,lists,area2,department2,header2,floor2,building2);
                        }
                }
            }
            String status1 = "全部";
            DataProcessing.dataStatus(finList,lists,status1);
            return new FebsResponse().code("200").message("查詢成功").data(finList);
        }

       /* //日期時間全為空時
        if (date.isEmpty() && building.isEmpty()) {
            List<NightDutyInfo> lists = new ArrayList<>();
            List<String> date1 = nightDutyInfoMapper.queryDate();
            for (int i = 0; i < date1.size(); i++) {
                    String date2 = date1.get(i);
                    List<NightDutyInfo> list1 = nightDutyInfoMapper.queryInfo(date2);
                    NightDutyInfo getbuilding = list1.get(i) ;
                    String building2 = getbuilding.getBuilding();
                    List<String> area2 = nightDutyInfoMapper.queryArea(building2);
                    List<String> department2 = nightDutyInfoMapper.queryDepartment(building2);
                    List<String> header2 = nightDutyInfoMapper.queryHeader(building2);
                    List<String> floor2 = nightDutyInfoMapper.queryFloor(building2);

                   DataProcessing.dataProcessing(list1,lists,area2,department2,header2,floor2,building2);

            }
            DataProcessing.dataStatus(finList,lists,status);
            return new FebsResponse().code("200").message("查詢成功").data(finList);
        }*/


        //日期時間全都不為空時
            if (!date.isEmpty() && !building.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    List<String> picture = new ArrayList<>();
                    NightDutyInfo nightDutyInfo = list.get(i);
                    switch (nightDutyInfo.getNGStatus()) {
                        case "Y":
                            nightDutyInfo.setNGStatus("異常");
                            break;
                        case "N":
                            nightDutyInfo.setNGStatus("正常");
                            break;
                    }

                    if (i == area.size()) {
                        return new FebsResponse().code("500").message("樓棟時間信息有誤，或當天沒有值夜信息，請檢查時間樓棟信息或聯繫管理員");
                    }

                    String s = building;
                    s = s + "-" + floor.get(i);
                    nightDutyInfo.setFloor(s);
                    nightDutyInfo.setBuilding("");

                    if(!nightDutyInfo.getPicture1().isEmpty()){
                        picture.add(nightDutyInfo.getPicture1());
                    }

                    if (!nightDutyInfo.getPicture2().isEmpty()) {
                        if (nightDutyInfo.getPicture2().equals("http://************:3000/ZgzyPicUpload/")) {
                            nightDutyInfo.setPicture2("");
                        }
                    }

                    if (!nightDutyInfo.getPicture2().isEmpty()){
                        picture.add(nightDutyInfo.getPicture2());
                    }
                    nightDutyInfo.setPicture(picture);


                    if (!area.get(i).equals(nightDutyInfo.getArea())) {
                        NightDutyInfo nightDutyInfo1 = new NightDutyInfo();
                        s = building;
                        s = s + "-" + floor.get(i);
                        nightDutyInfo1.setFloor(s);
                        nightDutyInfo1.setWorkId(nightDutyInfo.getWorkId());
                        nightDutyInfo1.setName(nightDutyInfo.getName());
                        nightDutyInfo1.setTime("未完成簽到");
                        nightDutyInfo1.setNGStatus("異常");
                        nightDutyInfo1.setPicture1("");
                        nightDutyInfo1.setPicture2("");
                        nightDutyInfo1.setDescription("");
                        nightDutyInfo1.setDepartment(department.get(i));
                        nightDutyInfo1.setHeader(header.get(i));
                        nightDutyInfo1.setArea(area.get(i));
                        list.add(i, nightDutyInfo1);
                    }

                }
                if (list.size() == 0) {
                    return new FebsResponse().code("500").message("樓棟時間信息有誤，或當天沒有值夜信息，請檢查時間樓棟信息或聯繫管理員");
                }

                if (list.size() < area.size()) {
                    NightDutyInfo nightDutyInfo1 = new NightDutyInfo();
                    NightDutyInfo nightDutyInfo = list.get(list.size() - 1);
                    String s = building;
                    s = s + "-" + floor.get(area.size() - 1);
                    nightDutyInfo1.setFloor(s);
                    nightDutyInfo1.setWorkId(nightDutyInfo.getWorkId());
                    nightDutyInfo1.setName(nightDutyInfo.getName());
                    nightDutyInfo1.setTime("未完成签到");
                    nightDutyInfo1.setNGStatus("異常");
                    nightDutyInfo1.setPicture1("");
                    nightDutyInfo1.setPicture2("");
                    nightDutyInfo1.setDescription("");
                    nightDutyInfo1.setDepartment(department.get(area.size() - 1));
                    nightDutyInfo1.setHeader(header.get(area.size() - 1));
                    nightDutyInfo1.setArea(area.get(area.size() - 1));
                    list.add(area.size() - 1, nightDutyInfo1);
                }
                System.out.println(list.size());
                DataProcessing.dataStatus(finList,list,status);
                return new FebsResponse().code("200").message("查詢成功").data(finList);
            }
            return new FebsResponse().code("500").message("出現未知bug請聯繫管理員");
        }

    @PostMapping("/downloadNightDutyInfo") 
    @ApiOperation("下载值夜信息")
    public FebsResponse downloadInfo(HttpServletResponse response,@RequestBody Information information) throws Exception {

        List<NightDutyInfo> finList = new ArrayList<>();
        String building = information.getBuilding();
        String date = information.getDate();
        String status = information.getStatus();
        List<String> area = nightDutyInfoMapper.queryArea(building);
        List<String> department = nightDutyInfoMapper.queryDepartment(building);
        List<String> header = nightDutyInfoMapper.queryHeader(building);
        List<String> floor = nightDutyInfoMapper.queryFloor(building);
        /*List<NightDutyInfo> list2 = nightDutyInfoMapper.queryInfo(date);*/
        List<NightDutyInfo> list2 = nightDutyInfoMapper.queryInfoByBuilding(date,building);

        if ((date.isEmpty() && building.isEmpty()) || (date.isEmpty() && !building.isEmpty()) ||(!date.isEmpty() && building.isEmpty())){
            return new FebsResponse().code("500").message("下載失敗，樓棟信息和時間信息不能為空");
        }

        if (!date.isEmpty() && !building.isEmpty()) {
            for (int i = 0; i < list2.size(); i++) {
                NightDutyInfo nightDutyInfo = list2.get(i);
                nightDutyInfo.setStatus("已完成簽到");
                switch (nightDutyInfo.getNGStatus()) {
                    case "Y":
                        nightDutyInfo.setNGStatus("異常");
                        break;
                    case "N":
                        nightDutyInfo.setNGStatus("正常");
                        break;
                }
                if (i == area.size()) {
                    return new FebsResponse().code("500").message("下載失敗,樓棟時間信息有誤，當天沒有值夜信息，請檢查樓棟時間信息後重試");
                }
                if (!area.get(i).equals(nightDutyInfo.getArea())) {
                    NightDutyInfo nightDutyInfo1 = new NightDutyInfo();
                    nightDutyInfo1.setBuilding(building);
                    nightDutyInfo1.setFloor(floor.get(i));
                    nightDutyInfo1.setWorkId(nightDutyInfo.getWorkId());
                    nightDutyInfo1.setName(nightDutyInfo.getName());
                    nightDutyInfo1.setStatus("未完成簽到");
                    nightDutyInfo1.setTime("");
                    nightDutyInfo1.setNGStatus("異常");
                    nightDutyInfo1.setPicture1("");
                    nightDutyInfo1.setPicture2("");
                    nightDutyInfo1.setDescription("");
                    nightDutyInfo1.setDepartment(department.get(i));
                    nightDutyInfo1.setHeader(header.get(i));
                    nightDutyInfo1.setArea(area.get(i));
                    list2.add(i, nightDutyInfo1);
                }

            }
            if(list2.size() == 0){
                return new FebsResponse().code("500").message("下載失敗,樓棟時間信息有誤，當天沒有值夜信息，請檢查樓棟時間信息後重試");
            }
            if (list2.size() < area.size()) {
                NightDutyInfo nightDutyInfo1 = new NightDutyInfo();
                NightDutyInfo nightDutyInfo = list2.get(list2.size() - 1);
                nightDutyInfo1.setBuilding(building);
                nightDutyInfo1.setFloor(floor.get(area.size() - 1));
                nightDutyInfo1.setWorkId(nightDutyInfo.getWorkId());
                nightDutyInfo1.setName(nightDutyInfo.getName());
                nightDutyInfo1.setStatus("未完成簽到");
                nightDutyInfo1.setTime("");
                nightDutyInfo1.setNGStatus("異常");
                nightDutyInfo1.setPicture1("");
                nightDutyInfo1.setPicture2("");
                nightDutyInfo1.setDescription("");
                nightDutyInfo1.setDepartment(department.get(area.size() - 1));
                nightDutyInfo1.setHeader(header.get(area.size() - 1));
                nightDutyInfo1.setArea(area.get(area.size() - 1));
                list2.add(area.size() - 1, nightDutyInfo1);
            }
            System.out.println(list2.size());
        }
            DataProcessing.dataStatus(finList,list2,status);
        List<NightDutyInfoVo> list = new ArrayList<>();
        for (int i = 0; i < finList.size(); i++) {
            NightDutyInfoVo s1 = new NightDutyInfoVo();
            NightDutyInfo s= finList.get(i);
            String picture1 = s.getPicture1();
            if (!s.getPicture2().isEmpty()){
                if(s.getPicture2().equals("http://************:3000/ZgzyPicUpload/")){
                    s.setPicture2("");
                }
            }
            String picture2 = s.getPicture2();
            if(!picture1.isEmpty()){
                s1.setPicture1(new URL(picture1));
            }

            if(!picture2.isEmpty()){
                s1.setPicture2(new URL(picture2));
            }

           s1.setBuilding(s.getBuilding());
            s1.setFloor(s.getFloor());
            s1.setArea(s.getArea());
            s1.setWorkId(s.getWorkId());
            s1.setName(s.getName());
            s1.setStatus(s.getStatus());
            s1.setTime(s.getTime());
            s1.setNGStatus(s.getNGStatus());
            s1.setDescription(s.getDescription());
            s1.setDepartment(s.getDepartment());
            s1.setHeader(s.getHeader());
            list.add(i,s1);

        }

        String filename = "值夜信息表";
        ServletOutputStream servletOutputStream = servletOutputStream(filename,response);


        //设置主标题格式
        WriteCellStyle titleWriteCellStyle = new WriteCellStyle();
        WriteFont titleWriteFont = new WriteFont();
        titleWriteFont.setBold(true);
        titleWriteFont.setFontName("宋体");
        titleWriteFont.setColor(IndexedColors.WHITE.getIndex());
        titleWriteCellStyle.setWriteFont(titleWriteFont);
        titleWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);  // 设置水平居中
        titleWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 设置垂直居中
        titleWriteCellStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());


        //副标题设置格式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);  // 设置水平居中
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 设置垂直居中
        headWriteCellStyle.setFillForegroundColor(IndexedColors.BLUE.getIndex());


        //内容设置格式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);  // 设置水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 设置垂直居中

        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(titleWriteCellStyle,contentWriteCellStyle);



        EasyExcel.write(servletOutputStream, NightDutyInfoVo.class).sheet("值夜信息表").registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short)25,(short)25)).doWrite(list);
        return new FebsResponse().code("200").message("下载成功");
    }


    public static ServletOutputStream servletOutputStream(String filename, HttpServletResponse response) throws Exception {
        try {
            filename = URLEncoder.encode(filename, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment; filename=" + filename + ".xls");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");
            return response.getOutputStream();
        } catch (IOException e) {
            throw new Exception("导出excel表格失败!", e);
        }
    }

}
