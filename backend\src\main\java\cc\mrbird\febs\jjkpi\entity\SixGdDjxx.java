package cc.mrbird.febs.jjkpi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Six_GD_DJXX")
public class SixGdDjxx implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("PutInId")
    private String PutInId;

    @TableField("SBU")
    private String sbu;

    private String factory;

    private String functions;

    private String section;

    private String series;

    private String line;

    @TableField("CPLH_PFN")
    private String cplhPfn;

    @TableField("RohsHF")
    private String RohsHF;

    private String banbie;

    @TableField("ProductClass")
    private String ProductClass;

    @TableField("WareCode")
    private String WareCode;

    @TableField("GLBH_SX")
    private String glbhSx;

    @TableField("GLBH_WON")
    private String glbhWon;

    @TableField("KHLH_CMN")
    private String khlhCmn;

    @TableField("KHMC_CN")
    private String khmcCn;

    @TableField("BSDYS_LSC")
    private String bsdysLsc;

    @TableField("SCSL_QOP")
    private Integer scslQop;

    @TableField("DC_Data")
    private String dcData;

    @TableField("DC_TSDM")
    private String dcTsdm;

    @TableField("DC_SCDC")
    private String dcScdc;

    @TableField("BG_LXBG")
    private String bgLxbg;

    @TableField("BG_WXBG")
    private String bgWxbg;

    @TableField("XS_LXXS")
    private Integer xsLxxs;

    @TableField("XS_WXS")
    private Integer xsWxs;

    @TableField("KHTSYQ")
    private String khtsyq;

    @TableField("BEIZHU")
    private String beizhu;

    @TableField("PaperType")
    private String PaperType;

    @TableField("RUKUDANHAO")
    private String rukudanhao;

    @TableField("XZ_WorkId")
    private String xzWorkid;

    @TableField("XZ_Name")
    private String xzName;

    @TableField("QC_WorkId")
    private String qcWorkid;

    @TableField("QHR_WorkId")
    private String qhrWorkid;

    @TableField("QHR_Name")
    private String qhrName;

    @TableField("SQR_WorkId")
    private String sqrWorkid;

    @TableField("SQR_Name")
    private String sqrName;

    @TableField("DATE_SCRQ")
    private String dateScrq;

    @TableField("DATE_TDRQ")
    private String dateTdrq;

    @TableField("DATE_QianHe")
    private String dateQianhe;

    @TableField("DATE_LieYin")
    private String dateLieyin;

    @TableField("CastingType")
    private Integer CastingType;

    @TableField("GD_RSTATUS")
    private Integer gdRstatus;

    @TableField("TD_STATUS")
    private Integer tdStatus;

    @TableField("QH_STATUS")
    private Integer qhStatus;

    @TableField("LY_STATUS")
    private Integer lyStatus;

    @TableField("STATUS")
    private Integer status;

    @TableField("GD_DATE")
    private String gdDate;


}
