package cc.mrbird.febs.customerCondition.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: AI Assistant
 * Date: 2024/4/11
 * Time: 10:49
 * 客戶條件記錄郵件實體
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class CustomerConditionMail {
    
    @ApiModelProperty(value = "郵件ID", position = 0)
    private Integer mailId;
    
    @ApiModelProperty(value = "用戶名", position = 1)
    private String userName;
    
    @ApiModelProperty(value = "郵件地址", position = 2)
    private String mailAddress;
    
    @ApiModelProperty(value = "客戶ID", position = 3)
    private Integer customerId;
    
    @ApiModelProperty(value = "客戶名稱", position = 4)
    private String customerName;
    
    @ApiModelProperty(value = "領導郵件ID", position = 5)
    private Integer leaderMailId;
    
    @ApiModelProperty(value = "用戶類型", position = 6)
    private String userType;
}
