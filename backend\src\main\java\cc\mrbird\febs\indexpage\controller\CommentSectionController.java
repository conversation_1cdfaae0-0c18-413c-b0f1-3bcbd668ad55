package cc.mrbird.febs.indexpage.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.indexpage.dao.CommentSectionMapper;
import cc.mrbird.febs.indexpage.dao.UsersMapperByMySql;
import cc.mrbird.febs.indexpage.entity.TDataComment;
import cc.mrbird.febs.indexpage.entity.TDataCommentAndUsers;
import cc.mrbird.febs.indexpage.entity.TDataUsers;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@RestController
@Api("评论区功能接口")
@ResponseBody
@RequestMapping("/CommentSection")
public class CommentSectionController {

    private final CommentSectionMapper commentSectionMapper;

    private final UsersMapperByMySql usersMapperByMySql;
    @Autowired
    public CommentSectionController(CommentSectionMapper commentSectionMapper, UsersMapperByMySql usersMapperByMySql) {
        this.commentSectionMapper = commentSectionMapper;
        this.usersMapperByMySql = usersMapperByMySql;
    }


    @PostMapping("/insertCommentData")
    @ApiOperation(value = "插入回复/评论接口")
    @DSTransactional
    public FebsResponse insertCommentData(@RequestBody TDataComment tDataComment){
        TDataUsers users=new TDataUsers();
        String userName =tDataComment.getUserName();
        String workId=tDataComment.getWorkId();
        String avatar =selectAvatar(workId);
        Integer isSort =0;
        tDataComment.setIsSort(isSort);
        users.setUserName(userName);
        users.setAvatar(avatar);
        users.setWorkId(workId);
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = localDateTime.format(formatter);
        tDataComment.setCreatedAt(formattedDateTime);
      //  System.out.println(tDataComment);
        // 判断是否为回复
        if (tDataComment.getParentId() != null) {
            // 设置回复的parentId为父留言的ID
            tDataComment.setParentId(tDataComment.getParentId());
        }
        // 插入留言或回复
        commentSectionMapper.insertDataByTComment(tDataComment);
        commentSectionMapper.insertDataByUsers(users);
        return new FebsResponse().code("200");
    }

    @PostMapping("/selectAllData")
    @ApiOperation(value = "查询全部回复/评论接口")
    public  FebsResponse selectAllData(){
        List<TDataCommentAndUsers> list = commentSectionMapper.getAllCommentData();
       // System.out.println(list);
        List<TDataCommentAndUsers> commentHierarchy = buildCommentHierarchy(list);
        return new FebsResponse().code("200").data(commentHierarchy);
    }

    @PostMapping("/setSort")
    @ApiOperation(value = "置顶接口")
    @DSTransactional
    public FebsResponse setSort(@RequestParam("id") String id){
        try {
            Integer idBySort = commentSectionMapper.selectSortId();
            commentSectionMapper.updateBySort(idBySort);
            commentSectionMapper.setSort(id);
            return new FebsResponse().code("200").message("置顶成功!!!");
        }catch (Exception e){
            return new FebsResponse().code("500").message("置顶那么多干嘛");
        }
    }

    //查询顶级留言下的回复
    public List<TDataComment> getRepliesByParentIdAndLevel(Integer parentId, Integer level){
        return commentSectionMapper.getRepliesByParentIdAndLevel(parentId,level);
    }

    private String selectAvatar(String workId){
        String tx =usersMapperByMySql.selectDataByAvatar(workId);
        if (tx.isEmpty()){
            tx="";
        }
        return tx;
    }
    //处理顶级留言接口排序
    public List<TDataCommentAndUsers> buildCommentHierarchy(List<TDataCommentAndUsers> flatComments) {
        Map<Integer, TDataCommentAndUsers> commentMap = new HashMap<>();

        // 构建commentId到Comment对象的映射
        for (TDataCommentAndUsers comment : flatComments) {
            int commentId = comment.getId();
            commentMap.put(commentId, comment);
        }

        // 构建层次结构
        for (TDataCommentAndUsers comment : flatComments) {
            int parentId = comment.getParentId();
            int leverId = comment.getLever();
            if (parentId != 0) {
                TDataCommentAndUsers parentComment = commentMap.get(parentId);
                if (parentComment != null) {
                    if (leverId != 0) {
                        parentComment.getIsHF().add(comment); // lever不为0，添加到父级的isHF列表
                    } else {
                        parentComment.getReplies().add(comment); // lever为0，添加到父级的回复列表
                    }
                }
            }
        }

        // 按层级排序回复列表
        sortReplies(flatComments);

        // 返回顶级留言列表
        List<TDataCommentAndUsers> topLevelComments = new ArrayList<>();
        for (TDataCommentAndUsers comment : flatComments) {
            int parentId = comment.getParentId();
            if (parentId == 0) {
                topLevelComments.add(comment);
            }
        }

        return topLevelComments;
    }

    private void sortReplies(List<TDataCommentAndUsers> comments) {
        for (TDataCommentAndUsers comment : comments) {
            List<TDataCommentAndUsers> replies = comment.getReplies();
            Collections.sort(replies, Comparator.comparing(TDataCommentAndUsers::getCreatedAt));
            sortReplies(replies);
        }
    }
    //
    private void sortByLever(List<TDataCommentAndUsers> comments) {
        for (TDataCommentAndUsers comment : comments) {
            List<TDataCommentAndUsers> replies = comment.getReplies();
            Collections.sort(replies, Comparator.comparing(TDataCommentAndUsers::getLever));
            sortByLever(replies);
        }
    }
    private void testSelectData(){

    }


}
