package cc.mrbird.febs.equipmentSelfInspection.service;

import cc.mrbird.febs.equipmentSelfInspection.entity.EquipmentInspectionProjectData;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionMainInfoDto;
import cc.mrbird.febs.equipmentSelfInspection.entity.dto.EquipmentInspectionProjectDto;
import cc.mrbird.febs.equipmentSelfInspection.param.EquipmentInspectionQueryParam;

import java.util.List;

/**
 * Author: 李国斌
 * Date: 2024-09-10
 * Time: 上午 08:06
 */
public interface EquipmentInspectionMaintenanceService {
    List<String> queryDiscern(String SBU,String functions);

    List<String> queryProduction(String SBU, String functions, String discern);

    List<String> queryProductionLine(String SBU, String functions, String discern, String production);

    List<String> querySelfInspectionType();

    List<EquipmentInspectionMainInfoDto> queryEquipmentInspectionMainInfo(EquipmentInspectionQueryParam equipmentQueryParam);

    //查詢一條不點檢的綫體數據
    EquipmentInspectionMainInfoDto queryIsNotDJ(EquipmentInspectionQueryParam equipmentInspectionQueryParam);

    //根據設備id和點檢類型查詢點檢項目信息
    List<EquipmentInspectionProjectDto> queryEquipmentInspectionProjectsByEquipmentIDByEquipmentIDAndType(String equipmentID,String djType);

    //查詢點檢項目數據的信息（主要包含了設備的各個點檢項目的點檢值和點檢結果）
    EquipmentInspectionProjectData queryEquipmentInspectionProjectData(String date, String selfInspectionType, String equipmentID);

    List<String> querySBU();

    List<String> queryFunctions(String sbu);

    EquipmentInspectionProjectDto queryByUserId(String userID);

    List<String> queryDiscernBySBU(String sbu);
}
