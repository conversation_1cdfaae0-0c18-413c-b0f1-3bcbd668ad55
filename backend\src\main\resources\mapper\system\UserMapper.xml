<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.mrbird.febs.system.dao.UserMapper">
    <resultMap id="BaseResultMap" type="cc.mrbird.febs.system.domain.User">
        <id column="USER_ID" jdbcType="DECIMAL" property="userId"/>
        <result column="USERNAME" jdbcType="VARCHAR" property="username"/>
        <result column="PASSWORD" jdbcType="VARCHAR" property="password"/>
        <result column="DEPT_ID" jdbcType="DECIMAL" property="deptId"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="STATUS" jdbcType="CHAR" property="status"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="LAST_LOGIN_TIME" jdbcType="TIMESTAMP" property="lastLoginTime"/>
        <result column="SSEX" jdbcType="CHAR" property="ssex"/>
        <result column="AVATAR" jdbcType="VARCHAR" property="avatar"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
    </resultMap>
    <resultMap id="deptUsersMap" type="cc.mrbird.febs.system.domain.DeptUsers">
        <id column="userIds" jdbcType="VARCHAR" property="userIds"/>
        <result column="deptId" jdbcType="DECIMAL" property="deptId"/>
    </resultMap>
    <select id="findUserDetail" resultType="user" parameterType="user">
        SELECT
        u.user_id userId,
        u.username,
        u.password,
        u.email,
        u.mobile,
        u. STATUS,
        u.create_time createTime,
        u.ssex,
        d.dept_id deptId,
        d.dept_name deptName,
        u.AVATAR,
        u.DESCRIPTION,
        u.LAST_LOGIN_TIME lastLoginTime,
        GROUP_CONCAT(r.role_id) roleId,
        GROUP_CONCAT(r.ROLE_NAME) roleName
        FROM
        t_user u
        LEFT JOIN t_dept d ON (u.dept_id = d.dept_id)
        LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id)
        LEFT JOIN t_role r ON r.role_id = ur.role_id
        WHERE 1 = 1
        <if test="user.username != null and user.username != ''">
            AND u.username = #{user.username}
        </if>
        <if test="user.deptId != null and user.deptId != ''">
            AND d.dept_id = #{user.deptId}
        </if>
        <if test="user.createTimeFrom != null and user.createTimeFrom !=''">
            And u.create_time &gt;= str_to_date(#{user.createTimeFrom},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="user.createTimeTo!= null and user.createTimeTo !=''">
            And u.create_time &lt;= str_to_date(#{user.createTimeTo},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="user.ssex != null and user.ssex != ''">
            AND u.ssex = #{user.ssex}
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        group by u.username,
            u.user_id,
            u.password,
            u.email,
            u.mobile,
            u. STATUS,
            u.create_time,
            u.ssex,
            d.dept_id,
            d.dept_name,
            u.AVATAR,
            u.DESCRIPTION,
            u.LAST_LOGIN_TIME

    </select>

    <select id="findDetail" resultType="user" parameterType="string">
        SELECT
        u.user_id userId,
        u.username,
        u.password,
        u.email,
        u.mobile,
        u. STATUS,
        u.create_time createTime,
        u.ssex,
        d.dept_id deptId,
        d.dept_name deptName,
        u.AVATAR,
        u.DESCRIPTION,
        u.LAST_LOGIN_TIME lastLoginTime,
        GROUP_CONCAT(r.role_id) roleId,
        GROUP_CONCAT(r.ROLE_NAME) roleName
        FROM
        t_user u
        LEFT JOIN t_dept d ON (u.dept_id = d.dept_id)
        LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id)
        LEFT JOIN t_role r ON r.role_id = ur.role_id
        WHERE  u.username = #{username}
        group by u.username,
            u.user_id,
            u.password,
            u.email,
            u.mobile,
            u.STATUS,
            u.create_time,
            u.ssex,
            d.dept_id,
            d.dept_name,
            u.AVATAR,
            u.DESCRIPTION,
            u.LAST_LOGIN_TIME
    </select>
    <select id="findSubordinates" resultType="string" parameterType="long">
        SELECT GROUP_CONCAT(u.user_id) FROM
        t_user u LEFT JOIN t_dept d ON u.dept_id = d.dept_id WHERE (d.dept_id = #{deptId} OR d.parent_id = #{deptId})
    </select>
    <select id="findSubordinatesMap" resultMap="deptUsersMap">
        SELECT d.dept_id deptId, GROUP_CONCAT(u.user_id) userIds FROM
        t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id or u.dept_id = d.parent_id ) where u.dept_id != '' GROUP BY deptId
    </select>
</mapper>
