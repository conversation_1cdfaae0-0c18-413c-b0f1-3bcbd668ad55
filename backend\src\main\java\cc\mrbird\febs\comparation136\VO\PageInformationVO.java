package cc.mrbird.febs.comparation136.VO;

import cc.mrbird.febs.common.annotation.NotBlank;
import cc.mrbird.febs.comparation136.entity.QueryInformation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageInformationVO implements Serializable {

    private List<QueryInformationVO> queryInformations = new ArrayList<>();
}
