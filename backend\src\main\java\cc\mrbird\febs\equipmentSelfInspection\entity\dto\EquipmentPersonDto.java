package cc.mrbird.febs.equipmentSelfInspection.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Author: 覃金華
 * Date: 2024-08-12
 * Time: 上午 11:39
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class EquipmentPersonDto {
    @ApiModelProperty(value = "表ID",example = "0",position = 0)
    private Integer id;
    @ApiModelProperty(value = "產品處",example = "IDS",position = 1)
    private String pl;
    @ApiModelProperty(value = "機能",example = "裝配",position = 2)
    private String enginery;
    @ApiModelProperty(value = "課別",example = "裝配一課",position = 3)
    private String discern;
    @ApiModelProperty(value = "系列",example = "1700SKT",position = 4)
    private String production;
    @ApiModelProperty(value = "線體",example = "V1",position = 5)
    private String productionLine;
    @ApiModelProperty(value = "工號",example = "F0841747",position = 6)
    private String userId;
    @ApiModelProperty(value = "用戶名",example = "覃金華",position = 7)
    private String userName;
    @ApiModelProperty(value = "數據類型",example = "確認",position = 8)
    private String dataType;

}
