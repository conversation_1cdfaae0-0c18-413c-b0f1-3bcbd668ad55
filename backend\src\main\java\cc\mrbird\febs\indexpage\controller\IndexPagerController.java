package cc.mrbird.febs.indexpage.controller;

import cc.mrbird.febs.common.domain.FebsResponse;
import cc.mrbird.febs.indexpage.dao.ProductionNumMapper;
import cc.mrbird.febs.indexpage.entity.*;
import cc.mrbird.febs.indexpage.entity.qo.SelectLineNumberQo;
import cc.mrbird.febs.indexpage.entity.vo.SheetNameDataVo;
import cc.mrbird.febs.indexpage.service.IndexPagerDataService;
import cc.mrbird.febs.newo.dao.EquipmentMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@RestController
@Api(tags = "无纸化首页数据请求接口")
@Slf4j
@ResponseBody
@RequestMapping("/indexPager")
public class IndexPagerController {
    @Autowired
    private ProductionNumMapper productionNumMapper;

    @Autowired
    private IndexPagerDataService indexPagerDataService;
    @Autowired
    private EquipmentMapper equipmentMapper;

    @PostMapping("/insertByContext")
    @ApiOperation(value = "公告栏插入接口", notes = "id不用传")
    private FebsResponse insertByContext(@RequestBody TDataContext tDataContext) {
        SimpleDateFormat format =new SimpleDateFormat("yyyy-MM-dd");
        Date date= new Date();
        String strTime=    format.format(date.getTime());
        tDataContext.setCreatTime(strTime);
        int a = productionNumMapper.insertByTDataContext(tDataContext);
        if (a == 1) {
            return new FebsResponse().code("200").message("更新成功");
        }
        return new FebsResponse().code("200").message("更新失败");
    }
    @PostMapping("/updateContext")
    @ApiOperation(value = "公告栏修改接口")
    private FebsResponse updateContext(@RequestBody TDataContext tDataContext) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date =new Date();
         tDataContext.setUpdateTime(format.format(date.getTime()));
        boolean b = productionNumMapper.updateContext(tDataContext);
        if (b){
            return new FebsResponse().code("200").message("更新成功");
        }
        return new FebsResponse().code("500").message("更新失败");
    }

    @GetMapping("/selectContext")
    @ApiOperation(value = "公告栏查询接口，直接调用即可")
    private FebsResponse selectContext() {
        List<TDataContext> list = productionNumMapper.selectContext();
        list.forEach(dd->{
            if (dd.getUsername().equals("韦安琪")){
                dd.setUsername("系统管理员");
            }
        });
        return new FebsResponse().code("200").data(list);
    }

    @PostMapping("/getProductionNum")
    @ApiOperation(value = "產能預測數據接口", notes = "產能預測數據接口")
    public FebsResponse getProductionNum(@RequestBody ProductionNum productionNum) {
        List<TDataByCountProductionNum> list = indexPagerDataService.getProductionNum(productionNum);
        list.forEach(num->{
            double dd = num.getProductionNum();
            dd=dd/1000;
            BigDecimal bigDecimal = new BigDecimal(dd);
            double round = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
            num.setProductionNum(round);
        });
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("getSubState")
    @ApiOperation(value = "获取提交数", notes = "参数值为用户工号")
    public FebsResponse getSubState(@RequestParam("workId") String workId) {

        return new FebsResponse().code("200").data(indexPagerDataService.getSubState(workId));
    }

    @GetMapping("/getQhState")
    @ApiOperation(value = "获取待签核数量", notes = "参数值为用户工号")
    public FebsResponse getQhState(@RequestParam("workId") String workId) {
        int a = indexPagerDataService.getQhState(workId);
        return new FebsResponse().code("200").data(a);
    }

    @GetMapping("/getBackNum")
    @ApiOperation(value = "获取被驳回数量", notes = "参数为用户工号")
    public FebsResponse getBackNum(@RequestParam("workId") String workId) {
        int a = indexPagerDataService.getBackNum(workId);
        return new FebsResponse().code("200").data(a);
    }

    @PostMapping("/getPartNumber")
    @ApiOperation(value = "獲取工单信息", notes = "参数值为用户工号")
    public FebsResponse getPartNumber(@RequestBody DateAndWorkIdQo andWorkIdQo) throws ParseException {
        SimpleDateFormat simpleDateFormat =new SimpleDateFormat("yyyy-MM-dd");
        Date date =simpleDateFormat.parse(andWorkIdQo.getDate());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        //+1之后的Date
        Date newDate = calendar.getTime();
        andWorkIdQo.setDate1( simpleDateFormat.format(newDate));
        List<PartNumberByIndex> list = indexPagerDataService.getPartNumber(andWorkIdQo);
        return new FebsResponse().code("200").data(list);
    }

    @GetMapping("/getProductivityData")
    @ApiOperation(value = "获取产能数据,范围为当周(周一开始)",notes = "参数值为PL 和 課別")
    public FebsResponse getProductivityData(@RequestParam("pl") String pl,
                                            @RequestParam("discern") String discern) {
        List<SumProductivityData> list = productionNumMapper.getProductivityData(pl, discern);


        return new FebsResponse().code("200").data(list);
    }

    @ApiOperation(value = "插入用户最近浏览页面数据",notes = "参数值看Data对象")
    @PostMapping("/insertPageVisitData")
    public FebsResponse insertPageVisitData(@RequestBody VisitPageData visitPageData){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm"); //hh:mm 是12小时制   HH:mm是24小时制
        Date date = new Date();
        String dateStr = format.format(date.getTime());
        visitPageData.setVisitTime(dateStr);
        int i = productionNumMapper.insertVisitData(visitPageData);
        if (i==1){
            return  new FebsResponse().code("200").message("插入成功");
        }
        return  new FebsResponse().code("200").message("插入失败");

    }



    @ApiOperation(value = "获取用户最近浏览页面数据",notes = "参数值为用户名")
    @GetMapping("/getPageVisitData")
    public FebsResponse getPageVisitData(@RequestParam("workId") String workId){
        List<VisitPageData> list = productionNumMapper.selectVisitData(workId);
        List<VisitPageData> list2 = list.stream().filter(distinctByKey(VisitPageData::getPageName)).collect(Collectors.toList());
        return  new FebsResponse().code("200").message("获取成功").data(list2);
    }
    //根据当前时间获取白晚班
    @PostMapping("/selectShiftByTime")
    public FebsResponse selectShiftByTime(@RequestParam("strTime")String strTime){
        DateTimeFormatter dateTimeFormatter =DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localDateTime = LocalDateTime.parse(strTime,dateTimeFormatter);
        String ShiftName =indexPagerDataService.determineShift(localDateTime);
        return new FebsResponse().code("200").data(ShiftName);
    }
    @ApiOperation(value = "根据用户信息获取表单的各项提交记录",notes = "参数值为:用户工号和姓名")
    @PostMapping("/getSheetDataByUser")
    public FebsResponse getSheetDataByUser(@RequestBody SelectLineNumberQo selectLineNumberQot)throws ParseException{
        Map<String, SheetNameDataVo> map =indexPagerDataService.selectAllDataByQo(selectLineNumberQot);
        return  new FebsResponse().code("200").data(map);
    }




    public static List<SumProductivityData> getWeekData(Date dataTime) {
        /**
         * 转为calendar格式
         * calendar.get(Calendar.MONTH)+1  calendar中的月份以0开头
         * Calendar.DAY_OF_WEEK 当前日期是所在周的第几天（以周日为一周的第一天）
         * Calendar.DATE 当前日期是几号
         *  */
        List<SumProductivityData> week = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataTime);

        // 如果是周日
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            calendar.add(Calendar.DAY_OF_YEAR, -1);
        }
        // 获取当前日期是当周的第i天
        int i = calendar.get(Calendar.DAY_OF_WEEK) - 1;

        // 获取当前日期所在周的第一天
        calendar.add(Calendar.DATE, -i + 1);
        for (int j = 0; j < 6; j++) {
            SumProductivityData sumProductivityData = new SumProductivityData();
            sumProductivityData.setDays(new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime()));
            sumProductivityData.setNum(0);
            week.add(sumProductivityData);
            calendar.add(Calendar.DATE, 1);
        }
        return week;

    }





/*    @GetMapping("/testDate")
    @ApiOperation(value = "日期测试",notes = "日期测试")
    public FebsResponse testDate(){
        Date date =new Date();
        DateFormat dateFormat =new SimpleDateFormat("yyyy-MM-dd");
       String str=     dateFormat.format(date.getTime());
        return  new FebsResponse().code("200").data(str);
    }*/


    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }



}
