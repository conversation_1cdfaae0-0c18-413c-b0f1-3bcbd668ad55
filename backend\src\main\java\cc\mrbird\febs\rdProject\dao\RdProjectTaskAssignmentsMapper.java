package cc.mrbird.febs.rdProject.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import cc.mrbird.febs.rdProject.entity.RdProjectTaskAssignments;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: AI Assistant
 * Date: 2025/7/18
 * Time: 10:49
 * 研究項目協同管理的任務責任人(團隊)表數據訪問接口
 */
@DS("primary")
public interface RdProjectTaskAssignmentsMapper {

    // 插入任務分配記錄
    int insertRdProjectTaskAssignments(RdProjectTaskAssignments rdProjectTaskAssignments);

    // 根據任務ID和分配者ID刪除任務分配記錄
    int deleteRdProjectTaskAssignments(@Param("taskId") Long taskId, @Param("assigneeId") String assigneeId);

    // 根據任務ID刪除所有任務分配記錄
    int deleteRdProjectTaskAssignmentsByTaskId(@Param("taskId") Long taskId);

    // 更新任務分配記錄
    int updateRdProjectTaskAssignments(RdProjectTaskAssignments rdProjectTaskAssignments);

    // 根據任務ID查詢任務分配記錄列表
    List<RdProjectTaskAssignments> selectRdProjectTaskAssignmentsByTaskId(@Param("taskId") Long taskId);

    // 根據分配者ID查詢任務分配記錄列表
    List<RdProjectTaskAssignments> selectRdProjectTaskAssignmentsByAssigneeId(@Param("assigneeId") String assigneeId);

    // 根據分配者類型查詢任務分配記錄列表
    List<RdProjectTaskAssignments> selectRdProjectTaskAssignmentsByAssigneeType(@Param("assigneeType") Integer assigneeType);

    // 查詢所有任務分配記錄
    List<RdProjectTaskAssignments> selectAllRdProjectTaskAssignments();
}
