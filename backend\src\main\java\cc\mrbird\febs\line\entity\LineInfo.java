package cc.mrbird.febs.line.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LineInfo {
     //系列
     private String series;
     //线体
     private String line;
     //料号
     private String CPLH_PFN;

     //交接人,默认为该账号
     private List<String> passPerson;

}
