package cc.mrbird.febs.rdProject.entity.vo;

import cc.mrbird.febs.rdProject.entity.RdProjectTaskAssignments;
import cc.mrbird.febs.rdProject.entity.RdProjectTaskMainInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Author: 李囯斌
 * @Date: 2025/7/21
 * @Time: 14:43
 */
@Data
@Getter
@Setter
@NoArgsConstructor
public class RdProjectTaskMainInfoVO extends RdProjectTaskMainInfo {
    @ApiModelProperty(value = "附件列表", position = 13)
    private List<MultipartFile> attachments; // 新增附件字段

    @ApiModelProperty(value = "責任人列表", position = 14)
    private List<RdProjectTaskAssignments> taskAssignmentsList; // 新增责任人字段
}
