package cc.mrbird.febs.procedurefileuploading.service;

import cc.mrbird.febs.procedurefileuploading.entity.dto.TDataProcedureFileUpload;
import cc.mrbird.febs.procedurefileuploading.entity.qo.GetProcedureFileUploadDataQo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ProcedureFileUploadService {
    String uploadFileByProcedure(List<MultipartFile> files) throws IOException;
    int insertFileUploadData(TDataProcedureFileUpload tDataProcedureFileUpload);
    List<String> getPl();



    List<String> getDiscern(String pl,String enginery);

    List<String> getProduction(String pl,String discern);
    List<String> getProductionLine(String pl,String discern,String production);

    List<TDataProcedureFileUpload> getProcedureFileUploadData(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);


    List<String> getEquipmentName(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getProductionByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getProductionLineByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getEquipmentNameByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);

    List<String> getEquipmentCodeByUploadFile(GetProcedureFileUploadDataQo getProcedureFileUploadDataQo);


    String updateFileUpload(TDataProcedureFileUpload tDataProcedureFileUpload) throws IOException;

    String queryEngineryByWorkId(String workId);





}
