package cc.mrbird.febs.glueweighing.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Weight {
    @JsonIgnore
    private String uuid;
    @ApiModelProperty(value = "事業處",position = 0)
    private String pl;
    @ApiModelProperty(value = "機能",position = 1)
    private String enginery;
    @ApiModelProperty(value = "課別",position = 2)
    private String discern;
    @ApiModelProperty(value = "系列",position = 3)
    private String production;
    @ApiModelProperty(value = "線體",position = 4)
    private String productionLine;
    @ApiModelProperty(value = "班別",position = 5)
    private String classInfo;
    @ApiModelProperty(value = "日期 2023-05-09",position = 6)
    private String recordDate;
    @ApiModelProperty(value = "時間 2023-05-09 13:23:00",position = 7)
    private String recordTime;
    @ApiModelProperty(value = "頻率",position = 8)
    private String frequency;
    @ApiModelProperty(value = "UI圖底部時間",position = 9)
    private String otherTime;
    @ApiModelProperty(value = "洗閥",position = 10)
    private String washValve;
    @ApiModelProperty(value = "溫度",position = 11)
    private String temperature;
    @ApiModelProperty(value = "機台號",position = 12)
    private String machineNumber;
    @ApiModelProperty(value = "換膠",position = 13)
    private String glueChange;
}
