package cc.mrbird.febs.securityInspection.dao;

import cc.mrbird.febs.securityInspection.entity.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("mssql-paperless")
public interface SecurityInspectionMapper {

    List<String> queryDate();

    List<String> queryFactory();

    List<String> queryBuilding();

    List<String> queryFloor();

    /**
     * 获取查询点位表数据
     * @return 查询点位表数据列表
     */
    List<SecurityInspectionExportExcelAllPoint> queryInfo(QueryCondition queryCondition);


    /**
     * 获取查询樓層表数据
     * @return 查询点位表数据列表
     */
    List<SecurityInspectionExportExcelFloor> queryInfoByFloor(QueryCondition queryCondition);

    List<InspectionStationRaw> queryStation(QueryCondition queryCondition);

    List<InspectionStationRaw> selectFloor(QueryCondition queryCondition);


    /**
     * 查询图片信息
     * @param queryCondition
     * @return
     */
    List<SecurityInspectionVO> queryPoint(QueryCondition queryCondition);
}
