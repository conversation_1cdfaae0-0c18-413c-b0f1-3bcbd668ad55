package cc.mrbird.febs.clearLine.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDataClearLineCheckItemByQC {
    @ApiModelProperty(value = "工作台面")
    private String tableTopByQC;
    @ApiModelProperty(value = "地面")
    private String groundByByQC;
    @ApiModelProperty("不良品盒")
    private String defectiveBoxByQC;
    @ApiModelProperty("物料容器")
    private String materialContainerByQC;
    @ApiModelProperty("标识符")
    private String tagByQC;
    @ApiModelProperty("生产治具")
    private String manufacturingFixtureByQC;
    @ApiModelProperty("线外治具")
    private String offLineFixtureByQC;
    @ApiModelProperty("线外工站站")
    private String offLineWorkStationByQC;
    @ApiModelProperty("不良品维修区")
    private String badMaintainByQC;
    @ApiModelProperty("工程图面/文件")
    private String projectDrawingFileByQC;
    @ApiModelProperty("辅助材料")
    private String auxiliaryMaterialsByQC;
    @ApiModelProperty("物料暂存区")
    private String materialStagingAreaByQC;
    @ApiModelProperty("产品包装")
    private String productPackaging;
    @ApiModelProperty("产品展示")
    private String productDisplayByQC;
}
